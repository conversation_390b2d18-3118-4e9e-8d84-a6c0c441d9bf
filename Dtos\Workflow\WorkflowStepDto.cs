using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Represents a step within a workflow
    /// </summary>
    [Mappable(nameof(WorkflowStepId))]
    public class WorkflowStepDto
    {
        /// <summary>
        /// Id that identifies a step within a workflow.
        /// </summary>
        public int WorkflowStepId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// </summary>
        public int WorkflowId { get; set; }
        
        /// <summary>
        /// Defines the type of each step (how each step behaves):
        /// 1 - Execute Via Queue
        /// 2 - Notification Event
        /// 3 - Condition
        /// 4 - Wait for Dependencies
        /// </summary>
        public short WorkflowStepTypeId { get; set; }
        
        /// <summary>
        /// For simple sequential workflows this controls the order steps are executed sequentially in.
        /// Steps are process in ascending order.
        /// For complex workflows with conditional paths the WorkflowStepConnector table controls the order steps are executed in.
        /// </summary>
        public short? SortOrder { get; set; }
        
        /// <summary>
        /// The name of the workflow step
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// The Azure Storage Queue Name. In order to execute the step a message will be placed in this queue.
        /// </summary>
        public string? QueueName { get; set; }
        
        /// <summary>
        /// To pass a message off to the Notification Engine this Event Type Code must be set.
        /// </summary>
        public string? NotificationEventTypeCode { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowStep
    /// </summary>
    public class WorkflowStepListDto : WorkflowStepDto
    {
    }
}
