using RediAzurefunctionBase.Dtos.Base;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// DTO for WorkflowStep
    /// </summary>
    public class WorkflowStepDto : DtoBase
    {
        /// <summary>
        /// Primary key for the workflow step
        /// </summary>
        public int WorkflowStepId { get; set; }

        /// <summary>
        /// The workflow this step belongs to
        /// </summary>
        public int WorkflowId { get; set; }

        /// <summary>
        /// Name of the workflow step
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Type of workflow step (Execute, Condition, Branch, etc.)
        /// </summary>
        public int WorkflowStepTypeId { get; set; }

        /// <summary>
        /// Queue name for execution steps
        /// </summary>
        public string? QueueName { get; set; }

        /// <summary>
        /// Condition ID for conditional steps
        /// </summary>
        public int? WorkflowConditionId { get; set; }

        /// <summary>
        /// Sort order for step execution
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// Whether the step is enabled
        /// </summary>
        public bool IsEnabled { get; set; }
    }
}
