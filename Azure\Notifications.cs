using System.Text;
using System.Text.Json;
using Azure.Storage.Queues;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Azure
{
    /// <summary>
    /// The Notifications class sends messages to the notification-event queue so that any form of notification 
    /// can be configured it. The Notification system will apply rules to send email/sms/mobile notification/in-app/message
    /// </summary>
    public class Notifications : AzureQueueBase
    {
        private readonly ILogger _logger;
        private readonly UtilityFunctions _utilityFunctions;

        public Notifications(IAzureClientFactory<QueueServiceClient> notificationQueueServiceClient, ILogger<Notifications> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            if (string.IsNullOrEmpty(Config.AzureStorageNotificationAccountName))
            {
                throw new Exception("Notifications has not been properly configured for use by this library. The config setting AzureStorageNotificationAccountName must be set");
            }
            _queueServiceClient = notificationQueueServiceClient.CreateClient("Notification");
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Send a notification event to the notification queue
        /// </summary>
        /// <param name="eventTypeCode">The type of notification event</param>
        /// <param name="recipientEmail">The email address of the recipient</param>
        /// <param name="subject">The subject of the notification</param>
        /// <param name="message">The message content</param>
        /// <param name="additionalData">Optional additional data</param>
        /// <param name="priority">Priority level (1=High, 2=Normal, 3=Low)</param>
        /// <returns>Task</returns>
        public async Task SendNotificationEvent(string eventTypeCode, string recipientEmail, string subject, string message, object? additionalData = null, int priority = 2)
        {
            var notificationEvent = new
            {
                EventTypeCode = eventTypeCode,
                RecipientEmail = recipientEmail,
                Subject = subject,
                Message = message,
                AdditionalData = additionalData,
                Priority = priority,
                TenantId = _utilityFunctions.TenantId,
                CreatedAt = DateTimeOffset.UtcNow,
                CreatedBy = _utilityFunctions.UserFullName
            };

            try
            {
                _logger.LogInformation($"Sending notification event: {eventTypeCode} to {recipientEmail}");
                await InsertIntoQueue(notificationEvent, "notification-event", autoCreateQueue: true);
                _logger.LogDebug($"Successfully sent notification event: {eventTypeCode}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send notification event: {eventTypeCode}");
                throw;
            }
        }

        /// <summary>
        /// Send a workflow completion notification
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="workflowName">The name of the workflow</param>
        /// <param name="recipientEmail">The email address of the recipient</param>
        /// <param name="isSuccess">Whether the workflow completed successfully</param>
        /// <param name="errorMessage">Error message if the workflow failed</param>
        /// <returns>Task</returns>
        public async Task SendWorkflowCompletionNotification(long wfJobId, string workflowName, string recipientEmail, bool isSuccess, string? errorMessage = null)
        {
            var subject = isSuccess 
                ? $"Workflow Completed Successfully: {workflowName}"
                : $"Workflow Failed: {workflowName}";

            var message = isSuccess
                ? $"The workflow '{workflowName}' (Job ID: {wfJobId}) has completed successfully."
                : $"The workflow '{workflowName}' (Job ID: {wfJobId}) has failed. Error: {errorMessage}";

            var additionalData = new
            {
                WfJobId = wfJobId,
                WorkflowName = workflowName,
                IsSuccess = isSuccess,
                ErrorMessage = errorMessage,
                CompletedAt = DateTimeOffset.UtcNow
            };

            await SendNotificationEvent("WORKFLOW_COMPLETION", recipientEmail, subject, message, additionalData, isSuccess ? 2 : 1);
        }

        /// <summary>
        /// Send a workflow step notification
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="workflowStepId">The workflow step ID</param>
        /// <param name="stepName">The name of the step</param>
        /// <param name="recipientEmail">The email address of the recipient</param>
        /// <param name="stepStatus">The status of the step</param>
        /// <param name="message">Additional message</param>
        /// <returns>Task</returns>
        public async Task SendWorkflowStepNotification(long wfJobId, int workflowStepId, string stepName, string recipientEmail, string stepStatus, string? message = null)
        {
            var subject = $"Workflow Step {stepStatus}: {stepName}";
            var notificationMessage = message ?? $"The workflow step '{stepName}' (Step ID: {workflowStepId}) is now {stepStatus}.";

            var additionalData = new
            {
                WfJobId = wfJobId,
                WorkflowStepId = workflowStepId,
                StepName = stepName,
                StepStatus = stepStatus,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            await SendNotificationEvent("WORKFLOW_STEP_UPDATE", recipientEmail, subject, notificationMessage, additionalData);
        }

        /// <summary>
        /// Send a dependency waiting notification
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="dependencyKey">The dependency key being waited for</param>
        /// <param name="recipientEmail">The email address of the recipient</param>
        /// <param name="timeoutAt">When the dependency wait will timeout</param>
        /// <returns>Task</returns>
        public async Task SendDependencyWaitingNotification(long wfJobId, string dependencyKey, string recipientEmail, DateTimeOffset? timeoutAt = null)
        {
            var subject = $"Workflow Waiting for Dependency: {dependencyKey}";
            var message = $"Workflow job {wfJobId} is waiting for dependency '{dependencyKey}' to be completed.";

            if (timeoutAt.HasValue)
            {
                message += $" This will timeout at {timeoutAt.Value:yyyy-MM-dd HH:mm:ss} UTC.";
            }

            var additionalData = new
            {
                WfJobId = wfJobId,
                DependencyKey = dependencyKey,
                TimeoutAt = timeoutAt,
                WaitingStartedAt = DateTimeOffset.UtcNow
            };

            await SendNotificationEvent("WORKFLOW_DEPENDENCY_WAITING", recipientEmail, subject, message, additionalData);
        }

        /// <summary>
        /// Send a custom workflow notification
        /// </summary>
        /// <param name="eventTypeCode">The custom event type code</param>
        /// <param name="recipientEmail">The email address of the recipient</param>
        /// <param name="subject">The subject of the notification</param>
        /// <param name="message">The message content</param>
        /// <param name="workflowData">Workflow-related data</param>
        /// <param name="priority">Priority level (1=High, 2=Normal, 3=Low)</param>
        /// <returns>Task</returns>
        public async Task SendCustomWorkflowNotification(string eventTypeCode, string recipientEmail, string subject, string message, object? workflowData = null, int priority = 2)
        {
            var additionalData = new
            {
                WorkflowData = workflowData,
                CustomEventType = eventTypeCode,
                SentAt = DateTimeOffset.UtcNow
            };

            await SendNotificationEvent(eventTypeCode, recipientEmail, subject, message, additionalData, priority);
        }

        /// <summary>
        /// Test the notification connection by sending a test message
        /// </summary>
        /// <returns>Task</returns>
        public new async Task TestConnection()
        {
            try
            {
                var testMessage = new
                {
                    EventTypeCode = "TEST_CONNECTION",
                    Message = "Test notification from Workflow Coordinator Library",
                    CreatedAt = DateTimeOffset.UtcNow,
                    TestId = Guid.NewGuid()
                };

                await InsertIntoQueue(testMessage, "notification-event", autoCreateQueue: true);
                _logger.LogInformation("Successfully tested notification connection");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to test notification connection");
                throw new Exception($"Notification Connection failed. Error: {ex.Message}");
            }
        }
    }
}
