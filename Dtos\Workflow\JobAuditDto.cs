using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Audit trail for workflow job execution steps.
    /// The Information field is user readable text about the step
    /// </summary>
    [Mappable(nameof(WfJobAuditId))]
    public class JobAuditDto : TenantDtoBase
    {
        /// <summary>
        /// The unique identifier for the workflow job audit record
        /// </summary>
        public long WfJobAuditId { get; set; }
        
        /// <summary>
        /// The WfJobId this Audit record is for.
        /// </summary>
        public long WfJobId { get; set; }
        
        /// <summary>
        /// Id that identifies a step within a workflow.
        /// Optional - only populated if job is related to a workflow with steps
        /// </summary>
        public int? WorkflowStepId { get; set; }
        
        /// <summary>
        /// The QueueName associated with a WfJob step that has no associated workflow (is just a series of chained queue's).
        /// </summary>
        public string? QueueName { get; set; }
        
        /// <summary>
        /// The time when this job step was queued for processing.
        /// </summary>
        public DateTimeOffset QueuedTime { get; set; }
        
        /// <summary>
        /// When a job step started executing. If the step has not started executing then this will be null.
        /// </summary>
        public DateTimeOffset? StartTime { get; set; }
        
        /// <summary>
        /// When a job step ended. If the job step has not completed then this will be null.
        /// </summary>
        public DateTimeOffset? EndTime { get; set; }
        
        /// <summary>
        /// Human readable text about the step. This can be anything that may be useful for a user to read.
        /// </summary>
        public string? Information { get; set; }
    }

    /// <summary>
    /// List DTO for JobAudit
    /// </summary>
    public class JobAuditListDto : JobAuditDto
    {
    }
}
