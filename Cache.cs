﻿using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// A wrapper to handle Caching within Prime3.
    /// Makes use of the .NET Hybrid Cache.
    /// Supports auto removing items from cache based on objectName's timestamp
    /// </summary>
    public class Cache
    {
        protected HybridCache _cache;
        protected Dictionary<string, long> _cacheObjects = new Dictionary<string, long>();

        public Cache(HybridCache cache)
        {
            _cache = cache;
        }

        /// <summary>
        /// Get item from cache if it exists
        /// </summary>
        /// <typeparam name="T">The Item Type to Cache</typeparam>
        /// <param name="objectName">Cache Object Name - Party, Listing, Setting - matches the main entity</param>
        /// <param name="cacheKey">Cache key to be used to lookup the value from the cache</param>
        /// <param name="bypassCache">If true, the cache will be bypassed and the value will be retrieved from the source</param>
        /// <param name="ct">Cancellation Token</param>
        /// <returns>(exists, value)</returns>
        public async ValueTask<(bool Exists, T Value)> GetCacheItem<T>(string objectName, string cacheKey, bool bypassCache = false, CancellationToken ct = default)
        {
            if (string.IsNullOrEmpty(objectName)) { objectName = "default"; }
            if (string.IsNullOrEmpty(cacheKey)) { throw new ArgumentException("cacheKey must have a non-null value"); }
            if (bypassCache == true) { return (false, default(T)!); }

            if (_cacheObjects.ContainsKey(objectName) == false) { _cacheObjects.Add(objectName, DateTime.UtcNow.Ticks); }
            var dt = _cacheObjects[objectName];

            string cacheLookupKey = objectName + cacheKey + dt;

            bool exists = true;
            var value = await _cache.GetOrCreateAsync<T>(
                cacheLookupKey,
                _ => 
                {
                    exists = false;
                    return new ValueTask<T>(default(T)!);
                },
                new HybridCacheEntryOptions { Flags = HybridCacheEntryFlags.DisableUnderlyingData | HybridCacheEntryFlags.DisableLocalCacheWrite | HybridCacheEntryFlags.DisableDistributedCacheWrite },
                cancellationToken: ct
                );

            return (exists, value);
        }

        /// <summary>
        /// Clear the cache for a specific object 
        /// This will have the effect that any cache item that has this objectName will be invalidated (no longer used)
        /// </summary>
        /// <param name="objectName">Cache Object Name - Party, Listing, Setting - matches the main entity</param>
        public void ClearCacheForObject(string objectName)
        {
            if (string.IsNullOrEmpty(objectName)) { return; }
            // To clear the cache for an object we simply update the timestamp associated with the object in _cacheObjects
            if (_cacheObjects.ContainsKey(objectName) == false) { _cacheObjects.Add(objectName, DateTime.UtcNow.Ticks); }
            _cacheObjects[objectName] = DateTime.UtcNow.Ticks;
        }

        /// <summary>
        /// Save item to cache
        /// </summary>
        /// <typeparam name="T">The Item Type to Cache</typeparam>
        /// <param name="objectName">Cache Object Name - Party, Listing, Setting - matches the main entity</param>
        /// <param name="cacheKey">Cache key to be used to lookup the value from the cache</param>
        /// <param name="value">The value to be cached</param>
        /// <param name="deepClone">Default true. Deep clones item</param>
        /// <param name="cacheForMinutes">Default 15 minutes</param>
        /// <param name="ct">Cancellation Token</param>
        public async ValueTask SetCacheItem<T>(string objectName, string cacheKey, T value, bool deepClone = true, double cacheForMinutes = 15, CancellationToken ct = default)
        {
            if (string.IsNullOrEmpty(objectName)) { objectName = "default"; }
            if (string.IsNullOrEmpty(cacheKey)) { throw new ArgumentException("cacheKey must have a non-null value"); }
            if (_cacheObjects.ContainsKey(objectName) == false) { _cacheObjects.Add(objectName, DateTime.UtcNow.Ticks); }
            _cacheObjects[objectName] = DateTime.UtcNow.Ticks;
            var dt = _cacheObjects[objectName];

            string cacheLookupKey = objectName + cacheKey + dt;

            if (deepClone && !ReferenceEquals(value, null) )
            {
                var cloneVal = JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(value));
                await _cache.SetAsync<T>(cacheLookupKey, cloneVal!, new HybridCacheEntryOptions() { LocalCacheExpiration = TimeSpan.FromMinutes(cacheForMinutes), Expiration = TimeSpan.FromMinutes(cacheForMinutes) }, cancellationToken: ct);
            }
            else
            {
                await _cache.SetAsync<T>(cacheLookupKey, value, new HybridCacheEntryOptions() { LocalCacheExpiration = TimeSpan.FromMinutes(cacheForMinutes), Expiration = TimeSpan.FromMinutes(cacheForMinutes) }, cancellationToken: ct);
            }
        }

        /// <summary>
        /// Remove Item from Cache.
        /// </summary>
        /// <param name="objectName">Cache Object Name - Party, Listing, Setting - matches the main entity</param>
        /// <param name="cacheKey">Cache key to be used to lookup the value from the cache</param>
        /// <param name="ct">Cancellation Token</param>
        /// <returns></returns>
        public async ValueTask RemoveCacheItem(string objectName, string cacheKey, CancellationToken ct = default)
        {
            if (string.IsNullOrEmpty(objectName)) { objectName = "default"; }
            if (string.IsNullOrEmpty(cacheKey)) { throw new ArgumentException("cacheKey must have a non-null value"); }

            if (_cacheObjects.ContainsKey(objectName) == false) { return; }
            var dt = _cacheObjects[objectName];

            string cacheLookupKey = objectName + cacheKey + dt;

            await _cache.RemoveAsync(cacheLookupKey, ct);
        }

    }
}
