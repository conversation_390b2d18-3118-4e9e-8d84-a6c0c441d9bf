using Microsoft.Extensions.Logging;
using RediAzurefunctionBase.Dtos;
using RediAzurefunctionBase.Azure;

namespace RediAzurefunctionBase.BusinessLogic
{
    /// <summary>
    /// Processor for handling job dependencies
    /// </summary>
    public class JobDependencyProcessor
    {
        private readonly ILogger<JobDependencyProcessor> _logger;
        private readonly DefaultAzureQueue _azureQueue;
        private readonly JobDependency _jobDependency;
        private readonly Job _job;
        private readonly JobAudit _jobAudit;
        private readonly SchedulerProcessor _schedulerProcessor;

        public JobDependencyProcessor(ILogger<JobDependencyProcessor> logger, DefaultAzureQueue azureQueue, JobDependency jobDependency, Job job, JobAudit jobAudit, SchedulerProcessor schedulerProcessor)
        {
            _logger = logger;
            _azureQueue = azureQueue;
            _jobDependency = jobDependency;
            _job = job;
            _jobAudit = jobAudit;
            _schedulerProcessor = schedulerProcessor;
        }

        /// <summary>
        /// Main execution method for dependency processing
        /// </summary>
        /// <returns>Task</returns>
        public async Task ExecuteAsync()
        {
            _logger.LogInformation("Starting job dependency processing");

            try
            {
                // TODO: Implement main dependency processing logic
                
                _logger.LogInformation("Job dependency processing completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during job dependency processing");
                throw;
            }
        }

        /// <summary>
        /// Resolve completed dependencies for jobs
        /// Note: Will be used by FunctionJobDependencyCheck.cs in future written up code
        /// </summary>
        /// <returns>Task</returns>
        public async Task ResolveCompletedDependenciesAsync(long wfJobId)
        {
            _logger.LogDebug($"Resolving completed dependencies for job: {wfJobId}");

            try
            {
                // Get all completed dependencies for this job
                var completedDependenciesResult = await _jobDependency.GetListAsync(
                    standardListParameters: null,
                    wfJobId: wfJobId,
                    dependencyKey: null,
                    isCompleted: true
                );

                if (completedDependenciesResult.List != null && completedDependenciesResult.List.Any())
                {
                    _logger.LogInformation($"Found {completedDependenciesResult.List.Count} completed dependencies for job {wfJobId}");

                    foreach (var completedDependency in completedDependenciesResult.List)
                    {
                        var job = await _job.GetWfJobDetailsAsync(completedDependency.WfJobId);

                        // Create WfJobAudit record
                        var jobAuditDto = new WfJobAuditDto
                        {
                            WfJobId = completedDependency.WfJobId,
                            WorkflowStepId = null, // Will be set based on job context
                            StartTime = DateTimeOffset.UtcNow,
                            EndTime = DateTimeOffset.UtcNow,
                            Information = $"Dependency completed: {completedDependency.DependencyKey}",
                            QueueName = "dependency-resolved",
                            TenantId = null // Will be set based on job context
                        };

                        // Create the audit record
                        var auditId = await _jobAudit.CreateAsync(jobAuditDto);
                        _logger.LogDebug($"Created job audit record {auditId} for dependency {completedDependency.DependencyKey}");

                        // Hard delete the dependency record
                        await _jobDependency.DeleteJobDependencyAsync(completedDependency.WfJobDependencyId);
                        _logger.LogDebug($"Deleted dependency record for WfJobDependencyId {completedDependency.WfJobDependencyId}");
                    }

                    _logger.LogInformation($"Successfully processed {completedDependenciesResult.List.Count} completed dependencies for job {wfJobId}");
                }
                else
                {
                    _logger.LogDebug($"No completed dependencies found for job {wfJobId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error resolving completed dependencies for job {wfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Resolve dependency met by removing record from JobDependency table and adding record to JobAudit table
        /// Note: Logic to be about removing record from JobDependency table and adding record to JobAudit table 
        /// if any of the records in DependencyComplete table
        /// </summary>
        /// <param name="dependencyKey">The dependency key that was met</param>
        /// <returns>Task</returns>
        public async Task ScheduleDependencyCheckForPendingJobsAsync(string dependencyKey)
        {
            _logger.LogDebug($"Resolving dependency met: {dependencyKey}");

            try
            {
                // Get all job dependencies that match the dependency key
                var jobDependenciesResult = await _jobDependency.GetListAsync(
                    standardListParameters: null,
                    wfJobId: null,
                    dependencyKey: dependencyKey,
                    isCompleted: true
                );

                if (jobDependenciesResult.List != null && jobDependenciesResult.List.Any())
                {
                    _logger.LogInformation($"Found {jobDependenciesResult.List.Count} job dependencies for key: {dependencyKey}");

                    // Loop through each unique job ID and get the complete job information
                    foreach (var jobDependency in jobDependenciesResult.List)
                    {
                        var job = new WfJobDto { WfJobId = jobDependency.WfJobId };
                        // Schedule dependency check for this job
                        await _schedulerProcessor.ScheduleJobDependencyCheckAsync(job);

                        _logger.LogDebug($"Scheduled dependency check for job {job.WfJobId} due to dependency key: {dependencyKey}");
                    }
                }
                else
                {
                    _logger.LogInformation($"No job dependencies found for key: {dependencyKey}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error resolving dependency met for key: {dependencyKey}");
                throw;
            }
        }

        /// <summary>
        /// Check if the job has any pending dependencies in the WfJobDependency table
        /// </summary>
        /// <param name="wfJobId">The workflow job ID to check</param>
        /// <returns>True if pending dependencies exist, false otherwise</returns>
        public async Task<bool> CheckPendingDependenciesExistAsync(long wfJobId)
        {
            _logger.LogDebug($"Checking pending dependencies for job: {wfJobId}");

            try
            {
                // TODO: Implement logic to check if the job has any records in the WfJobDependency table
                // Return true if dependencies exist, false if no dependencies
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking pending dependencies for job: {wfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Handle job schedule transition based on job status and conditions
        /// Note: Logic to be about checking if the Job is in Scheduled or Delayed state, and to check if the job 
        /// has a ConditionId to then write to wf-condition-evaluator queue and set the job status to now CheckConditions, 
        /// else write to the next Queue and set status to Executing. OR if the job had Waiting status, 
        /// to write to the next queue and set status to Executing.
        /// </summary>
        /// <param name="queueMessageDto">The queue message containing job information</param>
        /// <returns>Task</returns>
        public async Task HandleJobScheduleTransitionAsync(WorkflowQueueDto queueMessageDto)
        {
            _logger.LogDebug($"Handling job schedule transition for job: {queueMessageDto.WfJobId}");

            try
            {
                // TODO: Implement logic to:
                // 1. Check if job is in Scheduled (1) or Delayed (2) state
                //    - If job has ConditionId: write to 'wf-condition-evaluator' queue and set status to CheckConditions (3)
                //    - Else: write to next queue and set status to Executing (4)
                // 2. If job had Waiting (5) status: write to next queue and set status to Executing (4)
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling job schedule transition for job: {queueMessageDto.WfJobId}");
                throw;
            }
        }
    }
}
