using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class Report : BusinessLogicBase
    {
        public Report(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single Report by ID
        /// </summary>
        /// <param name="reportId">The report ID</param>
        /// <returns>ReportDto or null if not found</returns>
        internal async Task<ReportDto?> GetAsync(int reportId)
        {
            string sql = @"
                SELECT [ReportId]
                      ,[TenantId]
                      ,[Title]
                      ,[ReportTypeId]
                      ,[IsEnabled]
                      ,[ScheduleId]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[Report]
                WHERE [ReportId] = @reportId
                  AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("reportId", reportId);
                return await command.SelectSingle<ReportDto>();
            }
        }

        /// <summary>
        /// Create a new Report
        /// </summary>
        /// <param name="dto">The Report data</param>
        internal async Task CreateAsync(ReportDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Title))
            {
                throw new HttpRequestException("Title cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ReportTypeId <= 0)
            {
                throw new HttpRequestException("ReportTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate ReportType exists
            var reportType = new ReportType(_unitOfWork);
            await reportType.GetAsync(dto.ReportTypeId);

            // Validate optional foreign keys
            if (dto.ScheduleId.HasValue)
            {
                var schedule = new Schedule(_unitOfWork);
                await schedule.GetAsync(dto.ScheduleId.Value);
            }

            string sql = @"
                INSERT INTO [workflow].[Report]
                ([TenantId], [Title], [ReportTypeId], [IsEnabled], [ScheduleId], [CreatedOn], [CreatedByName])
                VALUES
                (@TenantId, @Title, @ReportTypeId, @IsEnabled, @ScheduleId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing Report
        /// </summary>
        /// <param name="dto">The Report data</param>
        internal async Task UpdateAsync(ReportDto dto)
        {
            if (dto.ReportId <= 0)
            {
                throw new HttpRequestException("ReportId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Title))
            {
                throw new HttpRequestException("Title cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.ReportTypeId <= 0)
            {
                throw new HttpRequestException("ReportTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.ReportId);
            if (exists == null)
            {
                throw new HttpRequestException($"Report with ID '{dto.ReportId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            // Validate ReportType exists
            var reportType = new ReportType(_unitOfWork);
            await reportType.GetAsync(dto.ReportTypeId);

            // Validate optional foreign keys
            if (dto.ScheduleId.HasValue)
            {
                var schedule = new Schedule(_unitOfWork);
                await schedule.GetAsync(dto.ScheduleId.Value);
            }

            string sql = @"
                UPDATE [workflow].[Report]
                SET [TenantId] = @TenantId,
                    [Title] = @Title,
                    [ReportTypeId] = @ReportTypeId,
                    [IsEnabled] = @IsEnabled,
                    [ScheduleId] = @ScheduleId,
                    [ModifiedOn] = @ModifiedOn,
                    [ModifiedByName] = @ModifiedByName
                WHERE [ReportId] = @ReportId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a Report (soft delete)
        /// </summary>
        /// <param name="reportId">The report ID</param>
        internal async Task DeleteAsync(int reportId)
        {
            var exists = await GetAsync(reportId);
            if (exists == null)
            {
                throw new HttpRequestException($"Report with ID '{reportId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[Report]
                SET [Deleted] = 1,
                    [ModifiedOn] = @ModifiedOn
                WHERE [ReportId] = @reportId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("reportId", reportId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of Reports with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="reportTypeId">Filter by report type ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <param name="scheduleId">Filter by schedule ID</param>
        /// <returns>List of Reports</returns>
        internal async Task<ListResponseDto<ReportListDto>> GetListAsync(StandardListParameters standardListParameters, int? tenantId = null, short? reportTypeId = null, bool? isEnabled = null, int? scheduleId = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ReportId", "[Report].[ReportId]" },
                { "Title", "[Report].[Title]" },
                { "TenantId", "[Report].[TenantId]" },
                { "ReportTypeId", "[Report].[ReportTypeId]" },
                { "IsEnabled", "[Report].[IsEnabled]" },
                { "ScheduleId", "[Report].[ScheduleId]" },
                { "CreatedOn", "[Report].[CreatedOn]" },
                { "ModifiedOn", "[Report].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [ReportId]
                      ,[TenantId]
                      ,[Title]
                      ,[ReportTypeId]
                      ,[IsEnabled]
                      ,[ScheduleId]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[Report]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (reportTypeId.HasValue)
            {
                sql += " AND [ReportTypeId] = @reportTypeId";
            }

            if (isEnabled.HasValue)
            {
                sql += " AND [IsEnabled] = @isEnabled";
            }

            if (scheduleId.HasValue)
            {
                sql += " AND [ScheduleId] = @scheduleId";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "Title");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ReportListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (reportTypeId.HasValue)
                {
                    command.AddArgument("reportTypeId", reportTypeId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                result.List = await command.SelectMany<ReportListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[Report]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (reportTypeId.HasValue)
            {
                countSql += " AND [ReportTypeId] = @reportTypeId";
            }

            if (isEnabled.HasValue)
            {
                countSql += " AND [IsEnabled] = @isEnabled";
            }

            if (scheduleId.HasValue)
            {
                countSql += " AND [ScheduleId] = @scheduleId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (reportTypeId.HasValue)
                {
                    command.AddArgument("reportTypeId", reportTypeId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
