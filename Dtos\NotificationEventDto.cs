﻿namespace Redi.Prime3.MicroService.BaseLib
{
    public class NotificationEventDto
    {
        /// <summary>
        /// The Notification Event Code (see common.NotificationEventType)
        /// </summary>
        public string? EventCode { get; set; }
        /// <summary>
        /// Identifies the tenant that the event was related to.
        /// </summary>
        public int? TenantId { get; set; }
        public Guid? ParentPartyId { get; set; }
        public Guid? UserId { get; set; }
        /// <summary>
        /// The CRM Party Id of the user who has been assigned to a record (ie. assigned to a Job, assigned to a Task).
        /// </summary>
        public Guid? AssigneePartyId { get; set; }
        /// <summary>
        /// The CRM Manager Party Id that is associated with an event. The manager may be the owner of a record 
        /// (eg. the user who created a task, or the user who has ownership of an order).
        /// </summary>
        public Guid? ManagerPartyId { get; set; }
        /// <summary>
        /// The CRM Customer Party Id that is associated with the event (ie. for an Order this is the Customer associated with the order)
        /// </summary>
        public Guid? CustomerPartyId { get; set; }
        /// <summary>
        /// Dictionary of fields and values (<string, object>) that can be used in the sent message/notification.
        /// </summary>
        public Dictionary<string, object?>? MetaData { get; set; }
    }
}
