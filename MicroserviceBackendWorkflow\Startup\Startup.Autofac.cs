using Autofac;
using Dapr.Client;
using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Constants;
using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendWorkflow.Startup
{
    public partial class Startup
    {
        public IContainer ApplicationContainer { get; private set; }

        public void ConfigureContainer(ContainerBuilder builder)
        {
            AddCustomServices(builder);
        }

        private static void AddCustomServices(ContainerBuilder builder)
        {
            builder.Register(c =>
            {
                var logger = c.Resolve<ILogger>();
                var utils = c.Resolve<UtilityFunctions>();
                return new DaprCommonServiceClient(DaprClient.CreateInvokeHttpClient(MicroserviceConstants.CommonAppId), logger, utils);
            }).As<DaprCommonServiceClient>().SingleInstance();
        }
    }
}
