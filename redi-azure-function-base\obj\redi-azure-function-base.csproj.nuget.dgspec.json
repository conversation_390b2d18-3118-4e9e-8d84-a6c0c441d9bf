{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\redi-azure-function-base\\redi-azure-function-base.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/api/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Sentry.AspNetCore": {"target": "Package", "version": "[3.36.0, )"}, "System.Data.Common": {"target": "Package", "version": "[4.3.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "autofac": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\redi-azure-function-base\\redi-azure-function-base.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\redi-azure-function-base\\redi-azure-function-base.csproj", "projectName": "redi-azure-function-base", "projectPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\redi-azure-function-base\\redi-azure-function-base.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\redi-azure-function-base\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/api/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Azure.Security.KeyVault.Secrets": {"target": "Package", "version": "[4.6.0, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.19.1, )"}, "Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[1.20.1, )"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights": {"target": "Package", "version": "[1.1.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Storage.Queues": {"target": "Package", "version": "[5.2.0, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[1.16.4, )"}, "Microsoft.SqlServer.Types": {"target": "Package", "version": "[160.1000.6, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Redi.Sql": {"target": "Package", "version": "[8.0.3, )"}, "Sentry.Azure.Functions.Worker": {"target": "Package", "version": "[4.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200/PortableRuntimeIdentifierGraph.json"}}}}}