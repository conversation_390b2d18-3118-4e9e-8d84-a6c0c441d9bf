﻿
namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Update or Create API request base structure
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class RequestCDto<T>
    {
        /// <summary>
        /// The Data Model for the request
        /// </summary>
        public T? Data { get; set; }
        /// <summary>
        /// For an update request ClearFields contains the fields that are to be cleared deleted.
        /// Empty fields in the Data Model will not be updated unless included in the ClearFields
        /// </summary>
        public Dictionary<string, object>? ClearFields { get; set; }
        /// <summary>
        /// Link Tenant to Requesting User. Allowing for Organisations created by a User to be immediately linked to the Tenant created in the process.
        /// </summary>
        public bool? LinkRequesterToTenant { get; set; }
    }
}
