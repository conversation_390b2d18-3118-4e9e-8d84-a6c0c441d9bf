using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for recur on operations
    /// </summary>
    public class RecurOn : BusinessLogicBase
    {
        public RecurOn(IUnitOfWork unitOfWork, ILogger<RecurOn> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single RecurOn by ID
        /// </summary>
        /// <param name="recurOnId">The recur on ID</param>
        /// <param name="ignoreErrorIfNotExists">If true, returns null when not found instead of throwing an exception</param>
        /// <returns>RecurOnDto or null if not found</returns>
        public async Task<RecurOnDto?> GetAsync(byte recurOnId, bool ignoreErrorIfNotExists = false)
        {
            _logger.LogDebug($"Getting recur on for ID: {recurOnId}");

            try
            {
                // Check cache first
                var cacheKey = $"RecurOn_{recurOnId}";
                var (exists, cachedRecurOn) = await _cache.GetCacheItem<RecurOnDto>("RecurOn", cacheKey);
                if (exists && cachedRecurOn != null)
                {
                    return cachedRecurOn;
                }

                string sql = @"
                    SELECT [RecurOnId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[RecurOn]
                    WHERE [RecurOnId] = @recurOnId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("recurOnId", recurOnId);
                    var recurOn = await command.SelectSingle<RecurOnDto>();

                    // Cache the result if found
                    if (recurOn != null)
                    {
                        await _cache.SetCacheItem("RecurOn", cacheKey, recurOn, cacheForMinutes: 30);
                    }
                    else if (!ignoreErrorIfNotExists)
                    {
                        throw new ArgumentException($"RecurOn with ID {recurOnId} not found");
                    }

                    return recurOn;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get recur on for ID: {recurOnId}");
                throw;
            }
        }
    }
}
