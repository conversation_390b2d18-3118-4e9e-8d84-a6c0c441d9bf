using Autofac;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow;
using Redi.Prime3.WorkflowCoordinator.Lib.Azure;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Redi.Prime3.Logger.Module;
using Sql.Module;
using System.Reflection;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Module
{
    /// <summary>
    /// Autofac module for Workflow Coordinator Library
    /// Registers all necessary dependencies including SQL module, logger, and business logic classes
    /// </summary>
    public class WorkflowCoordinatorModule : Autofac.Module
    {
        private readonly IConfiguration _configuration;
        private readonly string _appId;

        public WorkflowCoordinatorModule(IConfiguration configuration,string appId = "WorkflowCoordinator")
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _appId = appId;
        }

        protected override void Load(ContainerBuilder builder)
        {
            // Initialize Config if not already done
            Config.Initialise(_configuration);

            // Register SQL Module with profile name
            var connectionString = Config.DatabaseConnectionString;
            if (!string.IsNullOrEmpty(connectionString))
            {
                var sqlModule = SqlModule.Create(connectionString, "redi-prime3-workflow-coordinator");
                builder.RegisterModule(sqlModule);
            }
//TODO: https://autofac.readthedocs.io/en/latest/best-practices/index.html#register-frequently-used-components-with-lambdas
            // Register core services - use PreserveExistingDefaults() to avoid conflicts
            //builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().InstancePerLifetimeScope().PreserveExistingDefaults();
            builder.RegisterType<UtilityFunctions>().InstancePerLifetimeScope().PreserveExistingDefaults();
            builder.RegisterType<Cache>().InstancePerLifetimeScope().PreserveExistingDefaults();
            builder.RegisterType<MemoryCache>().As<IMemoryCache>().SingleInstance().PreserveExistingDefaults();

            // Register business logic classes from this assembly
            var currentAssembly = Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(currentAssembly).Where(
                t => t.BaseType == typeof(BusinessLogicBase) ||
                     t.BaseType == typeof(AzureQueueBase)
            ).InstancePerLifetimeScope();

            // Register specific workflow classes
            // builder.RegisterType<WorkflowSchedule>().InstancePerLifetimeScope();
            // builder.RegisterType<WorkflowJob>().InstancePerLifetimeScope();
            // builder.RegisterType<WorkflowDependencyValidation>().InstancePerLifetimeScope();
            // builder.RegisterType<SystemSetting>().InstancePerLifetimeScope();
            // builder.RegisterType<JobStatus>().InstancePerLifetimeScope();
            // builder.RegisterType<WfJobCancelledReason>().InstancePerLifetimeScope();

            // Register Azure Queue classes
            builder.RegisterType<DefaultAzureQueue>().InstancePerLifetimeScope().PreserveExistingDefaults();
            builder.RegisterType<Notifications>().InstancePerLifetimeScope().PreserveExistingDefaults();


        }


    }
}
