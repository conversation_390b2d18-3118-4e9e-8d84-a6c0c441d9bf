using Autofac;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow;
using Sql.Module;
using System.Reflection;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Module
{
    /// <summary>
    /// Autofac module for Workflow Coordinator Library
    /// Registers all necessary dependencies including SQL module, logger, and business logic classes
    /// </summary>
    public class WorkflowCoordinatorModule : Autofac.Module
    {
        private readonly IConfiguration _configuration;
        private readonly ILoggerProvider? _loggerProvider;
        private readonly string _appId;

        public WorkflowCoordinatorModule(IConfiguration configuration, ILoggerProvider? loggerProvider = null, string appId = "WorkflowCoordinator")
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _loggerProvider = loggerProvider;
            _appId = appId;
        }

        protected override void Load(ContainerBuilder builder)
        {
            // Initialize Config if not already done
            Config.Initialise(_configuration);

            // Register HttpContextAccessor if not already registered
            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(IHttpContextAccessor))))
            {
                builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().InstancePerLifetimeScope();
            }

            // Register UtilityFunctions if not already registered
            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(UtilityFunctions))))
            {
                builder.RegisterType<UtilityFunctions>().InstancePerLifetimeScope();
            }

            // Register Cache if not already registered
            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(Cache))))
            {
                builder.RegisterType<Cache>().InstancePerLifetimeScope();
            }

            // Register Memory Cache if not already registered
            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(IMemoryCache))))
            {
                builder.RegisterType<MemoryCache>().As<IMemoryCache>().SingleInstance();
            }

            // Register business logic classes from this assembly
            var currentAssembly = Assembly.GetExecutingAssembly();
            builder.RegisterAssemblyTypes(currentAssembly).Where(
                t => t.BaseType == typeof(BusinessLogicBase) ||
                     t.BaseType == typeof(AzureQueueBase) ||
                     t.BaseType == typeof(Modules.ModulesBase)
            ).InstancePerLifetimeScope();

            // Register specific workflow classes
            builder.RegisterType<WorkflowSchedule>().InstancePerLifetimeScope();
            builder.RegisterType<WorkflowJob>().InstancePerLifetimeScope();
            builder.RegisterType<WorkflowDependencyValidation>().InstancePerLifetimeScope();
            builder.RegisterType<SystemSetting>().InstancePerLifetimeScope();

            // Register Azure Queue classes if not already registered
            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(DefaultAzureQueue))))
            {
                builder.RegisterType<DefaultAzureQueue>().InstancePerLifetimeScope();
            }

            if (!builder.ComponentRegistryBuilder.IsRegistered(new TypedService(typeof(Notifications))))
            {
                builder.RegisterType<Notifications>().InstancePerLifetimeScope();
            }

            // Register Logger Module if logger provider is available and not already registered
            if (_loggerProvider != null)
            {
                try
                {
                    var loggerModule = new LoggerModule(_configuration, _loggerProvider, _appId);
                    builder.RegisterModule(loggerModule);
                }
                catch (Exception)
                {
                    // Logger module might already be registered, ignore the error
                }
            }

            // Register SQL Module with profile name
            var connectionString = Config.DatabaseConnectionString;
            if (!string.IsNullOrEmpty(connectionString))
            {
                var sqlModule = SqlModule.Create(connectionString, "redi-prime3-workflow-coordinator");
                builder.RegisterModule(sqlModule);
            }
        }
    }
}

namespace Redi.Prime3.WorkflowCoordinator.Lib.Modules
{
    /// <summary>
    /// Base class for Services.BusinessLogic.Modules used to add classes to the container
    /// </summary>
    public class ModulesBase
    {
    }
}
