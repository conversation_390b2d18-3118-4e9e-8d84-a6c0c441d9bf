using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendWorkflow.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get and Manage Schedule Definitions.
    /// </summary>
    [Route("api/Schedule")]
    public class ScheduleController : AppController
    {
        private readonly Schedule _schedule;

        public ScheduleController(Schedule schedule, IUnitOfWork unitOfWork)
        {
            _schedule = schedule;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Schedule
        /// </summary>
        /// <remarks>
        /// Returns a single Schedule record for a given Schedule ID.
        /// </remarks>
        /// <param name="scheduleId">The ID of a schedule record</param>
        /// <response code="200">Schedule returned, or null if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(ScheduleDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int scheduleId)
        {
            var result = await _schedule.GetAsync(scheduleId);
            return Ok(result);
        }

        /// <summary>
        /// Create a Schedule
        /// </summary>
        /// <remarks>
        /// Create a new Schedule definition with recurrence patterns
        /// </remarks>
        /// <param name="dto">A Schedule to be created</param>
        /// <response code="200">Schedule created</response>
        /// <response code="422">Schedule could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(ScheduleDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] ScheduleDto dto)
        {
            await _schedule.CreateAsync(dto);
            _unitOfWork.Commit();
            var result = await _schedule.GetAsync(dto.ScheduleId);
            return Ok(result);
        }

        /// <summary>
        /// Update a Schedule
        /// </summary>
        /// <remarks>
        /// Update an existing Schedule definition
        /// </remarks>
        /// <param name="dto">The Schedule data to update</param>
        /// <response code="200">Schedule updated</response>
        /// <response code="422">Schedule could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(ScheduleDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] ScheduleDto dto)
        {
            await _schedule.UpdateAsync(dto);
            _unitOfWork.Commit();
            var result = await _schedule.GetAsync(dto.ScheduleId);
            return Ok(result);
        }

        /// <summary>
        /// Delete a Schedule
        /// </summary>
        /// <remarks>
        /// Delete a Schedule with the given ID (soft delete).
        /// </remarks>
        /// <param name="scheduleId">The ID of a schedule record</param>
        /// <response code="200">Schedule has been deleted</response>
        /// <response code="404">Schedule not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] int scheduleId)
        {
            await _schedule.DeleteAsync(scheduleId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Schedules
        /// </summary>
        /// <remarks>
        /// Returns a list of Schedules with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <param name="parentEntityId">Filter by parent entity ID</param>
        /// <response code="200">List of Schedules returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<ScheduleListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? tenantId = null, int? workflowId = null, bool? isEnabled = null, Guid? parentEntityId = null)
        {
            var result = await _schedule.GetListAsync(standardListParameters, tenantId, workflowId, isEnabled, parentEntityId);
            return Ok(result);
        }
    }
}
