using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get Workflow Job Status lookup data.
    /// </summary>
    [Route("api/WfJobStatus")]
    public class WfJobStatusController : AppController
    {
        private readonly WfJobStatus _wfJobStatus;

        public WfJobStatusController(WfJobStatus wfJobStatus, IUnitOfWork unitOfWork)
        {
            _wfJobStatus = wfJobStatus;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Workflow Job Status
        /// </summary>
        /// <remarks>
        /// Returns a single Workflow Job Status record for a given ID.
        /// </remarks>
        /// <param name="wfJobStatusId">The ID of a workflow job status record</param>
        /// <response code="200">Workflow Job Status returned, or null if not found</response>
        /// <response code="404">Workflow Job Status not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(WfJobStatusDto), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(byte wfJobStatusId)
        {
            var result = await _wfJobStatus.GetAsync(wfJobStatusId);
            return Ok(result);
        }

        /// <summary>
        /// Get a List of Workflow Job Statuses
        /// </summary>
        /// <remarks>
        /// Returns a list of Workflow Job Statuses with filtering and paging options.
        /// Returns only enabled statuses by default.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <response code="200">List of Workflow Job Statuses returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<WfJobStatusListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            var result = await _wfJobStatus.GetListAsync(standardListParameters, showDisabled);
            return Ok(result);
        }
    }
}
