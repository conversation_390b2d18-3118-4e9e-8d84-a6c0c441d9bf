﻿using Azure.Storage.Queues;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Write to Queues on the Default Storage account
    /// </summary>
    public class DefaultAzureQueue : AzureQueueBase
    {
        private readonly ILogger _logger;

        public DefaultAzureQueue(IAzureClientFactory<QueueServiceClient> notificationQueueServiceClient, ILogger logger)
        {
            if (string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                throw new Exception("DefaultAzureQueue has not been properly configured for use by this Microservice. The config setting AzureStorageAccountName must be set");
            }
            _queueServiceClient = notificationQueueServiceClient.CreateClient("Default");
            _logger = logger;
        }

        /// <summary>
        /// Write a message to an Azure Storage Queue under the Default Storage Account.
        /// Queue will be created if it does not exist
        /// </summary>
        /// <param name="queueName">Azure Storage Queue Name</param>
        /// <param name="message">Message to write to queue</param>
        /// <returns></returns>
        public async Task WriteToQueue(string queueName, string message)
        {
            await InsertIntoQueue(message, queueName, autoCreateQueue: true);
        }

        /// <summary>
        /// Test the storage account connection by writing to a test queue.
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            try
            {
                var testQueueName = "ztest-def-" + StartupBase.AppId;
                if (testQueueName.Length >63 ) { testQueueName = testQueueName.Substring(0, 63); }
                await InsertIntoQueue("Test Message", testQueueName, autoCreateQueue: true);
            }
            catch (Exception ex)
            {
                throw new StartupException($"Default Storage Connection failed. {ex.Message}. AccountName:{ConfigBase.AzureStorageConnectionString}");
            }
        }
    }
}
