using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Stores data associated with a workflow job in JSON format.
    /// Max one record for a job. Not all jobs will have a record.
    /// </summary>
    [Mappable(nameof(WfJobId))]
    public class WfJobDataDto
    {
        /// <summary>
        /// The WfJob this Data record is for.
        /// </summary>
        public long WfJobId { get; set; }
        
        /// <summary>
        /// Dictionary style storage of data
        /// </summary>
        public string? Data { get; set; }
    }

    /// <summary>
    /// List DTO for WfJobData
    /// </summary>
    public class WfJobDataListDto : WfJobDataDto
    {
    }
}
