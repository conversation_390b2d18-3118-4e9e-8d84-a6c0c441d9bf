using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Defines a Workflow. Will contain one or more steps that are required to execute the workflow.
    /// </summary>
    [Mappable(nameof(WorkflowId))]
    public class WorkflowDto : TenantDtoBase
    {
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// </summary>
        public int WorkflowId { get; set; }
        
        /// <summary>
        /// The name of the workflow
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// The date and time when this workflow was last executed
        /// </summary>
        public DateTimeOffset? LastExecutedOn { get; set; }
        
        /// <summary>
        /// Indicates whether this workflow is enabled and can be executed
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// The first step in a Workflow.
        /// A workflow cannot be run if this is not set.
        /// </summary>
        public int? FirstWorkflowStepId { get; set; }
    }

    /// <summary>
    /// List DTO for Workflow
    /// </summary>
    public class WorkflowListDto : WorkflowDto
    {
    }
}
