﻿
namespace Redi.Prime3.MicroService.BaseLib
{
    public class ListResponseDto <T>
    {
        /// <summary>
        /// List of records
        /// </summary>
        public List<T>? List { get; set; }
        /// <summary>
        /// The Total Number of rows available based on the search/filter criteria
        /// This may be more than is returned in  the List
        /// </summary>
        public int TotalNumOfRows { get; set; } = 0;
    }
}
