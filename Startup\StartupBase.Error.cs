using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace Redi.Prime3.MicroService.BaseLib
{
	public partial class StartupBase
    {
        internal async Task HandleErrorMiddleware(HttpContext context, Func<Task> next)
        {
            using (var memoryResponse = new MemoryStream())
            {
                var originalResponse = context.Response.Body;
                context.Response.Body = memoryResponse;
                try
                {
                    await next.Invoke();
                    memoryResponse.Seek(0, SeekOrigin.Begin);
                    await memoryResponse.CopyToAsync(originalResponse);
                }
                catch (ApiErrorException exception)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message });
                    context.Response.ContentType = "application/json";
                    await originalResponse.WriteAsync(Encoding.UTF8.GetBytes(result));

                    string requestQuery = context.Request.Path.ToString() + "?" + context.Request.QueryString.ToString();
                    var logger = LoggerProviderRoot.CreateLogger(AppId);
                    logger.LogInformation(new EventId(0, requestQuery), exception.GetType().Name + " | " + exception.Message);
                }
                catch (HttpRequestException exception)
                {
                    context.Response.StatusCode = exception.StatusCode != null ? (int)exception.StatusCode : (int)HttpStatusCode.InternalServerError;
                    var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message });
                    context.Response.ContentType = "application/json";
                    await originalResponse.WriteAsync(Encoding.UTF8.GetBytes(result));

                    string requestQuery = context.Request.Path.ToString() + "?" + context.Request.QueryString.ToString();
                    var logger = LoggerProviderRoot.CreateLogger(AppId);
                    logger.LogError(new EventId(0, requestQuery), exception, exception?.GetType()?.Name);
                }
                catch (ArgumentException exception)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message + " | " + exception.ParamName });
                    context.Response.ContentType = "application/json";
                    await originalResponse.WriteAsync(Encoding.UTF8.GetBytes(result));

                    string requestQuery = context.Request.Path.ToString() + "?" + context.Request.QueryString.ToString();
                    var logger = LoggerProviderRoot.CreateLogger(AppId);
                    logger.LogInformation(new EventId(0, requestQuery), exception, exception.GetType().Name + " | " + exception.Message);
                }
                catch (Exception exception)
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    string result = "";
                    int errorResponseId = RandomNumberGenerator.GetInt32(99999999);
                    if (environmentRoot.IsDevelopment())
                    {
                        result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message, stackTrace = exception.StackTrace, innerException = exception.InnerException, source = exception.Source });
                    }
                    else
                    {
                        result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = $"Sorry we are currently unable to process your request (responseId: {errorResponseId})" });
                    }
                    context.Response.ContentType = "application/json";
                    await originalResponse.WriteAsync(Encoding.UTF8.GetBytes(result));

                    string requestQuery = context.Request.Path.ToString() + context.Request.QueryString.ToString();
                    var logger = LoggerProviderRoot.CreateLogger(AppId);
                    logger.LogError(new EventId(0, requestQuery), exception, exception?.GetType()?.Name + "  |ResponseId:" + errorResponseId);
                }
                finally
                {
                    context.Response.Body = originalResponse;
                }
            }
        }
    }
}