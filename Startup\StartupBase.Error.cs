using Microsoft.Extensions.Logging;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Microsoft.Azure.Functions.Worker.Http;
using System.Security.Cryptography;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Intercept Errors thrown by function.<para/>
    /// Handle errors based on function type (Http, Timer, Queue)
    /// </summary>
    internal sealed class HandleErrorMiddleware : IFunctionsWorkerMiddleware
    {
        private readonly ILogger _logger;

        public HandleErrorMiddleware(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Invoke(FunctionContext context, FunctionExecutionDelegate next)
        {
            (string triggerType, var scopedLoggingConext) = GetContextBasedOnTriggerType(context);
            var messageData = context.BindingContext.BindingData.ContainsKey(triggerType) ? context.BindingContext.BindingData[triggerType] : default; //Pull data from message

            // Wrap execution of function in Logging Scope so we always have context
            using (_logger.BeginScope(scopedLoggingConext))
            {
                try
                {
                    await next(context);
                }
                catch (ApiErrorException exception)
                {
                    if (triggerType == "httpTrigger")
                    {
                        // HTTP Request error - return 400 Bad Request
                        var request = await context.GetHttpRequestDataAsync();
                        var response = request!.CreateResponse();
                        response.StatusCode = System.Net.HttpStatusCode.BadRequest;

                        var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message });
                        response.Headers.Add("Content-Type","application/json");
                        await response.WriteStringAsync(result);

                        string requestQuery = triggerType + " | " + scopedLoggingConext["FunctionName"] + " | " + request?.Query?.ToString();
                        _logger.LogInformation(new EventId(0, requestQuery), exception.GetType().Name + " | " + exception.Message + " | Query:" + request?.Query?.ToString());
                    }
                    else
                    {
                        _logger.LogError(new EventId(0, triggerType + " | " + scopedLoggingConext["FunctionName"]), exception, exception?.GetType()?.Name + " msg:" + messageData);
                        throw;
                    }
                }
                catch (HttpRequestException exception)
                {
                    if (triggerType == "httpTrigger")
                    {
                        // HTTP Request error - return 500 Internal Server Error with associated error message
                        // See Log for Stack Trace not returned in response
                        var request = await context.GetHttpRequestDataAsync();
                        var response = request!.CreateResponse();
                        response.StatusCode = System.Net.HttpStatusCode.InternalServerError;

                        var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message });
                        response.Headers.Add("Content-Type","application/json");
                        await response.WriteStringAsync(result);

                        string requestQuery = triggerType + " | " + scopedLoggingConext["FunctionName"] + " | " + request?.Query?.ToString();
                        _logger.LogError(new EventId(0, requestQuery), exception, exception.GetType().Name + " | Query:" + request?.Query?.ToString());
                    }
                    else
                    {
                        _logger.LogError(new EventId(0, triggerType + " | " + scopedLoggingConext["FunctionName"]), exception, exception?.GetType()?.Name + " msg:" + messageData);
                        throw;
                    }
                }
                catch (ArgumentException exception)
                {
                    if (triggerType == "httpTrigger")
                    {
                        // HTTP Request error - return 500 Internal Server Error with associated error message and parameter 
                        // See Log for Stack Trace not returned in response
                        var request = await context.GetHttpRequestDataAsync();
                        var response = request!.CreateResponse();
                        response.StatusCode = System.Net.HttpStatusCode.InternalServerError;

                        var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = exception.Message + " | " + exception.ParamName });
                        response.Headers.Add("Content-Type","application/json");
                        await response.WriteStringAsync(result);

                        string requestQuery = triggerType + " | " + scopedLoggingConext["FunctionName"] + " | " + request?.Query?.ToString();
                        _logger.LogError(new EventId(0, requestQuery), exception, exception.GetType().Name + " | " + exception.ParamName + " | Query:" + request?.Query?.ToString());
                    }
                    else
                    {
                        _logger.LogError(new EventId(0, triggerType + " | " + scopedLoggingConext["FunctionName"]), exception, exception?.GetType()?.Name + " msg:" + messageData + " | " + exception?.ParamName);
                        throw;
                    }
                }
                catch (Exception exception)
                {
                    if (triggerType == "httpTrigger")
                    {
                        // HTTP Request error - return 500 Internal Server Error with Message 'Sorry we are currently unable to process your request (responseId: xxx)'
                        // See Log for Stack Trace not returned in response. Match based on ResponseId
                        var request = await context.GetHttpRequestDataAsync();
                        var response = request!.CreateResponse();
                        response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                        int errorResponseId = RandomNumberGenerator.GetInt32(99999999);

                        var result = JsonConvert.SerializeObject(new { type = exception.GetType().Name, message = $"Sorry we are currently unable to process your request (responseId: {errorResponseId})" });
                        response.Headers.Add("Content-Type","application/json");
                        await response.WriteStringAsync(result);

                        string requestQuery = triggerType + " | " + scopedLoggingConext["FunctionName"] + " | " + request?.Query?.ToString();
                        _logger.LogError(new EventId(0, requestQuery), exception, exception.GetType().Name + " | ResponseId:" + errorResponseId + " | Query:" + request?.Query?.ToString());
                    }
                    else
                    {
                        _logger.LogError(new EventId(0, triggerType + " | " + scopedLoggingConext["FunctionName"]), exception, exception?.GetType()?.Name + " msg:" + messageData);
                        throw; //Throw to retry Queue
                    }
                }
            }

        }
        private (string, Dictionary<string, object>) GetContextBasedOnTriggerType(FunctionContext context)
        {
            var triggerType = context.FunctionDefinition.InputBindings.Values.First(a => a.Type.EndsWith("Trigger")).Type;
            Dictionary<string, object> scopedLoggingConext = new Dictionary<string, object>
            {
                { "FunctionName", context.FunctionDefinition.Name }
            };

            switch (triggerType)
            {
                case "queueTrigger":
                    if (context.FunctionDefinition.Parameters[0]?.Properties["bindingAttribute"] != null)
                    {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        scopedLoggingConext.Add("QueueTriggerName", (context.FunctionDefinition.Parameters[0].Properties["bindingAttribute"] as QueueTriggerAttribute).QueueName );
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    }
                    break;
                case "blobTrigger":
                    break;
                case "httpTrigger":
                    break;
                case "timerTrigger":
                    if (context.FunctionDefinition.Parameters[0]?.Properties["bindingAttribute"] != null)
                    {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                        scopedLoggingConext.Add("CronSchedule", (context.FunctionDefinition.Parameters[0].Properties["bindingAttribute"] as TimerTriggerAttribute).Schedule);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                    }
                    break;
                default:
                    break;
            }

            return (triggerType, scopedLoggingConext);
        }
    }
}