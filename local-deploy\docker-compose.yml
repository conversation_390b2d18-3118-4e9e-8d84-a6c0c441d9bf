version: '3.4'

services:

  #redis:
  #  image: redis:alpine
  #  ports:
  #  - "5379:6379"

  #sqldata:
  #  image: mcr.microsoft.com/mssql/server:2022-latest
  #  environment:
  #    - SA_PASSWORD=Pass@word
  #    - ACCEPT_EULA=Y
  #  ports:
  #    - "5433:1433"
  #  volumes:
  #    - sqldata:/var/opt/mssql

  # traefik:
  #   # the official v2 traefik docker image
  #   image: traefik:v2.9
  #   # enables the web ui and tells traefik to listen to docker
  #   command: 
  #     # do not expose in prod. testing only. https://doc.traefik.io/traefik/operations/dashboard/#secure-mode
  #     - "--api.insecure=true"
  #     - "--api.dashboard=true"
  #     - "--providers.docker=true"
  #     - "--entrypoints.web.address=:80"
  #     - "--entrypoints.websecure.address=:443"
  #     # do not expose every container to traefik
  #     - "--providers.docker.exposedbydefault=false"
  #     - "--providers.docker.network=comms-network"
  #     - "--tracing.zipkin=true"
  #     - "--tracing.zipkin.httpEndpoint=http://zipkin:9411/api/v2/spans"
  #   ports:
  #     # the http port
  #     - "8084:80"
  #     # the web ui (enabled by --api.insecure=true)
  #     - "8080:8080"
  #     # the https port
  #     - "8443:443"
  #   volumes:
  #     # so that traefik can listen to the docker events
  #     - /var/run/docker.sock:/var/run/docker.sock
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.traefik.entrypoints=web"
  #     - "traefik.http.routers.traefik.rule=host(`traefik.docker.localhost`)"
  #     - "traefik.http.middlewares.redirecthttps.redirectscheme.scheme=https"
  #     - "traefik.http.middlewares.sslheader.headers.customrequestheaders.x-forwarded-proto=https"
  #     - "traefik.http.routers.traefik.middlewares=redirecthttps"
  #     - "traefik.http.routers.traefiksecure.entrypoints=websecure"
  #     - "traefik.http.routers.traefiksecure.rule=host(`traefik.docker.localhost`)"
  #     - "traefik.http.services.traefik.loadbalancer.server.port=8080"
  #     - "traefik.http.routers.traefiksecure.service=api@internal"
  #     - "traefik.docker.network=comms-network"

  #zipkin:
  #  image: openzipkin/zipkin-slim
  #  ports:
  #    - "5411:9411"

  redi-microservice-workflow:
    image: ${DOCKER_REGISTRY-}redi-microservice-workflow
    build:
      context: .
      dockerfile: ../MicroserviceBackendWorkflow/Dockerfile
    #depends_on:
    #  - sqldata
    labels:
      - "traefik.docker.network=comms-network"
      - "traefik.enable=true"
      - "traefik.http.routers.microservicebasesecure.tls=true"
      - "traefik.http.routers.microservicebase.rule=Host(`redi-microservice-workflow.docker.localhost`)"
      - "traefik.http.routers.microservicebase.entrypoints=web"
      - "traefik.http.routers.microservicebasesecure.rule=Host(`redi-microservice-workflow.docker.localhost`)"
      - "traefik.http.routers.microservicebasesecure.entrypoints=websecure"
  
  #If running in CLI, running dapr as a process and not in a container, then:
  #  dapr run --app-id MicroserviceBackendWorkflow --app-port 5103 --dapr-http-port 3500 --dapr-grpc-port 50001 --resources-path "<..path..>/dapr/components" --config "<..path..>/dapr/configuration/setup-config.yaml"
  redi-microservice-workflow-dapr:
   image: "daprio/daprd:latest"
   command: [ "./daprd", 
    "-app-id", "redi-microservice-workflow", 
    "-app-port", "8080",
    "-dapr-http-max-request-size", "64",
       "-dapr-grpc-port", "50001",
       "-components-path", "/components", 
       "-config", "/configuration/setup-config.yaml",
       ]
   volumes:
      - "./dapr/components/:/components"
      - "./dapr/configuration/:/configuration"
   depends_on: 
    - redi-microservice-workflow
   network_mode: "service:redi-microservice-workflow"

  #microservicebackgroundprocess:
  #  image: ${DOCKER_REGISTRY-}microservicebackgroundprocess
  #  build:
  #    context: .
  #    dockerfile: MicroserviceBackgroundProcess/Dockerfile
  #  ports:
  #   - "5102:80"

  #microservicebackgroundprocess-dapr:
  # image: "daprio/daprd:latest"
  # command: [ "./daprd", 
  # "-app-id", "microservicebackgroundprocess", 
  # "-app-port", "80",
  # "-components-path", "/components", 
  # "-config", "/configuration/setup-config.yaml",
  # ]
  # volumes:
  #    - "./dapr/components/:/components"
  #    - "./dapr/configuration/:/configuration"
  # depends_on: 
  #  - microservicebackgroundprocess
  # network_mode: "service:microservicebackgroundprocess"

networks:
  # Allows communication across docker compose groups (used by pubsub)
  default:
    name: comms-network