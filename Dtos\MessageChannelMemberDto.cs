﻿using Sql;

namespace Redi.Prime3.MicroService.BaseLib
{
    [Mappable(nameof(MessageChannelMemberId))]
    public class MessageChannelMemberDto
    {
        public int MessageChannelMemberId { get; set; }
        public int MessageChannelId { get; set; }
        public Guid PartyId { get; set; }
        public bool HasUnreadMessages { get; set; }
        public DateTimeOffset? LastSeenAt { get; set; }
        public int? SortOrder { get; set; }
        public bool IsMuted { get; set; }
        public bool IsPinned { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
        public bool Deleted { get; set; }
    }
}
