# bitbucket-pipelines.yml for building .NET Core librarie
# and publishing them to www.myget.org.
#
# You can use a Docker image from Docker Hub or your own container
# registry for your build environment.
#
# Requires environment variables to be configured:
#   MYGET_NUGET_URL:      The full URL of the NuGet feed on MyGet
#   MYGET_NUGET_APIKEY:   MyGet API key
#   BUILD_CONFIGURATION:  Build configuration (Debug/Release)
image: mcr.microsoft.com/dotnet/sdk:8.0
pipelines:
  default:
    - step:
        script:
          # Generate build number 
          - BUILD_NUMBER=`git log --oneline | wc -l`
          - echo "Build number':' ${BUILD_NUMBER} (will be appended to the generated NuGet package version)"
          # Restore packages
          - dotnet restore
          # Build project
          - dotnet build --configuration ${BUILD_CONFIGURATION}
          # Create package
          - dotnet pack --configuration ${BUILD_CONFIGURATION} --version-suffix=$BUILD_NUMBER redi.prime3.microservice.baselib.csproj
          # Push generated package(s)
          - "for file in bin/${BUILD_CONFIGURATION}/*.nupkg; do curl -X POST \"${MYGET_NUGET_URL}\" -H \"X-NuGet-ApiKey: ${MYGET_NUGET_APIKEY}\" -T $file; done"
