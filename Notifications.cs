﻿using System.Text;
using System.Text.Json;
using Azure.Storage.Queues;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;
using Microsoft.VisualBasic;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// The Notifications class sends messages to the notification-event queue so that any form of notification 
    /// can be configured it. The Notification system will apply rules to send email/sms/mobile notification/in-app/message
    /// </summary>
    public class Notifications : AzureQueueBase
    {
        private readonly ILogger _logger;
        private readonly UtilityFunctions _utilityFunctions;

        public Notifications(IAzureClientFactory<QueueServiceClient> notificationQueueServiceClient, ILogger logger, UtilityFunctions utilityFunctions)
        {
            if (string.IsNullOrEmpty(ConfigBase.AzureStorageNotificationAccountName)) 
            {
                throw new Exception("Notifications has not been properly configured for use by this Microservice. The config setting AzureStorageNotificationAccountName must be set");
            }
            _queueServiceClient = notificationQueueServiceClient.CreateClient("Notifications");
            _logger = logger;
            _utilityFunctions = utilityFunctions;
        }

        /// <summary>
        /// Write an event to the Notification Event Queue.
        /// This supports triggering of sending Emails, Sms's, Mobile Notifcations, App Messages, etc
        /// </summary>
        /// <param name="eventCode">NotificationEventCodeEnum - defines the tyoe of event. The notification runs rules on this to determine what needs to be sent.</param>
        /// <param name="tenantId">Tenant related to order, invoice, customer</param>
        /// <param name="customerPartyId"></param>
        /// <param name="assigneePartyId"></param>
        /// <param name="userId">The user who created or performed an action on an order or invoice</param>
        /// <param name="managerPartyId">Manager associated with the event. For Messages this is the Member</param>
        /// <param name="parentPartyId"></param>
        /// <param name="metaData">Dictionary of metaData values (string, object?)</param>
        /// <returns></returns>
#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task WriteEvent(string eventCode, int? tenantId = null, Guid? customerPartyId = null, Guid? assigneePartyId = null, Guid? userId = null, Guid? managerPartyId = null, Guid? parentPartyId = null, Dictionary<string, object?>? metaData = null)
        {

            if (metaData != null && metaData.Count > 0)
            {
                // Remove any null metaData values.
                foreach (var mrec in metaData.Where(t => t.Value == null).ToList())
                {
                    metaData.Remove(mrec.Key);
                }
            }

            if (tenantId == null) 
            { 
                // Always pass across current tenant if an override is not passed in
                tenantId = _utilityFunctions.TenantId; 
            }

            NotificationEventDto msg = new NotificationEventDto()
            {
                EventCode = eventCode,
                TenantId = tenantId,
                CustomerPartyId = customerPartyId,
                AssigneePartyId = assigneePartyId,
                UserId = userId,
                ManagerPartyId = managerPartyId,
                ParentPartyId = parentPartyId,
                MetaData = metaData
            };

            try
            {
                await InsertIntoQueue(JsonSerializer.Serialize(msg), "notification-event");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Writing NotificationEvent. Error Ignored.");
            }
        }

        /// <summary>
        /// Test the storage account connection by writing to a test queue.
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            try
            {
                var testQueueName = "ztest-noti-" + StartupBase.AppId;
                if (testQueueName.Length >63 ) { testQueueName = testQueueName.Substring(0, 63); }
                await InsertIntoQueue("Test Message", testQueueName, autoCreateQueue: true);
            }
            catch (Exception ex)
            {
                throw new StartupException($"Notifications Storage Connection failed. {ex.Message}. AccountName:{ConfigBase.AzureStorageNotificationAccountName}");
            }
        }
    }
}
