﻿using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Redi.Prime3.MicroService.Logger;
using System.Reflection;
using System.Runtime.Loader;

namespace Redi.Prime3.Function.BaseLib
{
    public partial class StartupBase
    {
        public static string AppId = "";
        public static string Version = "";        
        public ILoggerProvider LoggerProviderRoot { get; }

        public ILogger StartupLogger { get; }

        public static Assembly[] AppAssembliesRoot { 
            get {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable CS8604 // Possible null reference argument.
                return Assembly.GetEntryAssembly() // Get the main Assembly (one that called us)
                    .GetReferencedAssemblies()
                    .Select(Assembly.Load)
                    .Concat(new List<Assembly> { Assembly.GetEntryAssembly() })
                    .ToArray();
#pragma warning restore CS8604 // Possible null reference argument.
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            } 
        }

        public StartupBase(string[] args, string baseDirectory, string appId, string version)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("host.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables()
                .AddCommandLine(args)
                .Build();

            ConfigBase.Initialise(configuration);
            LoggerProviderRoot = new AppLoggerProvider(configuration);
            AppId = appId;
            Version = version;

            StartupLogger = LoggerProviderRoot.CreateLogger(AppId);
        }

        /// <summary>
        /// Stage 1 of startup <para/>
        /// - get connection string to db <para/>
        /// - check connection to database <para/>
        /// - get any config settings from db System Settings table. Pass the requried system setting code in systemSettingCodes. These will be globally available within the function
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="systemSettingCodes">System Setting Codes to read from the common.SystemSetting table. These will be available via ConfigBase.GetConfigValue/GetConfigValueList/GetConfigGuidValue/GetConfigIntValue/GetConfigBoolValue</param>
        /// <returns></returns>
        public async Task StartupStage1(string[] systemSettingCodes, bool bypassCommonSchemaCheck = false)
        {
            AssemblyLoadContext.Default.Resolving += OnAssemblyResolve;

            var commonSchemaExists = await CheckDatabaseConnection(ConfigBase.DatabaseConnectionString);
            if (bypassCommonSchemaCheck == false && commonSchemaExists == false) { throw new StartupException($"Common database schema does not exist in database. Common Microservice must be installed and started first."); } 

            await Redi.Prime3.Function.BaseLib.StartupBase.SetConfigurationValuesAsync(ConfigBase.IConfig, ConfigBase.DatabaseConnectionString!, systemSettingCodes);
        }

        /// <summary>
        /// Final stage of startup <para/>
        /// - Read any config values from the Key Vault <para/>
        /// - Validate Storage accounts for Queues, and Blobs
        /// </summary>
        /// <param name="app"></param>
        /// <param name="keyVaultSettings">Config items to be read from the Azure Key Vault.</param>
        /// <param name="checkBlobAccess">Default true. Checks the function can access queues in the Storage Account - writes to a queue</param>
        /// <param name="checkQueueAccess">Default true. Checks the function can access blobs in the Storage Account - writes a blob</param>
        /// <returns></returns>
        public async Task StartupStageFinal(IHost app, string[]? keyVaultSettings, bool checkQueueAccess = true, bool checkBlobAccess = true)
        {
            try
            {

                await ReadConfigValuesFromAzureKeyVault(app, keyVaultSettings);

                if (!string.IsNullOrEmpty(ConfigBase.AzureStorageNotificationAccountName))
                {
                    var autofacContainer = app.Services.GetAutofacRoot();
                    using (var childScope = autofacContainer.BeginLifetimeScope())
                    {
                        if (checkQueueAccess)
                        {
                            var notifications = childScope.Resolve<Redi.Prime3.Function.BaseLib.Notifications>();
                            await notifications.TestConnection();
                        }
                    }
                }

                if (!string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
                {
                    var autofacContainer = app.Services.GetAutofacRoot();
                    using (var childScope = autofacContainer.BeginLifetimeScope())
                    {
                        if (checkQueueAccess)
                        {
                            var client = childScope.Resolve<Redi.Prime3.Function.BaseLib.DefaultAzureQueue>();
                            await client.TestConnection();
                        }

                        if (checkBlobAccess)
                        {
                            var blobClient = childScope.Resolve<Redi.Prime3.Function.BaseLib.DefaultAzureBlob>();
                            await blobClient.TestConnection();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (ConfigBase.IsRunningLocally == true)
                {
                    // In local dev just write error message.
                    StartupLogger.LogError(ex.Message);
                }
                else
                {
                    // In Live throw error
                    throw;
                }
            }
        }

        /// <summary>
        /// Run the Function till shutdown 
        /// </summary>
        /// <param name="app"></param>
        public void Run(IHost app)
        {
            try
            {
                StartupLogger.LogInformation($"Starting PRIME3 Function '{AppId}', version {Version}...");
                app.Run();
            }
            catch (Exception ex)
            {
                StartupLogger.LogCritical(ex, $"PRIME3 Function terminated unexpectedly '{AppId}'...");
            }
        }

        /// <summary>
        /// Configure the runtime environment <para/>
        /// - Add error middleware <para/>
        /// - Add Logging <para/>
        /// - Add Authentication <para/>
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="addHttpRequestAuthentication">NOT YET SUPPORTED. Default false. Set to true to enable JWT Beaer authentation and use of Authorize attribute on http functions </param>
        public void Configure(IHostBuilder builder, bool addHttpRequestAuthentication = false)
        {
            builder
                .ConfigureFunctionsWebApplication((host, builder) =>
                { 
                    builder.UseMiddleware<HandleErrorMiddleware>(); 
                })
                //.ConfigureFunctionsWorkerDefaults((host, builder) =>
                //{
                //    builder.UseMiddleware<HandleErrorMiddleware>();
                //})
                .ConfigureLogging((hostingContext, logging) =>
                {
                    logging.ClearProviders();
                    logging.AddConfiguration(hostingContext.Configuration);
                    logging.AddDebug();
                    logging.AddConsole();
                    logging.SetMinimumLevel(LogLevel.Debug);
                    logging.AddProvider(new AppLoggerProvider(hostingContext.Configuration)); // The Redi Prime3 Logger
                });

            if (addHttpRequestAuthentication == true) { throw new StartupException("HTTP Request Authentication using JWT Bearer not yet supported"); }
        }

        // <summary>
        // since is a netframework lib, we need to redirect the Microsoft.SqlServer.Types lib to the dotnet core version we have installed
        // </summary>
        static Assembly OnAssemblyResolve(AssemblyLoadContext assemblyLoadContext, AssemblyName assemblyName)
        {
            try
            {
                AssemblyLoadContext.Default.Resolving -= OnAssemblyResolve;
                return assemblyLoadContext.LoadFromAssemblyName(assemblyName);
            }
            catch
            {
                if (assemblyName.Name == "Microsoft.SqlServer.Types")
                    return typeof(Microsoft.SqlServer.Types.SqlGeography).Assembly;
                throw;
            }
            finally
            {
                AssemblyLoadContext.Default.Resolving += OnAssemblyResolve;
            }
        }

        // <summary>
        // Loads any system setting values into the system Configuration on startup to be used globally. Preferred over constant calls to DB, however the option is available
        // for more dynamic changing values. This method is suited for loading values that won't change for the lifetime of the systems runtime.
        // </summary>
        private static async Task SetConfigurationValuesAsync(IConfiguration configuration, string connectionString, string[] getCodes)
        {
            var systemSettings = await SystemSetting.GetSystemSettingsAsync(connectionString, getCodes);
            foreach (var setting in systemSettings)
            {
                var value = configuration[setting.Key];
                if (string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(setting.Value))
                {
                    configuration[setting.Key] = setting.Value;
                }
            }
            //Environment will either come from the System settings table if available, or the default environment variable
            configuration["Environment"] = configuration["Environment"] ?? configuration["ASPNETCORE_ENVIRONMENT"];
        }

        /// <summary>
        /// Get secret's from the Azure Key Vault and add to config for easy access.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="getVaultKeys"></param>
        /// <returns></returns>
        /// <exception cref="StartupException"></exception>
        private static async Task ReadConfigValuesFromAzureKeyVault(IHost app, string[]? getVaultKeys)
        {
            if (getVaultKeys == null || getVaultKeys.Length == 0) { return; };

            if (!string.IsNullOrEmpty(ConfigBase.KeyVaultName))
            {
                var autofacContainer = app.Services.GetAutofacRoot();
                using (var childScope = autofacContainer.BeginLifetimeScope())
                {
                    var client = childScope.Resolve<Redi.Prime3.Function.BaseLib.DefaultAzureVault>();
                    foreach (var key in getVaultKeys)
                    {
                        var res = await client.GetSecret(key, ignoreNotFound: true);
                        if (res == null)
                        {
                            if (ConfigBase.IConfig[key] == null)
                            {
                                throw new StartupException($"Key Vault {ConfigBase.KeyVaultName} is missing a record for {key}");
                            }
                        }
                        else
                        {
                            ConfigBase.IConfig[key] = res.Value;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Check the database connection string works
        /// Also checks if the common schema exists
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns>bool - true if common schema exists</returns>
        private static async Task<bool> CheckDatabaseConnection(string? connectionString)
        {
            if (connectionString == null) 
            {
                throw new StartupException("Database Connection String is empty (DatabaseConnectionString)");
            }
            try
            {
                string sql = @"SELECT name FROM sys.schemas Where [name] = 'common'";
                var resultset = new Dictionary<string, string?>();
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(sql, connection))
                    {
                        var val = await command.ExecuteScalarAsync();
                        if (val != null && Convert.ToString(val) == "common") { return true; }

                    }
                }
            }
            catch (Exception ex)
            {
                // Throw exception with connect string details excluding password.
                string csDisplay = connectionString.IndexOf("Password") > 0 ? connectionString.Substring(0,connectionString.IndexOf("Password") + 5) + "....(password truncated)" : connectionString;
                throw new StartupException( $"Database Connection Failed. {ex.Message} ConnectionString Starts With: {csDisplay}");
            }

            return false;
        }
    }
}
