using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Reflection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Dapr.Client;
using System.Runtime.Loader;
using Microsoft.Data.SqlClient;
using Autofac.Extensions.DependencyInjection;
using Autofac;
using Redi.Prime3.MicroService.Logger;
using Scalar.AspNetCore;

namespace Redi.Prime3.MicroService.BaseLib
{
    public partial class StartupBase
    {

        public static string AppId = "";
        public static string Version = "";
        private string? sqlFailedMessage = null;
        IWebHostEnvironment environmentRoot { get; }
        public ILoggerProvider LoggerProviderRoot { get; }
        public static Assembly[] AppAssembliesRoot { 
            get {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
#pragma warning disable CS8604 // Possible null reference argument.
                return Assembly.GetEntryAssembly() // Get the main Assembly (one that called us)
                    .GetReferencedAssemblies()
                    .Select(Assembly.Load)
                    .Concat(new List<Assembly> { Assembly.GetEntryAssembly() })
                    .ToArray();
#pragma warning restore CS8604 // Possible null reference argument.
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            } 
        }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public StartupBase(IConfiguration configuration, IWebHostEnvironment environment, string appId, string version)
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            ConfigBase.Initialise(configuration);
            environmentRoot = environment;
            LoggerProviderRoot = new AppLoggerProvider(configuration);
            AppId = appId;
            Version = version;
        }

        /// <summary>
        /// Stage 1 of startup
        /// - get connection string to db
        /// - apply any sql to db
        /// - update any config settings from db System Settings
        /// </summary>
        /// <param name="builder"></param>
        /// <param name="systemSettingCodes"></param>
        /// <returns></returns>
        public async Task<String?> StartupStage1(WebApplicationBuilder builder, string[] systemSettingCodes, bool bypassCommonSchemaCheck = false)
        {
            var secretStoreName = builder.Configuration["SECRETS_STORE_NAME"] ?? "Store Name Missing";
            var secretStore = await GetSecretStoreConnectionStringsBaseDbConnAsync(secretStoreName);
            var connectionDbString = secretStore.Values.First();
            AssemblyLoadContext.Default.Resolving += OnAssemblyResolve;

            var commonSchemaExists = await CheckDatabaseConnection(connectionDbString);
            if (bypassCommonSchemaCheck == false && commonSchemaExists == false) { throw new StartupException($"Common database schema does not exist in database. Common Microservice must be installed and started first."); } 

            sqlFailedMessage = await StartupBaseRunSql.DoAsync(connectionDbString);

            await Redi.Prime3.MicroService.BaseLib.StartupBase.SetConfigurationValuesAsync(builder.Configuration, connectionDbString, systemSettingCodes);

            return sqlFailedMessage;
        }

        /// <summary>
        /// Validate Storage accounts
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        public async Task StartupStageFinal(WebApplication app)
        {
            try
            {
                if (!string.IsNullOrEmpty(ConfigBase.AzureStorageNotificationAccountName))
                {
                    var autofacContainer = app.Services.GetAutofacRoot();
                    using (var childScope = autofacContainer.BeginLifetimeScope())
                    {
                        var notifications = childScope.Resolve<Redi.Prime3.MicroService.BaseLib.Notifications>();
                        await notifications.TestConnection();
                    }
                }

                if (!string.IsNullOrEmpty(ConfigBase.AzureStorageConnectionString))
                {
                    var autofacContainer = app.Services.GetAutofacRoot();
                    using (var childScope = autofacContainer.BeginLifetimeScope())
                    {
                        var client = childScope.Resolve<Redi.Prime3.MicroService.BaseLib.DefaultAzureQueue>();
                        await client.TestConnection();
                    }
                }
            }
            catch (Exception ex)
            {
                if (environmentRoot.EnvironmentName == "Development")
                {
                    // In development just write error message.
                    app.Logger.LogError(ex.Message);
                }
                else
                {
                    // In Live throw error
                    throw;
                }
            }
        }

        /// <summary>
        /// Run the Application till shutdown 
        /// </summary>
        /// <param name="app"></param>
        public void Run(WebApplication app)
        {
            try
            {
                app.Logger.LogInformation($"Starting PRIME3 Microservice '{AppId}', version {Version}...");
                if (sqlFailedMessage != null)
                {
                    app.Logger.LogError(sqlFailedMessage);
                }
                app.Run();
            }
            catch (Exception ex)
            {
                app.Logger.LogCritical(ex, $"PRIME3 Microservice terminated unexpectedly '{AppId}'...");
            }
        }

        /// <summary>
        /// Configure the runtime environment
        /// - Add error middleware
        /// - Add Swagger
        /// - Add Cors
        /// - Add Authentication
        /// - Add Authorisation
        /// - Add Map Controllers
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void Configure(WebApplication app, IWebHostEnvironment env)
        {
            app.Use(HandleErrorMiddleware);
            app.Use((context, next) =>
            {
                context.Request.Scheme = "https";
                var customdomain = ConfigBase.Hostname;
                var slashIndex = customdomain != null ? customdomain.IndexOf('/') : -1;
                if (slashIndex > -1)
                {
                    var hostname = customdomain!.Substring(0, slashIndex);
                    var pathbase = customdomain.Substring(slashIndex);
                    context.Request.Host = new HostString(hostname);
                    context.Request.PathBase = new PathString(pathbase);
                }
                else
                {
                    context.Request.Host = new HostString(customdomain!);
                }

                return next(context);
            });

            app.UseForwardedHeaders();

            if (!env.IsDevelopment())
            {
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            } 
            else
            {
                app.UseSwagger();
                app.UseSwagger(options =>
                {
                    options.RouteTemplate = "/openapi/{documentName}.json";
                });
                // Scalar Api Documentation will be available at /scalar/v1
                app.MapScalarApiReference();
            }

            app.UseCors(options =>
                options
                     .AllowAnyMethod()
                     .AllowAnyHeader()
                     .AllowAnyHeader()
                     .SetIsOriginAllowed(origin => true)
                     .AllowCredentials()
            );

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseStaticFiles();
            app.UseSentryTracing();
            app.UseForwardedHeaders();

            //Middleware components to enable Dapr
            app.MapControllers();
            app.MapSubscribeHandler();

            app.MapControllerRoute("default", "api/{controller=Home}/{action=Index}/{id?}");
        }

        // <summary>
        // since is a netframework lib, we need to redirect the Microsoft.SqlServer.Types lib to the dotnet core version we have installed
        // </summary>
        static Assembly OnAssemblyResolve(AssemblyLoadContext assemblyLoadContext, AssemblyName assemblyName)
        {
            try
            {
                AssemblyLoadContext.Default.Resolving -= OnAssemblyResolve;
                return assemblyLoadContext.LoadFromAssemblyName(assemblyName);
            }
            catch
            {
                if (assemblyName.Name == "Microsoft.SqlServer.Types")
                    return typeof(Microsoft.SqlServer.Types.SqlGeography).Assembly;
                throw;
            }
            finally
            {
                AssemblyLoadContext.Default.Resolving += OnAssemblyResolve;
            }
        }

        public static async Task<Dictionary<string, string>> GetSecretStoreConnectionStringsBaseDbConnAsync(string secretStoreName)
        {
            var daprClient = new DaprClientBuilder().Build();
            var secretstore = await daprClient.GetSecretAsync(secretStoreName, "ConnectionStringsBaseDbConn");
            return secretstore;
        }

        // <summary>
        // Loads any system setting values into the system Configuration on startup to be used globally. Preferred over constant calls to DB, however the option is available
        // for more dynamic changing values. This method is suited for loading values that won't change for the lifetime of the systems runtime.
        // </summary>
        public static async Task SetConfigurationValuesAsync(ConfigurationManager configuration, string connectionString, string[] getCodes)
        {
            var systemSettings = await SystemSetting.GetSystemSettingsAsync(connectionString, getCodes);
            foreach (var setting in systemSettings)
            {
                var value = configuration[setting.Key];
                if (string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(setting.Value))
                {
                    configuration[setting.Key] = setting.Value;
                }
            }
            //Environment will either come from the System settings table if available, or the default environment variable
            configuration["Environment"] = configuration["Environment"] ?? configuration["ASPNETCORE_ENVIRONMENT"];
        }

        /// <summary>
        /// Check the database connection string works
        /// Also checks if the common schema exists
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns>bool - true if common schema exists</returns>
        public static async Task<bool> CheckDatabaseConnection(string connectionString)
        {
            if (connectionString == null) 
            {
                throw new StartupException("Database Connection String is empty");
            }
            try
            {
                string sql = @"SELECT name FROM sys.schemas Where [name] = 'common'";
                var resultset = new Dictionary<string, string?>();
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(sql, connection))
                    {
                        var val = await command.ExecuteScalarAsync();
                        if (val != null && Convert.ToString(val) == "common") { return true; }

                    }
                }
            }
            catch (Exception ex)
            {
                string csDisplay = connectionString.IndexOf("Password") > 0 ? connectionString.Substring(0,connectionString.IndexOf("Password")) : connectionString;
                throw new StartupException( $"Database Connection Failed. {ex.Message} ConnectionString Starts With: {csDisplay}");
            }

            return false;
        }
    }
}
