using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Azure Timer Triggered Functions.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class Prime3FunctionTimerBase : Prime3FunctionBase
	{
        protected string? _timerCronExpression;
        
        /// <summary>
        /// Prime3 Function standard pre processing of timer.<para/>
        /// 1) log timer details 
        /// </summary>
        /// <param name="timeInfo">TimerInfo</param>
        /// <param name="context">function context</param>
        /// <returns></returns>
        public async Task PreProcess(TimerInfo timeInfo, FunctionContext context)
        {
            if (context.BindingContext.BindingData.TryGetValue("TimerTrigger", out var timerTrigger) && timerTrigger != null)
            {
                _timerCronExpression = timerTrigger.ToString();
            } 

            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Triggered for Timer: {_timerCronExpression} {(timeInfo.IsPastDue ? "PAST DUE" : "")}");
        }

        /// <summary>
        /// Do Timer Post processing.<para/>
        /// 1) Log timer stats
        /// </summary>
        /// <param name="context">function context</param>
        /// <param name="timeInfo">TimerInfo</param>
        public async Task PostProcess(TimerInfo timeInfo, FunctionContext context)
        {
            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Processing Complete for Timer: {_timerCronExpression}. ");
        }
    }
}
