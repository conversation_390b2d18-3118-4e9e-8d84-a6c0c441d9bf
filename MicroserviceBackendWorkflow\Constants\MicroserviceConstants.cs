using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendWorkflow.Constants
{
    /// <summary>
    /// This class holds the App ID's for microservices used in this application
    /// It extends the MicroserviceConstantsBase class to inherit the App ID's for common microservices
    /// </summary>
    public class MicroserviceConstants : MicroserviceConstantsBase
    {
        /// <summary>
        /// User Microservice App ID
        /// </summary>
        public static readonly string AppId = "redi-microservice-workflow";
        /// <summary>
        /// User Microservice App ID
        /// </summary>
        public static readonly string CommonAppId = "redi-microservice-common";
        /// <summary>
        /// Microservice Version
        /// major . minor . bug
        /// </summary>
        public static readonly string Version = "0.9.0";
    }
}