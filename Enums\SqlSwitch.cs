namespace Redi.Prime3.MicroService.BaseLib
{
	/// <summary>
	/// SQL Middleware Switch
	/// </summary>
	public enum SqlSwitch
	{
		/// <summary>
		/// Set prevous value so we can audit what changed
		/// </summary>
		PreviousValue = 1,
		/// <summary>
		/// Force audit on this sql command. Has no effect on select statements
		/// </summary>
		Audit = 2,
		IgnoreDeletedFilter = 3,
		AddCreatedOn = 4,
		AddModifiedOn = 5,
	}
}