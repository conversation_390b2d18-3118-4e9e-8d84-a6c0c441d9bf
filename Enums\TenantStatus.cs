﻿namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Tenant Status Enum
    /// </summary>
    public enum TenantStatus
    {
        /// <summary>
        /// Tenant is active
        /// </summary>
        Active,
        /// <summary>
        /// Tenant has been cancelled. All associated data will be deleted after 90 days.
        /// </summary>
        Cancelled,
        /// <summary>
        /// Subscription has expired, access by client is restricted. Will move to cancelled after 90 days.
        /// </summary>
        Expired,
        /// <summary>
        /// Tenant is currently on hold, access is restricted
        /// </summary>
        OnHold
    }
}