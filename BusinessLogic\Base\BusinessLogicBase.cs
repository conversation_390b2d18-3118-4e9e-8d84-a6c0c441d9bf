using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Sql;

namespace RediAzurefunctionBase.BusinessLogic
{
    /// <summary>
    /// Base class for Business Logic type classes.
    /// Defines Unit of Work, Logger, and Cache
    /// </summary>
    public class BusinessLogicBase
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        protected IUnitOfWork _unitOfWork;
        protected IMemoryCache _memoryCache;
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public MemoryCacheEntryOptions CacheOptions()
        {
            var cacheEntryOptions = new MemoryCacheEntryOptions()
            {
                AbsoluteExpiration = DateTimeOffset.UtcNow.AddMinutes(0.1),
            };
            return cacheEntryOptions;
        }
    }
}
