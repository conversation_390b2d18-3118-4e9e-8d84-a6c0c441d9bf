using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic
{
    /// <summary>
    /// Base class for Business Logic type classes.
    /// Defines Unit of Work, Logger, Utility Functions, and Cache
    /// </summary>
    public class BusinessLogicBase
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        protected IUnitOfWork _unitOfWork;
        protected ILogger _logger;
        protected UtilityFunctions _utilityFunctions;
        protected Cache _cache;
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        public MemoryCacheEntryOptions CacheOptions()
        {
            var cacheEntryOptions = new MemoryCacheEntryOptions()
            {
                AbsoluteExpiration = DateTimeOffset.UtcNow.AddMinutes(0.1),
            };
            return cacheEntryOptions;
        }
    }
}
