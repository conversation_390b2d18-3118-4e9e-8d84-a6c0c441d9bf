using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Base class for DTOs with common audit fields
    /// </summary>
    public abstract class DtoBase
    {
        /// <summary>
        /// The date and time when the record was created
        /// </summary>
        public DateTimeOffset CreatedOn { get; set; }

        /// <summary>
        /// The name of the user who created the record
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// The date and time when the record was last modified
        /// </summary>
        public DateTimeOffset? ModifiedOn { get; set; }

        /// <summary>
        /// The name of the user who last modified the record
        /// </summary>
        public string? ModifiedByName { get; set; }

        /// <summary>
        /// Indicates whether the record has been soft deleted
        /// </summary>
        public bool Deleted { get; set; }
    }

    /// <summary>
    /// Base class for DTOs with tenant support and audit fields
    /// </summary>
    public abstract class TenantDtoBase : DtoBase
    {
        /// <summary>
        /// When set indicates the record belongs to a single tenant.
        /// When empty the record is applicable to any tenant.
        /// </summary>
        public int? TenantId { get; set; }
    }
}
