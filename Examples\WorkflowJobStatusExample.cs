using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Examples
{
    /// <summary>
    /// Example class demonstrating how to use workflow job status enums and validation
    /// </summary>
    public class WorkflowJobStatusExample
    {
        private readonly WorkflowJob _workflowJob;
        private readonly JobStatus _jobStatus;
        private readonly WfJobCancelledReason _cancelledReason;

        public WorkflowJobStatusExample(WorkflowJob workflowJob, JobStatus jobStatus, WfJobCancelledReason cancelledReason)
        {
            _workflowJob = workflowJob;
            _jobStatus = jobStatus;
            _cancelledReason = cancelledReason;
        }

        /// <summary>
        /// Example: Create and run a workflow job with validated status
        /// </summary>
        public async Task<long> CreateAndRunWorkflowExample(int workflowId)
        {
            // The WorkflowJob.RunUnscheduledWfJobAsync method automatically validates
            // the status using JobStatusEnum.Executing and sets the appropriate status ID
            var jobId = await _workflowJob.RunUnscheduledWfJobAsync(
                workflowId, 
                name: "Example Workflow Job"
            );

            return jobId;
        }

        /// <summary>
        /// Example: Cancel a workflow job with a specific reason
        /// </summary>
        public async Task CancelWorkflowJobExample(long jobId)
        {
            // Cancel the job with a manual cancellation reason
            // The method validates both the status change and the cancellation reason
            await _workflowJob.CancelWfJobAsync(jobId, WfJobCancelledReasonEnum.ManualCancel);
        }

        /// <summary>
        /// Example: Validate a job status ID
        /// </summary>
        public async Task<bool> ValidateJobStatusExample(short statusId)
        {
            // Check if a status ID is valid and enabled
            return await _jobStatus.IsValidStatusAsync(statusId);
        }

        /// <summary>
        /// Example: Get status information
        /// </summary>
        public async Task<string> GetStatusLabelExample(JobStatusEnum statusEnum)
        {
            // Convert enum to status ID
            var statusId = _jobStatus.GetStatusId(statusEnum);
            
            // Get the full status information
            var statusDto = await _jobStatus.GetAsync(statusId);
            
            return statusDto?.Label ?? "Unknown Status";
        }

        /// <summary>
        /// Example: Working with cancellation reasons
        /// </summary>
        public async Task<string> GetCancellationReasonExample(WfJobCancelledReasonEnum reasonEnum)
        {
            // Convert enum to reason ID
            var reasonId = _cancelledReason.GetReasonId(reasonEnum);
            
            // Get the full reason information
            var reasonDto = await _cancelledReason.GetAsync(reasonId);
            
            return reasonDto?.Label ?? "Unknown Reason";
        }

        /// <summary>
        /// Example: Validate status before using in business logic
        /// </summary>
        public async Task<short> GetValidatedStatusIdExample(JobStatusEnum statusEnum)
        {
            // This method validates that the enum value corresponds to a valid database record
            // Throws ArgumentException if the status is invalid or disabled
            return await _jobStatus.GetValidatedStatusIdFromEnumAsync(statusEnum);
        }

        /// <summary>
        /// Example: Get all available job statuses
        /// </summary>
        public async Task<List<string>> GetAllJobStatusLabelsExample()
        {
            var allStatuses = await _jobStatus.GetAllAsync();
            return allStatuses.Where(s => s.IsEnabled).Select(s => s.Label).ToList();
        }

        /// <summary>
        /// Example: Get all available cancellation reasons
        /// </summary>
        public async Task<List<string>> GetAllCancellationReasonsExample()
        {
            var allReasons = await _cancelledReason.GetAllAsync();
            return allReasons.Where(r => r.IsEnabled).Select(r => r.Label).ToList();
        }

        /// <summary>
        /// Example: Safe status conversion with error handling
        /// </summary>
        public JobStatusEnum? TryParseStatusEnum(short statusId)
        {
            return _jobStatus.GetStatusEnum(statusId);
        }

        /// <summary>
        /// Example: Safe cancellation reason conversion with error handling
        /// </summary>
        public WfJobCancelledReasonEnum? TryParseCancellationReasonEnum(byte reasonId)
        {
            return _cancelledReason.GetReasonEnum(reasonId);
        }
    }
}
