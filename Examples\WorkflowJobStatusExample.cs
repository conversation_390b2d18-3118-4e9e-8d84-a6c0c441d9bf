using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Examples
{
    /// <summary>
    /// Example class demonstrating how to use workflow job status enums and validation
    /// </summary>
    public class WorkflowJobStatusExample
    {
        private readonly WorkflowJob _workflowJob;
        private readonly JobStatus _jobStatus;
        private readonly WfJobCancelledReason _cancelledReason;

        public WorkflowJobStatusExample(WorkflowJob workflowJob, JobStatus jobStatus, WfJobCancelledReason cancelledReason)
        {
            _workflowJob = workflowJob;
            _jobStatus = jobStatus;
            _cancelledReason = cancelledReason;
        }

        /// <summary>
        /// Example: Create and run a workflow job with validated status
        /// </summary>
        public async Task<long> CreateAndRunWorkflowExample(int workflowId)
        {
            // The WorkflowJob.RunUnscheduledWfJobAsync method automatically validates
            // the status using JobStatusEnum.Executing and sets the appropriate status ID
            var jobId = await _workflowJob.RunUnscheduledWfJobAsync(
                workflowId, 
                name: "Example Workflow Job"
            );

            return jobId;
        }

        /// <summary>
        /// Example: Cancel a workflow job with a specific reason
        /// </summary>
        public async Task CancelWorkflowJobExample(long jobId)
        {
            // Cancel the job with a manual cancellation reason
            // The method validates both the status change and the cancellation reason using GetAsync()
            await _workflowJob.CancelWfJobAsync(jobId, WfJobCancelledReasonEnum.ManualCancel);
        }

        /// <summary>
        /// Example: Validate a job status using simple GetAsync pattern
        /// </summary>
        public async Task<bool> ValidateJobStatusExample(JobStatusEnum statusEnum)
        {
            // Use ignoreErrorIfNotExists = true to avoid exceptions
            var status = await _jobStatus.GetAsync(statusEnum, ignoreErrorIfNotExists: true);
            return status != null;
        }

        /// <summary>
        /// Example: Strict validation that throws exception if not found
        /// </summary>
        public async Task ValidateJobStatusStrictExample(JobStatusEnum statusEnum)
        {
            // This will throw ArgumentException if status doesn't exist
            await _jobStatus.GetAsync(statusEnum, ignoreErrorIfNotExists: false);
        }

        /// <summary>
        /// Example: Get status information using simple GetAsync
        /// </summary>
        public async Task<string> GetStatusLabelExample(JobStatusEnum statusEnum)
        {
            // Simple call with enum - validates and gets the full status information
            var statusDto = await _jobStatus.GetAsync(statusEnum);

            return statusDto?.Label ?? "Unknown Status";
        }

        /// <summary>
        /// Example: Working with cancellation reasons using simple GetAsync
        /// </summary>
        public async Task<string> GetCancellationReasonExample(WfJobCancelledReasonEnum reasonEnum)
        {
            // Simple call with enum - validates and gets the full reason information
            var reasonDto = await _cancelledReason.GetAsync(reasonEnum);

            return reasonDto?.Label ?? "Unknown Reason";
        }

        /// <summary>
        /// Example: Simple validation pattern used throughout the library
        /// </summary>
        public async Task ValidateStatusAndReasonExample(JobStatusEnum statusEnum, WfJobCancelledReasonEnum reasonEnum)
        {
            // Simple validation pattern - just call GetAsync
            // If the enum value doesn't exist in database, an exception will be thrown
            await _jobStatus.GetAsync(statusEnum, ignoreErrorIfNotExists: false);
            await _cancelledReason.GetAsync(reasonEnum, ignoreErrorIfNotExists: false);

            // If we get here, both values are valid
        }

        /// <summary>
        /// Example: Safe validation that doesn't throw exceptions
        /// </summary>
        public async Task<bool> SafeValidateStatusAndReasonExample(JobStatusEnum statusEnum, WfJobCancelledReasonEnum reasonEnum)
        {
            // Safe validation - returns null instead of throwing exceptions
            var status = await _jobStatus.GetAsync(statusEnum, ignoreErrorIfNotExists: true);
            var reason = await _cancelledReason.GetAsync(reasonEnum, ignoreErrorIfNotExists: true);

            return status != null && reason != null;
        }

        /// <summary>
        /// Example: Get specific status information by ID
        /// </summary>
        public async Task<string> GetStatusByIdExample(short statusId)
        {
            var statusDto = await _jobStatus.GetAsync(statusId);
            return statusDto?.Label ?? "Unknown Status";
        }

        /// <summary>
        /// Example: Get specific cancellation reason by ID
        /// </summary>
        public async Task<string> GetCancellationReasonByIdExample(byte reasonId)
        {
            var reasonDto = await _cancelledReason.GetAsync(reasonId);
            return reasonDto?.Label ?? "Unknown Reason";
        }

        /// <summary>
        /// Example: Validate multiple statuses at once
        /// </summary>
        public async Task ValidateMultipleStatusesExample()
        {
            // Validate multiple enum values exist in database
            await _jobStatus.GetAsync(JobStatusEnum.Scheduled);
            await _jobStatus.GetAsync(JobStatusEnum.Executing);
            await _jobStatus.GetAsync(JobStatusEnum.Completed);

            // All statuses are valid if no exceptions were thrown
        }
    }
}
