﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<VersionPrefix>1.0.0</VersionPrefix>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Title>Redi Prime3 Azure Function Base Library</Title>
    <Authors>Redi Software</Authors>
    <Company>Redi Software</Company>
    <Copyright>Redi Software 2025</Copyright>
    <PackageId>Redi.Prime3.Function.BaseLib</PackageId>
    <PackageTags>Function Base</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Storage.Queues" Version="5.5.1" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.1" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.9.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.1.0-preview.1.25064.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Redi.Prime3.MicroService.Logger" Version="1.0.2-10" />
    <PackageReference Include="Redi.Select.Filter" Version="1.0.7" />
    <PackageReference Include="Redi.Sql" Version="8.0.8" />
    <PackageReference Include="Scalar.AspNetCore" Version="1.2.72" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.2.0" />
    <PackageReference Include="Sentry.Azure.Functions.Worker" Version="4.1.0" />
  </ItemGroup>

</Project>
