﻿using System.Data.Common;
using Microsoft.Data.SqlClient;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Redi.Prime3.MicroService.BaseLib
{
    public class StartupBaseRunSql
    {
        /// <summary>
        /// Run SQL scripts that are in the Database folder and each sql file is prefixed with S
        /// </summary>
        /// <param name="connectionDbString"></param>
        /// <returns>Error when processing sql</returns>
        internal static async Task<string?> DoAsync(string connectionDbString)
        {
            string currentSql = "";
            string currentFile = "";
            try
            {
                var sqlFiles = await GetResourceRunSql();

                foreach (var sql in sqlFiles)
                {
                    currentFile = sql.Key;
                    if (!string.IsNullOrEmpty(sql.Value))
                    {
                        var statements = Regex.Split(sql.Value, @"^\s*GO\s*$", RegexOptions.Multiline | RegexOptions.IgnoreCase);
                        using (var connection = new SqlConnection(connectionDbString))
                        {
                            await connection.OpenAsync();
                            var transaction = connection.BeginTransaction();
                            foreach (var item in statements)
                            {
                                if (!string.IsNullOrEmpty(item))
                                {
                                    currentSql = item;
                                    using (var command = new SqlCommand(item, connection, transaction))
                                    {
                                        await command.ExecuteNonQueryAsync();
                                    }
                                }
                            }
                            Commit(transaction);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                Console.WriteLine(currentFile + ": " + currentSql);
                return currentFile + ": " + currentSql + " | " + ex.Message;
            }
            return null;
        }

        private static void Commit(DbTransaction transaction)
        {
            if (transaction != null)
            {
                try
                {
                    transaction.Commit();
                }
                catch (Exception e)
                {
                    transaction.Rollback();
                    throw new Exception("Transaction failed for SQL Startup. Error: " + e.Message);
                }
                finally
                {
                    transaction?.Dispose();
                }
            }
        }

        private static async Task<Dictionary<string, string>> GetResourceRunSql()
        {
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
            Assembly assembly = Assembly.GetEntryAssembly();
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
            Dictionary<string, string> resource = [];
#pragma warning disable CS8602 // Dereference of a possibly null reference.
            foreach (string name in assembly.GetManifestResourceNames())
            {
                if (name.Contains("Database.S"))
                {
                    using (Stream? stream = assembly.GetManifestResourceStream(name))
                    {
                        if (stream != null)
                        {
                            using (StreamReader reader = new StreamReader(stream))
                            {
                                resource.Add(name, await reader.ReadToEndAsync());
                            }
                        }
                    }
                }
            }
#pragma warning restore CS8602 // Dereference of a possibly null reference.
            return resource;
        }
    }
}
