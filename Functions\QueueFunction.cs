using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using RediAzurefunctionBase.BusinessLogic;
using RediAzurefunctionBase.Dtos;
using Sql;

namespace RediAzurefunctionBase.Functions
{
    public class QueueFunctionBase : Prime3FunctionQueueBase
    {
        private QueueProcessor1 _queueProcessor1;
        public QueueFunctionBase(IUnitOfWork unitOfWork
            , ILogger logger
            ,QueueProcessor1 queueProcessor1
            ,DefaultAzureQueue defaultAzureQueue
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _queueProcessor1 = queueProcessor1;
            _defaultAzureQueue = defaultAzureQueue;
        }

        [Function(nameof(QueueFunctionBase))]
        public async Task Run([QueueTrigger("theQueueName", Connection = "StorageConnectionString")] string queueItem, FunctionContext context)
        {
            // 1) Performs Standard Prime3 Pre Processing of a Queue (log, convert queue item to object of requested type/string)
            var data = await PreProcess<QueueFunction1Dto>(queueItem, context);

            // 2) Process the queue item
            var result = await _queueProcessor1.ProcessQueueItem(data);
            _unitOfWork.Commit();
            _unitOfWork.CloseConnection();

            // 2.1) Insert to queue to trigger subsequent processing.... DELETE IF NOT REQUIRED
            await _defaultAzureQueue.WriteToQueue("outputQueueName", "outputQueueMessageData");

            // 3) Performs standard Prime3 Post Processing of a qeueue (logging)
            await PostProcess(context);
        }
    }
}

