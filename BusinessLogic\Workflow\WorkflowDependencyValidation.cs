using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Azure;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow dependency validation operations
    /// </summary>
    public class WorkflowDependencyValidation : BusinessLogicBase
    {
        private readonly DefaultAzureQueue _azureQueue;

        public WorkflowDependencyValidation(ILogger<WorkflowDependencyValidation> logger, UtilityFunctions utilityFunctions, Cache cache, DefaultAzureQueue azureQueue)
        {
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _azureQueue = azureQueue;
        }

        /// <summary>
        /// Publish that a dependency has been met by writing to the 'dependency-met' Azure queue
        /// </summary>
        /// <param name="dependencyKey">The unique dependency key</param>
        /// <returns>Task</returns>
        public async Task PublishDependencyMetAsync(string dependencyKey)
        {
            if (string.IsNullOrWhiteSpace(dependencyKey))
            {
                throw new ArgumentException("Dependency key cannot be null or empty", nameof(dependencyKey));
            }

            _logger.LogInformation($"Publishing dependency met for key: {dependencyKey}");

            try
            {
                // Write the dependency key to the 'dependency-met' Azure queue
                await _azureQueue.WriteToQueue("dependency-met", dependencyKey, autoCreateQueue: true);

                _logger.LogInformation($"Successfully published dependency met for key: {dependencyKey} to dependency-met queue");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to publish dependency met for key: {dependencyKey}");
                throw;
            }
        }

    }
}
}
