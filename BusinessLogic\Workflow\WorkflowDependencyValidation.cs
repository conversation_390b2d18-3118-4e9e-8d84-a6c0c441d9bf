using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow dependency validation operations
    /// </summary>
    public class WorkflowDependencyValidation : BusinessLogicBase
    {
        public WorkflowDependencyValidation(IUnitOfWork unitOfWork, ILogger<WorkflowDependencyValidation> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Publish that a dependency has been met
        /// </summary>
        /// <param name="dependencyKey">The unique dependency key</param>
        /// <param name="completedAt">When the dependency was completed (optional, defaults to now)</param>
        /// <param name="data">Optional additional data about the dependency completion</param>
        /// <returns>The created dependency complete ID</returns>
        public async Task<long> PublishDependencyMetAsync(string dependencyKey, DateTimeOffset? completedAt = null, string? data = null)
        {
            if (string.IsNullOrWhiteSpace(dependencyKey))
            {
                throw new ArgumentException("Dependency key cannot be null or empty", nameof(dependencyKey));
            }

            _logger.LogInformation($"Publishing dependency met for key: {dependencyKey}");

            try
            {
                var dependencyCompleteDto = new DependencyCompleteDto
                {
                    DependencyKey = dependencyKey,
                    CompletedAt = completedAt ?? DateTimeOffset.UtcNow,
                    Data = data,
                    CreatedOn = DateTimeOffset.UtcNow
                };

                var dependencyCompleteId = await CreateDependencyCompleteAsync(dependencyCompleteDto);

                // Clear cache for any workflow jobs that might be waiting for this dependency
                _cache.ClearCacheForObject("WfJobDependency");
                _cache.ClearCacheForObject("DependencyComplete");

                // Check if any workflow jobs are waiting for this dependency
                await CheckAndUpdateWaitingJobsAsync(dependencyKey, dependencyCompleteDto.CompletedAt);

                _logger.LogInformation($"Successfully published dependency met for key: {dependencyKey} with ID: {dependencyCompleteId}");
                return dependencyCompleteId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to publish dependency met for key: {dependencyKey}");
                throw;
            }
        }

        /// <summary>
        /// Check if all dependencies for a workflow job are met
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <returns>True if all dependencies are met, false otherwise</returns>
        public async Task<bool> AreAllDependenciesMetAsync(long wfJobId)
        {
            _logger.LogDebug($"Checking if all dependencies are met for workflow job {wfJobId}");

            try
            {
                // Get all required dependencies for this job
                var requiredDependencies = await GetRequiredDependenciesAsync(wfJobId);

                if (!requiredDependencies.Any())
                {
                    // No dependencies required, so all are "met"
                    return true;
                }

                // Check each dependency
                foreach (var dependency in requiredDependencies)
                {
                    var isMetResult = await IsDependencyMetAsync(dependency.DependencyKey, dependency.RequiredDependencyDate);
                    if (!isMetResult)
                    {
                        _logger.LogDebug($"Dependency {dependency.DependencyKey} is not met for workflow job {wfJobId}");
                        return false;
                    }
                }

                _logger.LogDebug($"All dependencies are met for workflow job {wfJobId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check dependencies for workflow job {wfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Check if a specific dependency is met
        /// </summary>
        /// <param name="dependencyKey">The dependency key to check</param>
        /// <param name="requiredDate">The required completion date</param>
        /// <returns>True if the dependency is met, false otherwise</returns>
        public async Task<bool> IsDependencyMetAsync(string dependencyKey, DateTimeOffset requiredDate)
        {
            if (string.IsNullOrWhiteSpace(dependencyKey))
            {
                throw new ArgumentException("Dependency key cannot be null or empty", nameof(dependencyKey));
            }

            _logger.LogDebug($"Checking if dependency {dependencyKey} is met for date {requiredDate}");

            try
            {
                // Check cache first
                var cacheKey = $"{dependencyKey}_{requiredDate:yyyyMMddHHmmss}";
                var (exists, cachedResult) = await _cache.GetCacheItem<bool>("DependencyMet", cacheKey);
                if (exists)
                {
                    return cachedResult;
                }

                string sql = @"
                    SELECT COUNT(*)
                    FROM [workflow].[DependencyComplete]
                    WHERE [DependencyKey] = @dependencyKey 
                      AND [CompletedAt] >= @requiredDate";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                    command.AddArgument("requiredDate", requiredDate);

                    var count = await command.SelectSingle<int>();
                    var isMet = count > 0;

                    // Cache the result for a short time
                    await _cache.SetCacheItem("DependencyMet", cacheKey, isMet, cacheForMinutes: 2);

                    return isMet;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check if dependency {dependencyKey} is met");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task<long> CreateDependencyCompleteAsync(DependencyCompleteDto dependencyCompleteDto)
        {
            string sql = @"
                INSERT INTO [workflow].[DependencyComplete]
                ([DependencyKey], [CompletedAt], [Data], [CreatedOn])
                OUTPUT INSERTED.DependencyCompleteId
                VALUES
                (@DependencyKey, @CompletedAt, @Data, @CreatedOn)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dependencyCompleteDto);
                return await command.SelectSingle<long>();
            }
        }

        private async Task<List<WfJobDependencyDto>> GetRequiredDependenciesAsync(long wfJobId)
        {
            string sql = @"
                SELECT [WfJobDependencyId], [WfJobId], [DependencyKey], [RequiredDependencyDate]
                FROM [workflow].[WfJobDependency]
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                return await command.SelectMany<WfJobDependencyDto>();
            }
        }

        private async Task CheckAndUpdateWaitingJobsAsync(string dependencyKey, DateTimeOffset completedAt)
        {
            // Find workflow jobs that are waiting for this dependency
            string sql = @"
                SELECT DISTINCT wj.[WfJobId]
                FROM [workflow].[WfJob] wj
                INNER JOIN [workflow].[WfJobDependency] wjd ON wj.[WfJobId] = wjd.[WfJobId]
                WHERE wjd.[DependencyKey] = @dependencyKey
                  AND wjd.[RequiredDependencyDate] <= @completedAt
                  AND wj.[WfJobStatusId] = 5"; // Waiting for dependencies status

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("dependencyKey", dependencyKey);
                command.AddArgument("completedAt", completedAt);

                var waitingJobIds = await command.SelectMany<long>();

                // Check each waiting job to see if all its dependencies are now met
                foreach (var jobId in waitingJobIds)
                {
                    if (await AreAllDependenciesMetAsync(jobId))
                    {
                        // All dependencies are met, update the job status to ready
                        await UpdateJobStatusToReadyAsync(jobId);
                    }
                }
            }
        }

        private async Task UpdateJobStatusToReadyAsync(long wfJobId)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WfJobStatusId] = 1,
                    [NextCheckDependenciesAt] = NULL,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }

            _logger.LogInformation($"Updated workflow job {wfJobId} status to ready - all dependencies are now met");
        }

        #endregion
    }

    /// <summary>
    /// DTO for workflow job dependencies (placeholder - should match actual schema)
    /// </summary>
    public class WfJobDependencyDto
    {
        public long WfJobDependencyId { get; set; }
        public long WfJobId { get; set; }
        public string DependencyKey { get; set; } = string.Empty;
        public DateTimeOffset RequiredDependencyDate { get; set; }
    }
}
