using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for schedule type operations
    /// </summary>
    public class ScheduleType : BusinessLogicBase
    {
        public ScheduleType(IUnitOfWork unitOfWork, ILogger<ScheduleType> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single ScheduleType by ID
        /// </summary>
        /// <param name="scheduleTypeId">The schedule type ID</param>
        /// <returns>ScheduleTypeDto or null if not found</returns>
        public async Task<ScheduleTypeDto?> GetAsync(short scheduleTypeId)
        {
            _logger.LogDebug($"Getting schedule type for ID: {scheduleTypeId}");

            try
            {
                // Check cache first
                var cacheKey = $"ScheduleType_{scheduleTypeId}";
                var (exists, cachedType) = await _cache.GetCacheItem<ScheduleTypeDto>("ScheduleType", cacheKey);
                if (exists && cachedType != null)
                {
                    return cachedType;
                }

                string sql = @"
                    SELECT [ScheduleTypeId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[ScheduleType]
                    WHERE [ScheduleTypeId] = @scheduleTypeId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("scheduleTypeId", scheduleTypeId);
                    var scheduleType = await command.SelectSingle<ScheduleTypeDto>();

                    // Cache the result if found
                    if (scheduleType != null)
                    {
                        await _cache.SetCacheItem("ScheduleType", cacheKey, scheduleType, cacheForMinutes: 30);
                    }

                    return scheduleType;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get schedule type for ID: {scheduleTypeId}");
                throw;
            }
        }
    }
}
