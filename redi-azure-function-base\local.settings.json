{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=testredi1storage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=testredi1storage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ConnectionStringsBaseDbConn": "Data Source=test2.redisoftware.com.au,1434;Initial Catalog=connectsource;User ID=sa;Password=**************$45mKP*910aPF1wMsLrBDFeal;Integrated Security=False;multipleactiveresultsets=True;Connection Timeout=120", "SENTRY_TRACES_SAMPLE_RATE": 1, "SENTRY_LOG_LEVEL_DEFAULT": "Warning", "SENTRY_ENABLE_TRACING": false, "SENTRY_ENABLE_DEBUG": false, "Environment": "development", "SENTRY_DSN": ""}}