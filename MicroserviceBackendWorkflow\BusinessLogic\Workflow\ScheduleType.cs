using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class ScheduleType : BusinessLogicBase
    {
        public ScheduleType(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get a single ScheduleType by ID
        /// </summary>
        /// <param name="scheduleTypeId">The schedule type ID</param>
        /// <param name="ignoreErrorIfNotExists">If false, throws exception when not found</param>
        /// <returns>ScheduleTypeDto or null if not found</returns>
        internal async Task<ScheduleTypeDto?> GetAsync(short scheduleTypeId, bool ignoreErrorIfNotExists = false)
        {
            string sql = @"
                SELECT [ScheduleTypeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ScheduleType]
                WHERE [ScheduleTypeId] = @scheduleTypeId";

            string cacheKey = "ScheduleType" + scheduleTypeId;

            if (_memoryCache.TryGetValue(cacheKey, out ScheduleTypeDto? cacheValue))
            {
                if (cacheValue != null)
                {
                    return cacheValue.DeepClone();
                }
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleTypeId", scheduleTypeId);
                var result = await command.SelectSingle<ScheduleTypeDto>();

                if (result == null && !ignoreErrorIfNotExists)
                {
                    throw new HttpRequestException($"ScheduleType with ID '{scheduleTypeId}' not found", null, System.Net.HttpStatusCode.NotFound);
                }

                if (result != null)
                {
                    _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());
                }

                return result;
            }
        }

        /// <summary>
        /// Get a list of ScheduleTypes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of ScheduleTypes</returns>
        internal async Task<ListResponseDto<ScheduleTypeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ScheduleTypeId", "[ScheduleType].[ScheduleTypeId]" },
                { "Label", "[ScheduleType].[Label]" },
                { "IsEnabled", "[ScheduleType].[IsEnabled]" }
            };

            string sql = @"
                SELECT [ScheduleTypeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ScheduleType]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ScheduleTypeId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ScheduleTypeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<ScheduleTypeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[ScheduleType]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
