using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job audit operations
    /// </summary>
    public class JobAudit : BusinessLogicBase
    {
        public JobAudit(IUnitOfWork unitOfWork, ILogger<JobAudit> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a workflow job audit record by WfJobId and optional WorkflowStepId (no cache)
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="workflowStepId">The workflow step ID (optional)</param>
        /// <returns>JobAuditDto or null if not found</returns>
        public async Task<JobAuditDto?> GetAsync(long wfJobId, int? workflowStepId = null)
        {
            _logger.LogDebug($"Getting job audit record for WfJobId: {wfJobId}, WorkflowStepId: {workflowStepId}");

            try
            {
                var whereConditions = new List<string> { "[WfJobId] = @wfJobId" };

                if (workflowStepId.HasValue)
                {
                    whereConditions.Add("[WorkflowStepId] = @workflowStepId");
                }

                string sql = $@"
                    SELECT TOP 1 [WfJobAuditId]
                          ,[WfJobId]
                          ,[WorkflowStepId]
                          ,[QueueName]
                          ,[TenantId]
                          ,[QueuedTime]
                          ,[StartTime]
                          ,[EndTime]
                          ,[Information]
                    FROM [workflow].[WfJobAudit]
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY [WfJobAuditId] DESC";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("wfJobId", wfJobId);

                    if (workflowStepId.HasValue)
                    {
                        command.AddArgument("workflowStepId", workflowStepId.Value);
                    }

                    return await command.SelectSingle<JobAuditDto>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get job audit record for WfJobId: {wfJobId}, WorkflowStepId: {workflowStepId}");
                throw;
            }
        }

        /// <summary>
        /// Update an existing workflow job audit record
        /// </summary>
        /// <param name="dto">The job audit data to update</param>
        /// <returns>Task</returns>
        public async Task UpdateAsync(JobAuditDto dto)
        {
            _logger.LogDebug($"Updating job audit record with ID: {dto.WfJobAuditId}");

            try
            {
                // Validation
                if (dto.WfJobAuditId <= 0)
                {
                    throw new ArgumentException("WfJobAuditId must be greater than 0");
                }

                string sql = @"
                    UPDATE [workflow].[WfJobAudit]
                    SET [WorkflowStepId] = @WorkflowStepId,
                        [QueueName] = @QueueName,
                        [TenantId] = @TenantId,
                        [QueuedTime] = @QueuedTime,
                        [StartTime] = @StartTime,
                        [EndTime] = @EndTime,
                        [Information] = @Information
                    WHERE [WfJobAuditId] = @WfJobAuditId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
                {
                    command.AddArguments(dto);
                    await command.Execute();
                }

                _logger.LogInformation($"Successfully updated job audit record with ID: {dto.WfJobAuditId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to update job audit record with ID: {dto.WfJobAuditId}");
                throw;
            }
        }

        /// <summary>
        /// Create a new workflow job audit record
        /// </summary>
        /// <param name="dto">The job audit data</param>
        /// <returns>Task</returns>
        public async Task CreateAsync(JobAuditDto dto)
        {
            _logger.LogDebug($"Creating job audit record for WfJobId: {dto.WfJobId}");

            try
            {
                // Validation
                if (dto.WfJobId <= 0)
                {
                    throw new ArgumentException("WfJobId must be greater than 0");
                }

                // Set audit fields if not already set
                if (dto.QueuedTime == default)
                {
                    dto.QueuedTime = DateTimeOffset.UtcNow;
                }

                string sql = @"
                    INSERT INTO [workflow].[WfJobAudit]
                    ([WfJobId], [WorkflowStepId], [QueueName], [TenantId], [QueuedTime], 
                     [StartTime], [EndTime], [Information])
                    OUTPUT INSERTED.WfJobAuditId
                    VALUES
                    (@WfJobId, @WorkflowStepId, @QueueName, @TenantId, @QueuedTime, 
                     @StartTime, @EndTime, @Information)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(dto);
                    var auditId = await command.SelectSingle<long>();
                    dto.WfJobAuditId = auditId;
                }

                _logger.LogInformation($"Successfully created job audit record with ID: {dto.WfJobAuditId} for WfJobId: {dto.WfJobId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create job audit record for WfJobId: {dto.WfJobId}");
                throw;
            }
        }
    }
}
