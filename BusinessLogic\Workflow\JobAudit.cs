using RediAzurefunctionBase.Dtos;
using Sql;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class JobAudit : BusinessLogicBase
    {
        public JobAudit(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Create a new WfJobAudit record
        /// </summary>
        /// <param name="wfJobAuditDto">The job audit data to create</param>
        /// <returns>The created WfJobAudit ID</returns>
        internal async Task<long> CreateAsync(WfJobAuditDto wfJobAuditDto)
        {
            string sql = @"
                INSERT INTO [workflow].[WfJobAudit] (
                    [WfJobId],
                    [WorkflowStepId],
                    [StartTime],
                    [EndTime],
                    [Information],
                    [QueueName],
                    [TenantId],
                    [CreatedOn],
                    [CreatedByName],
                    [ModifiedOn],
                    [ModifiedByName]
                ) VALUES (
                    @WfJobId,
                    @WorkflowStepId,
                    @StartTime,
                    @EndTime,
                    @Information,
                    @QueueName,
                    @TenantId,
                    @CreatedOn,
                    @CreatedByName,
                    @ModifiedOn,
                    @ModifiedByName
                );
                SELECT SCOPE_IDENTITY();";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Insert, sql))
            {
                command.AddArgument("WfJobId", wfJobAuditDto.WfJobId);
                command.AddArgument("WorkflowStepId", wfJobAuditDto.WorkflowStepId);
                command.AddArgument("StartTime", wfJobAuditDto.StartTime);
                command.AddArgument("EndTime", wfJobAuditDto.EndTime);
                command.AddArgument("Information", wfJobAuditDto.Information ?? string.Empty);
                command.AddArgument("QueueName", wfJobAuditDto.QueueName ?? string.Empty);
                command.AddArgument("TenantId", wfJobAuditDto.TenantId);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", "System");
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("ModifiedByName", "System");

                var result = await command.SelectSingle<decimal>();
                return Convert.ToInt64(result);
            }
        }
    }
}
