using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job audit operations
    /// </summary>
    public class JobAudit : BusinessLogicBase
    {
        public JobAudit(IUnitOfWork unitOfWork, ILogger<JobAudit> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Create a new workflow job audit record
        /// </summary>
        /// <param name="dto">The job audit data</param>
        /// <returns>Task</returns>
        public async Task CreateAsync(JobAuditDto dto)
        {
            _logger.LogDebug($"Creating job audit record for WfJobId: {dto.WfJobId}");

            try
            {
                // Validation
                if (dto.WfJobId <= 0)
                {
                    throw new ArgumentException("WfJobId must be greater than 0");
                }

                // Set audit fields if not already set
                if (dto.QueuedTime == default)
                {
                    dto.QueuedTime = DateTimeOffset.UtcNow;
                }

                string sql = @"
                    INSERT INTO [workflow].[WfJobAudit]
                    ([WfJobId], [WorkflowStepId], [QueueName], [TenantId], [QueuedTime], 
                     [StartTime], [EndTime], [Information])
                    OUTPUT INSERTED.WfJobAuditId
                    VALUES
                    (@WfJobId, @WorkflowStepId, @QueueName, @TenantId, @QueuedTime, 
                     @StartTime, @EndTime, @Information)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(dto);
                    var auditId = await command.SelectSingle<long>();
                    dto.WfJobAuditId = auditId;
                }

                _logger.LogInformation($"Successfully created job audit record with ID: {dto.WfJobAuditId} for WfJobId: {dto.WfJobId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create job audit record for WfJobId: {dto.WfJobId}");
                throw;
            }
        }
    }
}
