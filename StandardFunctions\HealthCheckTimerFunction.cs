using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using Sql;

namespace RediAzurefunctionBase.Functions
{
    public class HealthCheckTimerFunction : Prime3FunctionTimerBase
    {
        public HealthCheckTimerFunction(IUnitOfWork unitOfWork
            , ILogger logger
            ,DefaultAzureQueue defaultAzureQueue
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _defaultAzureQueue = defaultAzureQueue;
        }

        /// <summary>
        /// Health Check Timer runs daily at 22:00 UTC
        /// Sends a message to the zhealthfunctionalive queue with the functions domain.
        /// Also writes to message log.
        /// Used to identify functions that are running in an environment.
        /// </summary>
        /// <param name="timeInfo"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        [Function(nameof(HealthCheckTimerFunction))]
        public async Task Run([TimerTrigger("0 22 * * * *")] TimerInfo timeInfo, FunctionContext context)
        {
            // 1) Performs Standard Prime3 Pre Processing of a Timer (log)
            await PreProcess(timeInfo, context);

            // 2) Insert to queue to trigger subsequent processing....
            await _defaultAzureQueue.WriteToQueue("zhealthfunctionalive", Environment.GetEnvironmentVariable("WEBSITE_HOSTNAME")!);

            // 3) Performs standard Prime3 Post Processing of a timer (logging)
            await PostProcess(timeInfo, context);
        }
    }
}

