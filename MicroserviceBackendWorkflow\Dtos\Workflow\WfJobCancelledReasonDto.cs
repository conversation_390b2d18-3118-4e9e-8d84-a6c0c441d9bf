using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Lookup table for workflow job cancellation reasons
    /// </summary>
    [Mappable(nameof(WfJobCancelledReasonId))]
    public class WfJobCancelledReasonDto
    {
        /// <summary>
        /// The unique identifier for the workflow job cancellation reason
        /// </summary>
        public byte WfJobCancelledReasonId { get; set; }
        
        /// <summary>
        /// The display label for the cancellation reason
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this cancellation reason is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// The sort order for displaying cancellation reasons
        /// </summary>
        public byte SortOrder { get; set; }
    }

    /// <summary>
    /// List DTO for WfJobCancelledReason
    /// </summary>
    public class WfJobCancelledReasonListDto : WfJobCancelledReasonDto
    {
    }
}
