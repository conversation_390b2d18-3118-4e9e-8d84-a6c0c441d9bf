using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for schedule mode operations
    /// </summary>
    public class ScheduleMode : BusinessLogicBase
    {
        public ScheduleMode(IUnitOfWork unitOfWork, ILogger<ScheduleMode> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single ScheduleMode by ID
        /// </summary>
        /// <param name="scheduleModeId">The schedule mode ID</param>
        /// <returns>ScheduleModeDto or null if not found</returns>
        public async Task<ScheduleModeDto?> GetAsync(short scheduleModeId)
        {
            _logger.LogDebug($"Getting schedule mode for ID: {scheduleModeId}");

            try
            {
                // Check cache first
                var cacheKey = $"ScheduleMode_{scheduleModeId}";
                var (exists, cachedMode) = await _cache.GetCacheItem<ScheduleModeDto>("ScheduleMode", cacheKey);
                if (exists && cachedMode != null)
                {
                    return cachedMode;
                }

                string sql = @"
                    SELECT [ScheduleModeId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[ScheduleMode]
                    WHERE [ScheduleModeId] = @scheduleModeId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("scheduleModeId", scheduleModeId);
                    var scheduleMode = await command.SelectSingle<ScheduleModeDto>();

                    // Cache the result if found
                    if (scheduleMode != null)
                    {
                        await _cache.SetCacheItem("ScheduleMode", cacheKey, scheduleMode, cacheForMinutes: 30);
                    }

                    return scheduleMode;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get schedule mode for ID: {scheduleModeId}");
                throw;
            }
        }
    }
}
