using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Workflow Job Status lookup table
    /// 1 - Scheduled,
    /// 2 - Delayed
    /// 3 - Check Conditions
    /// 4 - Executing
    /// 5 - Waiting
    /// 6 - Completed
    /// 7 - Cancelled
    /// 8 - Error
    /// </summary>
    [Mappable(nameof(WfJobStatusId))]
    public class WfJobStatusDto
    {
        /// <summary>
        /// The workflow job status identifier:
        /// 1 - Scheduled,
        /// 2 - Delayed
        /// 3 - Check Conditions
        /// 4 - Executing
        /// 5 - Waiting
        /// 6 - Completed
        /// 7 - Cancelled
        /// 8 - Error
        /// </summary>
        public byte WfJobStatusId { get; set; }
        
        /// <summary>
        /// The display label for the workflow job status
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this workflow job status is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// The sort order for displaying workflow job statuses
        /// </summary>
        public byte SortOrder { get; set; }
    }

    /// <summary>
    /// List DTO for WfJobStatus
    /// </summary>
    public class WfJobStatusListDto : WfJobStatusDto
    {
    }
}
