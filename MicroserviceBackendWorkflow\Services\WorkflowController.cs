using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendWorkflow.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get and Manage Workflow Definitions.
    /// </summary>
    [Route("api/Workflow")]
    public class WorkflowController : AppController
    {
        private readonly BusinessLogic.Workflow _workflow;

        public WorkflowController(BusinessLogic.Workflow workflow, IUnitOfWork unitOfWork)
        {
            _workflow = workflow;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Workflow
        /// </summary>
        /// <remarks>
        /// Returns a single Workflow record for a given Workflow ID.
        /// </remarks>
        /// <param name="workflowId">The ID of a workflow record</param>
        /// <response code="200">Workflow returned, or null if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(WorkflowDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int workflowId)
        {
            var result = await _workflow.GetAsync(workflowId);
            return Ok(result);
        }

        /// <summary>
        /// Create a Workflow
        /// </summary>
        /// <remarks>
        /// Create a new Workflow definition
        /// </remarks>
        /// <param name="dto">A Workflow to be created</param>
        /// <response code="200">Workflow created</response>
        /// <response code="422">Workflow could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WorkflowDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] WorkflowDto dto)
        {
            await _workflow.CreateAsync(dto);
            _unitOfWork.Commit();
            var result = await _workflow.GetAsync(dto.WorkflowId);
            return Ok(result);
        }

        /// <summary>
        /// Update a Workflow
        /// </summary>
        /// <remarks>
        /// Update an existing Workflow definition
        /// </remarks>
        /// <param name="dto">The Workflow data to update</param>
        /// <response code="200">Workflow updated</response>
        /// <response code="422">Workflow could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WorkflowDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] WorkflowDto dto)
        {
            await _workflow.UpdateAsync(dto);
            _unitOfWork.Commit();
            var result = await _workflow.GetAsync(dto.WorkflowId);
            return Ok(result);
        }

        /// <summary>
        /// Delete a Workflow
        /// </summary>
        /// <remarks>
        /// Delete a Workflow with the given ID.
        /// </remarks>
        /// <param name="workflowId">The ID of a workflow record</param>
        /// <response code="200">Workflow has been deleted</response>
        /// <response code="404">Workflow not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] int workflowId)
        {
            await _workflow.DeleteAsync(workflowId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Workflows
        /// </summary>
        /// <remarks>
        /// Returns a list of Workflows with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <response code="200">List of Workflows returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<WorkflowListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? tenantId = null, bool? isEnabled = null)
        {
            var result = await _workflow.GetListAsync(standardListParameters, tenantId, isEnabled);
            return Ok(result);
        }
    }
}
