using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WfJobData : BusinessLogicBase
    {
        public WfJobData(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WfJobData by WfJobId
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <returns>WfJobDataDto or null if not found</returns>
        internal async Task<WfJobDataDto?> GetAsync(long wfJobId)
        {
            string sql = @"
                SELECT [WfJobId]
                      ,[Data]
                FROM [workflow].[WfJobData]
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                return await command.SelectSingle<WfJobDataDto>();
            }
        }

        /// <summary>
        /// Get a list of WfJobData with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="hasData">Filter by whether data exists</param>
        /// <returns>List of WfJobData</returns>
        internal async Task<ListResponseDto<WfJobDataListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool? hasData = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobId", "[WfJobData].[WfJobId]" }
            };

            string sql = @"
                SELECT [WfJobId]
                      ,[Data]
                FROM [workflow].[WfJobData]
                WHERE 1=1";

            if (hasData.HasValue)
            {
                if (hasData.Value)
                {
                    sql += " AND [Data] IS NOT NULL AND [Data] != ''";
                }
                else
                {
                    sql += " AND ([Data] IS NULL OR [Data] = '')";
                }
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WfJobId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobDataListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<WfJobDataListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJobData]
                WHERE 1=1";

            if (hasData.HasValue)
            {
                if (hasData.Value)
                {
                    countSql += " AND [Data] IS NOT NULL AND [Data] != ''";
                }
                else
                {
                    countSql += " AND ([Data] IS NULL OR [Data] = '')";
                }
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
