using Newtonsoft.Json;

namespace RediAzurefunctionBase.Dtos
{
    public static class ObjectExtensions
    {
        public static T DeepClone<T>(this T source)
        {
            if (ReferenceEquals(source, null))
            {
                return default;
            }

            var deserializeSettings = new JsonSerializerSettings
            {
                //IncludeFields = true,
                //PropertyNameCaseInsensitive = true
            };

            return JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(source));
        }
    }
}
