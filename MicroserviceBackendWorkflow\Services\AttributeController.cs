using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get Workflow Attribute lookup data.
    /// </summary>
    [Route("api/Attribute")]
    public class AttributeController : AppController
    {
        private readonly BusinessLogic.Attribute _attribute;

        public AttributeController(BusinessLogic.Attribute attribute, IUnitOfWork unitOfWork)
        {
            _attribute = attribute;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Attribute
        /// </summary>
        /// <remarks>
        /// Returns a single Attribute record for a given code.
        /// </remarks>
        /// <param name="attributeCode">The code of an attribute record</param>
        /// <response code="200">Attribute returned, or null if not found</response>
        /// <response code="404">Attribute not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(AttributeDto), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(string attributeCode)
        {
            var result = await _attribute.GetAsync(attributeCode);
            return Ok(result);
        }

        /// <summary>
        /// Get a List of Attributes
        /// </summary>
        /// <remarks>
        /// Returns a list of Attributes with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="attributeValueTypeCode">Filter by attribute value type code</param>
        /// <response code="200">List of Attributes returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<AttributeListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters? standardListParameters = null, string? attributeValueTypeCode = null)
        {
            var result = await _attribute.GetListAsync(standardListParameters, attributeValueTypeCode);
            return Ok(result);
        }
    }
}
