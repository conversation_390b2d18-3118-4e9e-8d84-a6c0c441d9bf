﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	<VersionPrefix>1.0.4</VersionPrefix>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Title>Redi Prime3 MicroService Base Library</Title>
    <Authors>Redi Software</Authors>
    <Company>Redi Software</Company>
    <Copyright>Redi Software 2025</Copyright>
    <PackageId>Redi.Prime3.MicroService.BaseLib</PackageId>
    <PackageTags>MicroService Base</PackageTags>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.21.0" />
    <PackageReference Include="Dapr.AspNetCore" Version="1.14.0" />
    <PackageReference Include="Dapr.Extensions.Configuration" Version="1.14.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.9.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.1.0-preview.1.25064.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Redi.Prime3.MicroService.Logger" Version="1.0.0-8" />
    <PackageReference Include="Redi.Select.Filter" Version="1.0.7" />
    <PackageReference Include="Redi.Sql" Version="8.0.6" />
    <PackageReference Include="Scalar.AspNetCore" Version="1.2.72" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.2.0" />
  </ItemGroup>

</Project>
