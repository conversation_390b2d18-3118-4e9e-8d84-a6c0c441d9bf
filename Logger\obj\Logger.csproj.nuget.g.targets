﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)sentry\3.36.0\buildTransitive\Sentry.targets" Condition="Exists('$(NuGetPackageRoot)sentry\3.36.0\buildTransitive\Sentry.targets')" />
  </ImportGroup>
</Project>