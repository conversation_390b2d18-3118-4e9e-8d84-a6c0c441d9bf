using MicroserviceBackendWorkflow.Enums;

namespace MicroserviceBackendWorkflow.Policies
{
    public static class PolicyType
    {
        //Global Admin (catch-all for any calls to be blocked unless the user had this claim or a new claim is added for that particular call)
        public const string GlobalAdminPolicy = "GlobalAdminPolicy";

        public static List<PolicyTypeDefinition> List = new List<PolicyTypeDefinition>()
        {
            new PolicyTypeDefinition(GlobalAdminPolicy, (policy) => policy.RequireClaim("Global Admin"))
        };
    }
}
