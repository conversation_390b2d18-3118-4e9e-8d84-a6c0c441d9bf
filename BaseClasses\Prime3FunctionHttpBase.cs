using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Azure Http Triggered Functions.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class Prime3FunctionHttpBase : Prime3FunctionBase
	{ 
        /// <summary>
        /// Prime3 Function standard pre processing of http request.<para/>
        /// 1) log blob and container names
        /// </summary>
        /// <param name="request">http request</param>
        /// <param name="context">function context</param>
        /// <returns></returns>
        public async Task PreProcess(HttpRequest request, FunctionContext context)
        {
            var queryString = string.Join("&", request.Query.Select(q => $"{q.Key}={q.Value}"));
            _logger.LogInformation($"{context.FunctionDefinition.Name} Http Function Triggered query: {queryString}");
        }

        
        /// <summary>
        /// Do Timer Post processing.<para/>
        /// 1) Log timer stats
        /// </summary>
        /// <param name="context">function context</param>
        /// <param name="request">HttpRequest</param>
        public async Task PostProcess(HttpRequest request, FunctionContext context)
        {
            var queryString = string.Join("&", request.Query.Select(q => $"{q.Key}={q.Value}"));
            _logger.LogInformation($"{context.FunctionDefinition.Name} Http Function Triggered query: {queryString}");
        }

        
    }
}
