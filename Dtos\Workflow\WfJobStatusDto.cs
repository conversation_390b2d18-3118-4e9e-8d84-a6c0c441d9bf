using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// DTO for workflow job status lookup table
    /// </summary>
    [Mappable(nameof(WfJobStatusId))]
    public class WfJobStatusDto
    {
        /// <summary>
        /// The unique identifier for the workflow job status
        /// </summary>
        public short WfJobStatusId { get; set; }

        /// <summary>
        /// The display label for the workflow job status
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// Indicates whether this workflow job status is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// The sort order for displaying workflow job statuses
        /// </summary>
        public short SortOrder { get; set; }

        /// <summary>
        /// Detailed description of the workflow job status
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// List DTO for WfJobStatus
    /// </summary>
    public class WfJobStatusListDto : WfJobStatusDto
    {
    }
}
