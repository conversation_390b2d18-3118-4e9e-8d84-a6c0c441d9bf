using Microsoft.Extensions.Logging;
using Sql;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// <PERSON>les inserting records into the common.Event table
    /// Common Event is used to record events for any Entity Type.
    /// </summary>
    public class CommonEvent : BusinessLogicBase
    {
        private readonly ILogger _logger;
        private readonly UtilityFunctions _utils;
        /// <summary>
        /// <PERSON>les inserting records into the common.Event table
        /// Common Event is used to record events for any Entity Type.
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="utils"></param>
        /// <param name="unitOfWork"></param>
        public CommonEvent(ILogger logger, UtilityFunctions utils, IUnitOfWork unitOfWork)
        {
            _logger = logger;
            _utils = utils;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// Insert a record into the common.Event table if the table exists
        /// parentEntityId or parentEntityIntId must be provided
        /// The Event table is used to track events across any entity type
        /// </summary>
        /// <param name="description">Description of the event. Max length is 4000 characters</param>
        /// <param name="parentEntityId">Guid of the parent entity</param>
        /// <param name="parentEntityIntId">Int of the parent entity</param>
        /// <param name="parentEntityType">Type of the parent entity</param>
        /// <returns></returns>
        public async Task CreateEvent(string? description, string parentEntityType, Guid? parentEntityId = null, long? parentEntityIntId = null)
        {
            if (parentEntityId == null && parentEntityIntId == null)
            {
                throw new ApiErrorException("ParentEntityId or ParentEntityIntId must be provided");
            }
            if (description == null)
            {
                throw new ApiErrorException("Description must be provided");
            }
            
            string sql = @$"
IF EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='common' and t.name='Event')
INSERT INTO [common].[Event]
                ([Description]
                {(parentEntityId != null ? ",[ParentEntityId]" : "")}
                {(parentEntityIntId != null ? ",[ParentEntityIntId]" : "")}
            ,[ParentEntityType]
            ,[CreatedOn]
            ,[CreatedByName])
        VALUES
            (@Description
            {(parentEntityId != null ? ",@ParentEntityId" : "")}
            {(parentEntityIntId != null ? ",@ParentEntityIntId" : "")}
            ,@ParentEntityType
            ,@CreatedOn
            ,@CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("Description", description.Length > 4000 ? description.Substring(0, 4000) : description );
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("ParentEntityIntId", parentEntityIntId);
                command.AddArgument("ParentEntityType", parentEntityType);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);

                await command.Execute();
            }



        }
    }
}
