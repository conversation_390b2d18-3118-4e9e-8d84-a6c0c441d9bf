{"Dsn": "", "TracesSampleRate": 1, "LogLevelDefault": "Information", "ConnectionStringsBaseDbConn": "Data Source=host.docker.internal,1433;Initial Catalog=base;User=paul1;Password=**********;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;application name=workflow;", "AuthJwtKey": "dX&4<73SGL$MR'[dh#wbj8ZQGSh,]xLgZmKrcnPgy>P4[rPruQkmh(2y~#avFkr3&'nhpRdddkc8_QDP", "AuthJwtIssuer": "http://rediapps.com.au"}