using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// The frequency something repeats or recurs at
    /// </summary>
    [Mappable(nameof(RecurrenceFrequencyId))]
    public class RecurrenceFrequencyDto
    {
        /// <summary>
        /// The frequency something repeats or recurs at.
        /// 1 - Daily, 2 - Weekly, 3 - Monthly, 4 - Yearly, 5 - Every X Hours, 6 - Every X Minutes
        /// </summary>
        public short RecurrenceFrequencyId { get; set; }
        
        /// <summary>
        /// The display label for the recurrence frequency
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this recurrence frequency is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for RecurrenceFrequency
    /// </summary>
    public class RecurrenceFrequencyListDto : RecurrenceFrequencyDto
    {
    }
}
