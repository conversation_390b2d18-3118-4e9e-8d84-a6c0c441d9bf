﻿using Azure.Storage.Queues;
using Newtonsoft.Json;
using System.Text;
using System.Text.RegularExpressions;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for writing to Azure Storage Queue
    /// Create a class extending AzureQueueBase to use the Queue Writing ability
    /// </summary>
    public abstract class AzureQueueBase : BusinessLogicBase
    {
        protected QueueServiceClient? _queueServiceClient;

#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task InsertIntoQueue(object message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null)
        {
            await InsertIntoQueue(JsonConvert.SerializeObject(message), queueName, timeout, delay);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage

        /// <summary>
        /// Write a message to Azure Storage Queue
        /// </summary>
        /// <param name="message"></param>
        /// <param name="queueName"></param>
        /// <param name="timeout"></param>
        /// <param name="delay"></param>
        /// <param name="autoCreateQueue"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task InsertIntoQueue(string message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null, bool autoCreateQueue = true)
        {
            if (queueName == string.Empty || queueName == null)
            {
                throw new Exception("Queue name was not given");
            }
            queueName = ValidateQueueName(queueName);
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            var queueClient = _queueServiceClient.GetQueueClient(queueName);

            if (!await queueClient.ExistsAsync())
            {
                if (autoCreateQueue)
                {
                    await _queueServiceClient.CreateQueueAsync(queueName);
                }
                else
                {
                    throw new Exception($"Queue of name {queueName} does not exist.");
                }
            }

            var messageBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(message));
            await queueClient.SendMessageAsync(messageBase64, timeout, delay);
        }

        /// <summary>
        /// Validates a QueueName. 
        /// Returns a lowecase queue name.
        /// </summary>
        /// <param name="queueName"></param>
        /// <returns></returns>
        /// <exception cref="Exception">When queue name is not valid</exception>
        private string ValidateQueueName(string queueName)
        {
            queueName = queueName.ToLower(); // Auto set to lowercase
            string pattern = @"^[a-z0-9][a-z0-9-]{3,62}$";
            if (Regex.IsMatch(queueName, pattern) == false)
            {
                throw new Exception($"Storage Queue Name '{queueName}' is not valid. Must be alphanumeric, and dashes (within) with length 3 to 63.");
            }

            return queueName;
        }

        /// <summary>
        /// Test the storage account connection by writing to a test queue.
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            try
            {
                var testQueueName = "ztest-def-" + StartupBase.AppId;
                if (testQueueName.Length >63 ) { testQueueName = testQueueName.Substring(0, 63); }
                await InsertIntoQueue("Test Message", testQueueName, autoCreateQueue: true);
            }
            catch (Exception ex)
            {
                throw new StartupException($"Azure Queue Storage Connection failed for AccountName:{_queueServiceClient.AccountName}. Error: {ex.Message}. ");
            }
        }
    }
}

