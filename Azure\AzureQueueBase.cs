using Azure.Storage.Queues;
using Newtonsoft.Json;
using System.Text;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Azure
{
    /// <summary>
    /// Base class for writing to Azure Storage Queue
    /// Create a class extending AzureQueueBase to use the Queue Writing ability
    /// </summary>
    public abstract class AzureQueueBase : BusinessLogicBase
    {
        protected QueueServiceClient? _queueServiceClient;

#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task InsertIntoQueue(object message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null)
        {
            await InsertIntoQueue(JsonConvert.SerializeObject(message), queueName, timeout, delay);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage

        public async Task InsertIntoQueue(string message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null, bool autoCreateQueue = false)
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("queueName cannot be null or empty");
            }

            if (string.IsNullOrEmpty(message))
            {
                throw new ArgumentException("message cannot be null or empty");
            }

            try
            {
                var queueClient = _queueServiceClient.GetQueueClient(queueName);

                if (autoCreateQueue)
                {
                    await queueClient.CreateIfNotExistsAsync();
                }

                var messageBytes = Encoding.UTF8.GetBytes(message);
                var base64Message = Convert.ToBase64String(messageBytes);

                if (delay.HasValue)
                {
                    await queueClient.SendMessageAsync(base64Message, visibilityTimeout: delay, timeToLive: timeout);
                }
                else
                {
                    await queueClient.SendMessageAsync(base64Message, timeToLive: timeout);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to insert message into queue '{queueName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Test the storage account connection by writing to a test queue.
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            try
            {
                var testQueueName = "ztest-wf-coordinator";
                if (testQueueName.Length > 63) { testQueueName = testQueueName.Substring(0, 63); }
                await InsertIntoQueue("Test Message", testQueueName, autoCreateQueue: true);
            }
            catch (Exception ex)
            {
                throw new Exception($"Azure Queue Storage Connection failed for AccountName:{_queueServiceClient.AccountName}. Error: {ex.Message}. ");
            }
        }

        /// <summary>
        /// Get the number of messages in a queue
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <returns>The approximate number of messages in the queue</returns>
        public async Task<int> GetQueueMessageCount(string queueName)
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("queueName cannot be null or empty");
            }

            try
            {
                var queueClient = _queueServiceClient.GetQueueClient(queueName);
                var properties = await queueClient.GetPropertiesAsync();
                return properties.Value.ApproximateMessagesCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get message count for queue '{queueName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Check if a queue exists
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <returns>True if the queue exists, false otherwise</returns>
        public async Task<bool> QueueExists(string queueName)
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("queueName cannot be null or empty");
            }

            try
            {
                var queueClient = _queueServiceClient.GetQueueClient(queueName);
                var response = await queueClient.ExistsAsync();
                return response.Value;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Create a queue if it doesn't exist
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <returns>True if the queue was created, false if it already existed</returns>
        public async Task<bool> CreateQueueIfNotExists(string queueName)
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("queueName cannot be null or empty");
            }

            try
            {
                var queueClient = _queueServiceClient.GetQueueClient(queueName);
                var response = await queueClient.CreateIfNotExistsAsync();
                return response != null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create queue '{queueName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Delete a queue
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <returns>Task</returns>
        public async Task DeleteQueue(string queueName)
        {
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            if (string.IsNullOrEmpty(queueName))
            {
                throw new ArgumentException("queueName cannot be null or empty");
            }

            try
            {
                var queueClient = _queueServiceClient.GetQueueClient(queueName);
                await queueClient.DeleteIfExistsAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to delete queue '{queueName}': {ex.Message}", ex);
            }
        }
    }
}
