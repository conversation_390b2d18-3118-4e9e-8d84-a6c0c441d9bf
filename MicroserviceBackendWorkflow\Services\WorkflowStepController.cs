using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendWorkflow.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get and Manage Workflow Steps.
    /// </summary>
    [Route("api/WorkflowStep")]
    public class WorkflowStepController : AppController
    {
        private readonly WorkflowStep _workflowStep;

        public WorkflowStepController(WorkflowStep workflowStep, IUnitOfWork unitOfWork)
        {
            _workflowStep = workflowStep;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Workflow Step
        /// </summary>
        /// <remarks>
        /// Returns a single Workflow Step record for a given Step ID.
        /// </remarks>
        /// <param name="workflowStepId">The ID of a workflow step record</param>
        /// <response code="200">Workflow Step returned, or null if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(WorkflowStepDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int workflowStepId)
        {
            var result = await _workflowStep.GetAsync(workflowStepId);
            return Ok(result);
        }

        /// <summary>
        /// Create a Workflow Step
        /// </summary>
        /// <remarks>
        /// Create a new Workflow Step
        /// </remarks>
        /// <param name="dto">A Workflow Step to be created</param>
        /// <response code="200">Workflow Step created</response>
        /// <response code="422">Workflow Step could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WorkflowStepDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] WorkflowStepDto dto)
        {
            await _workflowStep.CreateAsync(dto);
            _unitOfWork.Commit();
            var result = await _workflowStep.GetAsync(dto.WorkflowStepId);
            return Ok(result);
        }

        /// <summary>
        /// Update a Workflow Step
        /// </summary>
        /// <remarks>
        /// Update an existing Workflow Step
        /// </remarks>
        /// <param name="dto">The Workflow Step data to update</param>
        /// <response code="200">Workflow Step updated</response>
        /// <response code="422">Workflow Step could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WorkflowStepDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] WorkflowStepDto dto)
        {
            await _workflowStep.UpdateAsync(dto);
            _unitOfWork.Commit();
            var result = await _workflowStep.GetAsync(dto.WorkflowStepId);
            return Ok(result);
        }

        /// <summary>
        /// Delete a Workflow Step
        /// </summary>
        /// <remarks>
        /// Delete a Workflow Step with the given ID.
        /// </remarks>
        /// <param name="workflowStepId">The ID of a workflow step record</param>
        /// <response code="200">Workflow Step has been deleted</response>
        /// <response code="404">Workflow Step not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] int workflowStepId)
        {
            await _workflowStep.DeleteAsync(workflowStepId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Workflow Steps
        /// </summary>
        /// <remarks>
        /// Returns a list of Workflow Steps with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="workflowStepTypeId">Filter by workflow step type ID</param>
        /// <response code="200">List of Workflow Steps returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<WorkflowStepListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? workflowId = null, short? workflowStepTypeId = null)
        {
            var result = await _workflowStep.GetListAsync(standardListParameters, workflowId, workflowStepTypeId);
            return Ok(result);
        }
    }
}
