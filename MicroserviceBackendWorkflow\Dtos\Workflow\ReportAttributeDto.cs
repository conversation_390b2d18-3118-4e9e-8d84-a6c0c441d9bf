using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Supports having different attributes against a report
    /// </summary>
    [Mappable(nameof(ReportAttributeId))]
    public class ReportAttributeDto
    {
        /// <summary>
        /// The unique identifier for the report attribute
        /// </summary>
        public long ReportAttributeId { get; set; }
        
        /// <summary>
        /// The report this attribute belongs to
        /// </summary>
        public int ReportId { get; set; }
        
        /// <summary>
        /// The attribute code that defines what type of attribute this is
        /// </summary>
        public string AttributeCode { get; set; } = string.Empty;
        
        /// <summary>
        /// String value for the attribute (up to 100 characters)
        /// </summary>
        public string? ValueString { get; set; }
        
        /// <summary>
        /// Large string value for the attribute (unlimited length)
        /// </summary>
        public string? ValueStringMax { get; set; }
        
        /// <summary>
        /// Numeric value for the attribute
        /// </summary>
        public decimal? ValueNumeric { get; set; }
        
        /// <summary>
        /// Date/time value for the attribute
        /// </summary>
        public DateTimeOffset? ValueDateTime { get; set; }
    }

    /// <summary>
    /// List DTO for ReportAttribute
    /// </summary>
    public class ReportAttributeListDto : ReportAttributeDto
    {
    }
}
