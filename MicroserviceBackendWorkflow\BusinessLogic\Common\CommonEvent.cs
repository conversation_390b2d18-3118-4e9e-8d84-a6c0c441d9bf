using Redi.Prime3.MicroService.BaseLib;
using Sql;

namespace MicroserviceBackendWorkflow.BusinessLogic.Common
{
    /// <summary>
    /// 
    /// </summary>
    public class CommonEvent : BusinessLogicBase
    {
        private readonly ILogger _logger;
        private readonly UtilityFunctions _utils;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="utils"></param>
        public CommonEvent(ILogger logger, UtilityFunctions utils, IUnitOfWork unitOfWork)
        {
            _logger = logger;
            _utils = utils;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// 
        /// </summary>
        internal async Task CreateAsync(string description, string parentEntityType, Guid? parentEntityId = null, int? parentEntityIntId = null)
        {
            if (!ConfigBase.Schemas.ContainsKey("common")) { throw new HttpRequestException($"Common schema does not exist for CommonEvent.CreateAsync", null, System.Net.HttpStatusCode.UnprocessableEntity); }
            string sql = @"
INSERT INTO [common].[Event]
            ([Description]
            ,[ParentEntityId]
            ,[ParentEntityIntId]
            ,[ParentEntityType]
            ,[CreatedOn]
            ,[CreatedByName])
        VALUES
            (@Description
            ,@ParentEntityId
            ,@ParentEntityIntId
            ,@ParentEntityType
            ,@CreatedOn
            ,@CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArgument("Description", description);
                command.AddArgument("ParentEntityId", parentEntityId);
                command.AddArgument("ParentEntityType", parentEntityType);
                command.AddArgument("ParentEntityIntId", parentEntityIntId);
                command.AddArgument("CreatedOn", DateTimeOffset.UtcNow);
                command.AddArgument("CreatedByName", _utils.UserFullName);

                await command.Execute();
            }
        }
    }
}
