namespace Redi.Prime3.MicroService.BaseLib
{
	/// <summary>
	/// This class holds the App ID's for microservices used in this application
	/// </summary>
	public class MicroserviceConstantsBase
	{
		/// <summary>
		/// CRM Microservice App ID
		/// </summary>
		public static readonly string CrmAppId = "redi-microservice-crm";
		/// <summary>
		/// Common Microservice App ID
		/// </summary>
		public static readonly string CommonAppId = "redi-microservice-common";
		/// <summary>
		/// Listing Microservice App ID
		/// </summary>
		public static readonly string ListingAppId = "redi-microservice-listing";
		/// <summary>
		/// User Microservice App ID
		/// </summary>
		public static readonly string UserAppId = "redi-microservice-user";
		/// <summary>
		/// Bill of Materials Microservice App ID
		/// </summary>
		public static readonly string BillOfMaterialsAppId = "redi-microservice-billofmaterials";
		/// <summary>
		/// Connector Microservice App ID
		/// </summary>
		public static readonly string ConnectorAppId = "redi-microservice-connector";
		/// <summary>
		/// Form Microservice App ID
		/// </summary>
		public static readonly string FormAppId = "redi-microservice-form";
		/// <summary>
		/// Order Microservice App ID
		/// </summary>
		public static readonly string OrderAppId = "redi-microservice-order";
		/// <summary>
		/// Payment Microservice App ID
		/// </summary>
		public static readonly string PaymentAppId = "redi-microservice-payment";
		/// <summary>
		/// Product Microservice App ID
		/// </summary>
		public static readonly string ProductAppId = "redi-microservice-product";
		/// <summary>
		/// Reporting Microservice App ID
		/// </summary>
		public static readonly string ReportingAppId = "redi-microservice-reporting";
    }
}