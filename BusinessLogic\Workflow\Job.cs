using RediAzurefunctionBase.Dtos;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class Job : BusinessLogicBase
    {
        public Job(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get scheduled jobs that have status of Scheduled (1), Delayed (2), or Waiting (5)
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <returns>List of scheduled jobs</returns>
        internal async Task<ListResponseDto<WfJobListDto>> GetScheduledJobsAsync(
            StandardListParameters? standardListParameters = null,
            int? tenantId = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobId", "[WfJob].[WfJobId]" },
                { "Name", "[WfJob].[Name]" },
                { "ScheduleId", "[WfJob].[ScheduleId]" },
                { "WorkflowId", "[WfJob].[WorkflowId]" },
                { "WfJobStatusId", "[WfJob].[WfJobStatusId]" },
                { "ScheduledAt", "[WfJob].[ScheduledAt]" },
                { "CreatedOn", "[WfJob].[CreatedOn]" },
                { "ModifiedOn", "[WfJob].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [WfJobId], [Name], [ScheduleId], [WorkflowId], [WorkflowConditionId], [ScheduledAt], [IsScheduled],
                       [WfJobStatusId], [SchedulePurposeId], [OwningPartyId], [CompletedAt], [NextCheckDependenciesAt],
                       [TimeoutDependencyWaitAt], [WorkflowStepId], [WfJobCancelledReasonId], [TenantId],
                       [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted]
                FROM [workflow].[WfJob]
                WHERE [Deleted] = 0
                  AND [WfJobStatusId] IN (1, 2, 5)"; // Scheduled (1), Delayed (2), Waiting (5)

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WfJobId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                
                result.List = await command.SelectMany<WfJobListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJob]
                WHERE [Deleted] = 0
                  AND [WfJobStatusId] IN (1, 2, 5)"; // Scheduled (1), Delayed (2), Waiting (5)

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
