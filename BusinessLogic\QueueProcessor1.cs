using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using RediAzurefunctionBase.Dtos;
using Sql;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class QueueProcessor1 : BusinessLogicBase
    {
        public QueueProcessor1(ILogger logger, IUnitOfWork unitOfWork, Cache cache)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _cache = cache;
        }
        public async Task<string> ProcessQueueItem(QueueFunction1Dto request)
        {
            return "";
        }
    }
}
