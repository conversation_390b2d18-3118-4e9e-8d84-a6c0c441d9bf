using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Defines attributes that can be associated with various entities in the workflow system
    /// </summary>
    [Mappable(nameof(AttributeCode))]
    public class AttributeDto
    {
        /// <summary>
        /// The unique code that identifies the attribute
        /// </summary>
        public string AttributeCode { get; set; } = string.Empty;
        
        /// <summary>
        /// The display label for the attribute
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// The code that defines what type of value this attribute can hold (references AttributeValueType table)
        /// </summary>
        public string AttributeValueTypeCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// List DTO for Attribute
    /// </summary>
    public class AttributeListDto : AttributeDto
    {
    }
}
