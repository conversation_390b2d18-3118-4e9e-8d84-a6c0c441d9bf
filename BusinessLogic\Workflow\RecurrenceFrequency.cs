using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for recurrence frequency operations
    /// </summary>
    public class RecurrenceFrequency : BusinessLogicBase
    {
        public RecurrenceFrequency(IUnitOfWork unitOfWork, ILogger<RecurrenceFrequency> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single RecurrenceFrequency by ID
        /// </summary>
        /// <param name="recurrenceFrequencyId">The recurrence frequency ID</param>
        /// <param name="ignoreErrorIfNotExists">If true, returns null when not found instead of throwing an exception</param>
        /// <returns>RecurrenceFrequencyDto or null if not found</returns>
        public async Task<RecurrenceFrequencyDto?> GetAsync(short recurrenceFrequencyId, bool ignoreErrorIfNotExists = false)
        {
            _logger.LogDebug($"Getting recurrence frequency for ID: {recurrenceFrequencyId}");

            try
            {
                // Check cache first
                var cacheKey = $"RecurrenceFrequency_{recurrenceFrequencyId}";
                var (exists, cachedFrequency) = await _cache.GetCacheItem<RecurrenceFrequencyDto>("RecurrenceFrequency", cacheKey);
                if (exists && cachedFrequency != null)
                {
                    return cachedFrequency;
                }

                string sql = @"
                    SELECT [RecurrenceFrequencyId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[RecurrenceFrequency]
                    WHERE [RecurrenceFrequencyId] = @recurrenceFrequencyId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("recurrenceFrequencyId", recurrenceFrequencyId);
                    var recurrenceFrequency = await command.SelectSingle<RecurrenceFrequencyDto>();

                    // Cache the result if found
                    if (recurrenceFrequency != null)
                    {
                        await _cache.SetCacheItem("RecurrenceFrequency", cacheKey, recurrenceFrequency, cacheForMinutes: 30);
                    }
                    else if (!ignoreErrorIfNotExists)
                    {
                        throw new ArgumentException($"RecurrenceFrequency with ID {recurrenceFrequencyId} not found");
                    }

                    return recurrenceFrequency;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get recurrence frequency for ID: {recurrenceFrequencyId}");
                throw;
            }
        }
    }
}
