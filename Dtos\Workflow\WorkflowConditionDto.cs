using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// This is a generic Condition table. 
    /// This can be used for determining if a next step within a workflow should be executed. It can also control if a scheduled job meets conditions to be run.
    /// </summary>
    [Mappable(nameof(WorkflowConditionId))]
    public class WorkflowConditionDto : TenantDtoBase
    {
        /// <summary>
        /// The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.
        /// </summary>
        public int WorkflowConditionId { get; set; }
        
        /// <summary>
        /// The C# expression that will be evaluated to determine if the condition is true
        /// </summary>
        public string Expression { get; set; } = string.Empty;
        
        /// <summary>
        /// A descriptive name for the condition
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// A detailed description of what the condition checks
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// Indicates whether this condition is enabled and can be used
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowCondition
    /// </summary>
    public class WorkflowConditionListDto : WorkflowConditionDto
    {
    }
}
