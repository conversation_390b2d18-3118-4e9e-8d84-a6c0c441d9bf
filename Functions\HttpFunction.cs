using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using RediAzurefunctionBase.BusinessLogic;
using Sql;

namespace RediAzurefunctionBase.Functions
{
    public class HttpFunctionBase : Prime3FunctionHttpBase
    {
        private HttpProcessor1 _httpProcessor1;
        public HttpFunctionBase(IUnitOfWork unitOfWork
            , ILogger logger
            ,HttpProcessor1 httpProcessor1
            ,DefaultAzureQueue defaultAzureQueue
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _httpProcessor1 = httpProcessor1;
            _defaultAzureQueue = defaultAzureQueue;
        }

        [Function(nameof(HttpFunctionBase))]
         public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, 
                                                    methods: "post", 
                                                    Route = "route/{myIntParm:int}")] HttpRequest  request
                                                    , int myIntParm, FunctionContext context)
        {
            // 1) Performs Standard Prime3 Pre Processing of a Http Request (log)
            await PreProcess(request, context);

            string? name = request.Query["name"];
            string requestBody = await new StreamReader(request.Body).ReadToEndAsync();
            dynamic? data = JsonSerializer.Deserialize<dynamic>(requestBody);

            // 2) Http Request processing
            await _httpProcessor1.ProcessHttpRequest();
            _unitOfWork.Commit();
            _unitOfWork.CloseConnection();

            // 2.1) Insert to queue to trigger subsequent processing.... DELETE IF NOT REQUIRED
            await _defaultAzureQueue.WriteToQueue("outputQueueName", "outputQueueMessageData");

            // 3) Performs standard Prime3 Post Processing of a http request (logging)
            await PostProcess(request, context);

            return new OkObjectResult("");
        }
    }
}

