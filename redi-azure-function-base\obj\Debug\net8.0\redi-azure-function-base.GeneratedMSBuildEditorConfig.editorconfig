is_global = true
build_property.FunctionsEnableMetadataSourceGen = true
build_property.FunctionsEnableExecutorSourceGen = true
build_property.FunctionsAutoRegisterGeneratedFunctionsExecutor = true
build_property.FunctionsAutoRegisterGeneratedMetadataProvider = true
build_property.FunctionsGeneratedCodeNamespace = RediAzurefunctionBase
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.FunctionsExecutionModel = isolated
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = RediAzurefunctionBase
build_property.ProjectDir = C:\Users\<USER>\Documents\Web Application Projects\redi-microservices\redi-azure-function-base\redi-azure-function-base\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
