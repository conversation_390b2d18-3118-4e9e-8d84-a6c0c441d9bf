﻿using System;
namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Prime3 Micro Service Startup Exception
    /// </summary>
    public class StartupException : Exception
    {
        public StartupException()
        {
        }

        public StartupException(string message)
            : base(message)
        {
        }

        public StartupException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }

    /// <summary>
    /// Prime3 ApiErrorException is to be used when an error is to be 
    /// returned to the front-end, but stack trace is not required.
    /// </summary>
    public class ApiErrorException : Exception
    {
        public ApiErrorException()
        {
        }

        /// <summary>
        /// ApiErrorException - error to be returned to front-end
        /// </summary>
        /// <param name="message"></param>
        public ApiErrorException(string message)
            : base(message)
        {
        }

        public ApiErrorException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}
