using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class Attribute : BusinessLogicBase
    {
        public Attribute(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single Attribute by Code
        /// </summary>
        /// <param name="attributeCode">The attribute code</param>
        /// <returns>AttributeDto or null if not found</returns>
        internal async Task<AttributeDto?> GetAsync(string attributeCode, bool ignoreErrorIfNotExists = false)
        {
            if (string.IsNullOrWhiteSpace(attributeCode))
            {
                throw new HttpRequestException("AttributeCode cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string sql = @"
                SELECT [AttributeCode]
                      ,[Label]
                      ,[AttributeValueTypeCode]
                FROM [workflow].[Attribute]
                WHERE [AttributeCode] = @attributeCode";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("attributeCode", attributeCode);
                var result = await command.SelectSingle<AttributeDto>();

                if (result == null && !ignoreErrorIfNotExists)
                {
                    throw new HttpRequestException($"Attribute with code '{attributeCode}' not found", null, System.Net.HttpStatusCode.NotFound);
                }

                return result;
            }
        }

        /// <summary>
        /// Get a list of Attributes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="attributeValueTypeCode">Filter by attribute value type code</param>
        /// <returns>List of Attributes</returns>
        internal async Task<ListResponseDto<AttributeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, string? attributeValueTypeCode = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "AttributeCode", "[Attribute].[AttributeCode]" },
                { "Label", "[Attribute].[Label]" },
                { "AttributeValueTypeCode", "[Attribute].[AttributeValueTypeCode]" }
            };

            string sql = @"
                SELECT [AttributeCode]
                      ,[Label]
                      ,[AttributeValueTypeCode]
                FROM [workflow].[Attribute]
                WHERE 1=1";

            if (!string.IsNullOrWhiteSpace(attributeValueTypeCode))
            {
                sql += " AND [AttributeValueTypeCode] = @attributeValueTypeCode";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "AttributeCode");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<AttributeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (!string.IsNullOrWhiteSpace(attributeValueTypeCode))
                {
                    command.AddArgument("attributeValueTypeCode", attributeValueTypeCode);
                }
                result.List = await command.SelectMany<AttributeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[Attribute]
                WHERE 1=1";

            if (!string.IsNullOrWhiteSpace(attributeValueTypeCode))
            {
                countSql += " AND [AttributeValueTypeCode] = @attributeValueTypeCode";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (!string.IsNullOrWhiteSpace(attributeValueTypeCode))
                {
                    command.AddArgument("attributeValueTypeCode", attributeValueTypeCode);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
