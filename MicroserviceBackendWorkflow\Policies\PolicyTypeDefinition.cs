using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Policies
{
    public class PolicyTypeDefinition
    {
        public string Policy { get; set; }
        public Action<AuthorizationPolicyBuilder> Action { get; set; }
        public PolicyTypeDefinition(string policy, Action<AuthorizationPolicyBuilder> action)
        {
            this.Policy = policy;
            this.Action = action;
        }
    }
}
