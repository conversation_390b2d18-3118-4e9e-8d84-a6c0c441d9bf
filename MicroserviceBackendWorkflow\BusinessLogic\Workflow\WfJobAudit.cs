using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WfJobAudit : BusinessLogicBase
    {
        public WfJobAudit(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WfJobAudit by ID
        /// </summary>
        /// <param name="wfJobAuditId">The workflow job audit ID</param>
        /// <returns>WfJobAuditDto or null if not found</returns>
        internal async Task<WfJobAuditDto?> GetAsync(long wfJobAuditId)
        {
            string sql = @"
                SELECT [WfJobAuditId]
                      ,[WfJobId]
                      ,[WorkflowStepId]
                      ,[QueueName]
                      ,[TenantId]
                      ,[QueuedTime]
                      ,[StartTime]
                      ,[EndTime]
                      ,[Information]
                FROM [workflow].[WfJobAudit]
                WHERE [WfJobAuditId] = @wfJobAuditId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("wfJobAuditId", wfJobAuditId);
                return await command.SelectSingle<WfJobAuditDto>();
            }
        }

        /// <summary>
        /// Get a list of WfJobAudits with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="wfJobId">Filter by workflow job ID</param>
        /// <param name="workflowStepId">Filter by workflow step ID</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="queueName">Filter by queue name</param>
        /// <returns>List of WfJobAudits</returns>
        internal async Task<ListResponseDto<WfJobAuditListDto>> GetListAsync(StandardListParameters? standardListParameters = null, long? wfJobId = null, int? workflowStepId = null, int? tenantId = null, string? queueName = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobAuditId", "[WfJobAudit].[WfJobAuditId]" },
                { "WfJobId", "[WfJobAudit].[WfJobId]" },
                { "WorkflowStepId", "[WfJobAudit].[WorkflowStepId]" },
                { "QueuedTime", "[WfJobAudit].[QueuedTime]" },
                { "StartTime", "[WfJobAudit].[StartTime]" },
                { "EndTime", "[WfJobAudit].[EndTime]" }
            };

            string sql = @"
                SELECT [WfJobAuditId]
                      ,[WfJobId]
                      ,[WorkflowStepId]
                      ,[QueueName]
                      ,[TenantId]
                      ,[QueuedTime]
                      ,[StartTime]
                      ,[EndTime]
                      ,[Information]
                FROM [workflow].[WfJobAudit]
                WHERE 1=1";

            if (wfJobId.HasValue)
            {
                sql += " AND [WfJobId] = @wfJobId";
            }

            if (workflowStepId.HasValue)
            {
                sql += " AND [WorkflowStepId] = @workflowStepId";
            }

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (!string.IsNullOrWhiteSpace(queueName))
            {
                sql += " AND [QueueName] = @queueName";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "QueuedTime");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobAuditListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }
                if (workflowStepId.HasValue)
                {
                    command.AddArgument("workflowStepId", workflowStepId.Value);
                }
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (!string.IsNullOrWhiteSpace(queueName))
                {
                    command.AddArgument("queueName", queueName);
                }
                result.List = await command.SelectMany<WfJobAuditListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJobAudit]
                WHERE 1=1";

            if (wfJobId.HasValue)
            {
                countSql += " AND [WfJobId] = @wfJobId";
            }

            if (workflowStepId.HasValue)
            {
                countSql += " AND [WorkflowStepId] = @workflowStepId";
            }

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (!string.IsNullOrWhiteSpace(queueName))
            {
                countSql += " AND [QueueName] = @queueName";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }
                if (workflowStepId.HasValue)
                {
                    command.AddArgument("workflowStepId", workflowStepId.Value);
                }
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (!string.IsNullOrWhiteSpace(queueName))
                {
                    command.AddArgument("queueName", queueName);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
