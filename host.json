{"version": "2.0", "functionTimeout": "00:10:00", "logging": {"fileLoggingMode": "never", "logLevel": {"default": "Information"}, "console": {"isEnabled": false, "DisableColors": false}, "applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 5, "excludedTypes": "Request"}, "enableLiveMetricsFilters": true}}, "extensions": {"blobs": {"maxDegreeOfParallelism": 1, "poisonBlobThreshold": 5}, "queues": {"maxPollingInterval": "00:00:02", "visibilityTimeout": "00:02:00", "batchSize": 1, "maxDequeueCount": 5, "newBatchThreshold": 1, "messageEncoding": "base64"}, "http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true, "hsts": {"isEnabled": true, "maxAge": "10"}, "customHeaders": {"X-Content-Type-Options": "nosniff"}}}}