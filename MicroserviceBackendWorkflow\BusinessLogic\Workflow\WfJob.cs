using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WfJob : BusinessLogicBase
    {
        public WfJob(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WfJob by ID
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <returns>WfJobDto or null if not found</returns>
        internal async Task<WfJobDto?> GetAsync(long wfJobId)
        {
            string sql = @"
                SELECT [WfJobId]
                      ,[TenantId]
                      ,[Name]
                      ,[ScheduleId]
                      ,[WorkflowId]
                      ,[WorkflowConditionId]
                      ,[ScheduledAt]
                      ,[IsScheduled]
                      ,[WfJobStatusId]
                      ,[SchedulePurposeId]
                      ,[OwningPartyId]
                      ,[CompletedAt]
                      ,[NextCheckDependenciesAt]
                      ,[TimeoutDependencyWaitAt]
                      ,[WorkflowStepId]
                      ,[WfJobCancelledReasonId]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[WfJob]
                WHERE [WfJobId] = @wfJobId
                  AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                return await command.SelectSingle<WfJobDto>();
            }
        }

        /// <summary>
        /// Create a new WfJob
        /// </summary>
        /// <param name="dto">The WfJob data</param>
        internal async Task CreateAsync(WfJobDto dto)
        {
            if (dto.WfJobStatusId <= 0)
            {
                throw new HttpRequestException("WfJobStatusId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.SchedulePurposeId <= 0)
            {
                throw new HttpRequestException("SchedulePurposeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate that OwningPartyId is not empty GUID
            if (dto.OwningPartyId.HasValue && dto.OwningPartyId == Guid.Empty)
            {
                throw new HttpRequestException("OwningPartyId cannot be empty GUID", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate WfJobStatus exists
            var wfJobStatus = new WfJobStatus(_unitOfWork);
            await wfJobStatus.GetAsync(dto.WfJobStatusId);

            // Validate SchedulePurpose exists
            var schedulePurpose = new SchedulePurpose(_unitOfWork);
            await schedulePurpose.GetAsync(dto.SchedulePurposeId);

            // Validate optional foreign keys
            if (dto.ScheduleId.HasValue)
            {
                var schedule = new Schedule(_unitOfWork);
                await schedule.GetAsync(dto.ScheduleId.Value);
            }

            if (dto.WorkflowId.HasValue)
            {
                var workflow = new Workflow(_unitOfWork);
                await workflow.GetAsync(dto.WorkflowId.Value);
            }

            if (dto.WorkflowConditionId.HasValue)
            {
                var workflowCondition = new WorkflowCondition(_unitOfWork);
                await workflowCondition.GetAsync(dto.WorkflowConditionId.Value);
            }

            if (dto.WorkflowStepId.HasValue)
            {
                var workflowStep = new WorkflowStep(_unitOfWork);
                await workflowStep.GetAsync(dto.WorkflowStepId.Value);
            }

            string sql = @"
                INSERT INTO [workflow].[WfJob]
                ([TenantId], [Name], [ScheduleId], [WorkflowId], [WorkflowConditionId], [ScheduledAt], [IsScheduled],
                 [WfJobStatusId], [SchedulePurposeId], [OwningPartyId], [CompletedAt], [NextCheckDependenciesAt],
                 [TimeoutDependencyWaitAt], [WorkflowStepId], [WfJobCancelledReasonId], [CreatedOn], [CreatedByName])
                VALUES
                (@TenantId, @Name, @ScheduleId, @WorkflowId, @WorkflowConditionId, @ScheduledAt, @IsScheduled,
                 @WfJobStatusId, @SchedulePurposeId, @OwningPartyId, @CompletedAt, @NextCheckDependenciesAt,
                 @TimeoutDependencyWaitAt, @WorkflowStepId, @WfJobCancelledReasonId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing WfJob
        /// </summary>
        /// <param name="dto">The WfJob data</param>
        internal async Task UpdateAsync(WfJobDto dto)
        {
            if (dto.WfJobId <= 0)
            {
                throw new HttpRequestException("WfJobId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.WfJobStatusId <= 0)
            {
                throw new HttpRequestException("WfJobStatusId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.SchedulePurposeId <= 0)
            {
                throw new HttpRequestException("SchedulePurposeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate that OwningPartyId is not empty GUID
            if (dto.OwningPartyId.HasValue && dto.OwningPartyId == Guid.Empty)
            {
                throw new HttpRequestException("OwningPartyId cannot be empty GUID", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WfJobId);
            if (exists == null)
            {
                throw new HttpRequestException($"WfJob with ID '{dto.WfJobId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            // Validate WfJobStatus exists
            var wfJobStatus = new WfJobStatus(_unitOfWork);
            await wfJobStatus.GetAsync(dto.WfJobStatusId);

            // Validate SchedulePurpose exists
            var schedulePurpose = new SchedulePurpose(_unitOfWork);
            await schedulePurpose.GetAsync(dto.SchedulePurposeId);

            // Validate optional foreign keys
            if (dto.ScheduleId.HasValue)
            {
                var schedule = new Schedule(_unitOfWork);
                await schedule.GetAsync(dto.ScheduleId.Value);
            }

            if (dto.WorkflowId.HasValue)
            {
                var workflow = new Workflow(_unitOfWork);
                await workflow.GetAsync(dto.WorkflowId.Value);
            }

            if (dto.WorkflowConditionId.HasValue)
            {
                var workflowCondition = new WorkflowCondition(_unitOfWork);
                await workflowCondition.GetAsync(dto.WorkflowConditionId.Value);
            }

            if (dto.WorkflowStepId.HasValue)
            {
                var workflowStep = new WorkflowStep(_unitOfWork);
                await workflowStep.GetAsync(dto.WorkflowStepId.Value);
            }

            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [TenantId] = @TenantId,
                    [Name] = @Name,
                    [ScheduleId] = @ScheduleId,
                    [WorkflowId] = @WorkflowId,
                    [WorkflowConditionId] = @WorkflowConditionId,
                    [ScheduledAt] = @ScheduledAt,
                    [IsScheduled] = @IsScheduled,
                    [WfJobStatusId] = @WfJobStatusId,
                    [SchedulePurposeId] = @SchedulePurposeId,
                    [OwningPartyId] = @OwningPartyId,
                    [CompletedAt] = @CompletedAt,
                    [NextCheckDependenciesAt] = @NextCheckDependenciesAt,
                    [TimeoutDependencyWaitAt] = @TimeoutDependencyWaitAt,
                    [WorkflowStepId] = @WorkflowStepId,
                    [WfJobCancelledReasonId] = @WfJobCancelledReasonId,
                    [ModifiedOn] = @ModifiedOn,
                    [ModifiedByName] = @ModifiedByName
                WHERE [WfJobId] = @WfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a WfJob (soft delete)
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        internal async Task DeleteAsync(long wfJobId)
        {
            var exists = await GetAsync(wfJobId);
            if (exists == null)
            {
                throw new HttpRequestException($"WfJob with ID '{wfJobId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [Deleted] = 1,
                    [ModifiedOn] = @ModifiedOn
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of WfJobs with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="scheduleId">Filter by schedule ID</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="wfJobStatusId">Filter by job status ID</param>
        /// <param name="owningPartyId">Filter by owning party ID</param>
        /// <returns>List of WfJobs</returns>
        internal async Task<ListResponseDto<WfJobListDto>> GetListAsync(StandardListParameters standardListParameters, int? tenantId = null, int? scheduleId = null, int? workflowId = null, byte? wfJobStatusId = null, Guid? owningPartyId = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobId", "[WfJob].[WfJobId]" },
                { "Name", "[WfJob].[Name]" },
                { "TenantId", "[WfJob].[TenantId]" },
                { "ScheduleId", "[WfJob].[ScheduleId]" },
                { "WorkflowId", "[WfJob].[WorkflowId]" },
                { "ScheduledAt", "[WfJob].[ScheduledAt]" },
                { "WfJobStatusId", "[WfJob].[WfJobStatusId]" },
                { "CompletedAt", "[WfJob].[CompletedAt]" },
                { "CreatedOn", "[WfJob].[CreatedOn]" },
                { "ModifiedOn", "[WfJob].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [WfJobId], [TenantId], [Name], [ScheduleId], [WorkflowId], [WorkflowConditionId], [ScheduledAt], [IsScheduled],
                       [WfJobStatusId], [SchedulePurposeId], [OwningPartyId], [CompletedAt], [NextCheckDependenciesAt],
                       [TimeoutDependencyWaitAt], [WorkflowStepId], [WfJobCancelledReasonId], [CreatedOn], [CreatedByName],
                       [ModifiedOn], [ModifiedByName], [Deleted]
                FROM [workflow].[WfJob]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (scheduleId.HasValue)
            {
                sql += " AND [ScheduleId] = @scheduleId";
            }

            if (workflowId.HasValue)
            {
                sql += " AND [WorkflowId] = @workflowId";
            }

            if (wfJobStatusId.HasValue)
            {
                sql += " AND [WfJobStatusId] = @wfJobStatusId";
            }

            if (owningPartyId.HasValue && owningPartyId != Guid.Empty)
            {
                sql += " AND [OwningPartyId] = @owningPartyId";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WfJobId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (wfJobStatusId.HasValue)
                {
                    command.AddArgument("wfJobStatusId", wfJobStatusId.Value);
                }
                if (owningPartyId.HasValue && owningPartyId != Guid.Empty)
                {
                    command.AddArgument("owningPartyId", owningPartyId.Value);
                }
                result.List = await command.SelectMany<WfJobListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJob]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (scheduleId.HasValue)
            {
                countSql += " AND [ScheduleId] = @scheduleId";
            }

            if (workflowId.HasValue)
            {
                countSql += " AND [WorkflowId] = @workflowId";
            }

            if (wfJobStatusId.HasValue)
            {
                countSql += " AND [WfJobStatusId] = @wfJobStatusId";
            }

            if (owningPartyId.HasValue && owningPartyId != Guid.Empty)
            {
                countSql += " AND [OwningPartyId] = @owningPartyId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (wfJobStatusId.HasValue)
                {
                    command.AddArgument("wfJobStatusId", wfJobStatusId.Value);
                }
                if (owningPartyId.HasValue && owningPartyId != Guid.Empty)
                {
                    command.AddArgument("owningPartyId", owningPartyId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
