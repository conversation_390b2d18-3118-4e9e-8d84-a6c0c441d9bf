using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job operations
    /// </summary>
    public class WorkflowJob : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;
        private readonly WfJobCancelledReason _cancelledReason;

        public WorkflowJob(IUnitOfWork unitOfWork, ILogger<WorkflowJob> logger, UtilityFunctions utilityFunctions, Cache cache, JobStatus jobStatus, WfJobCancelledReason cancelledReason)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
            _cancelledReason = cancelledReason;
        }

        public async Task RunUnscheduledWfJobAsync()
        {
            _logger.LogInformation($"Running unscheduled workflow job");

            try
            {

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to run unscheduled workflow job");
                throw;
            }
        }

        /// <summary>
        /// Cancel a workflow job
        /// </summary>
        /// <param name="wfJobId">The workflow job ID to cancel</param>
        /// <param name="cancelledReasonEnum">The reason for cancellation</param>
        /// <param name="reason">Optional additional reason text</param>
        /// <returns>Task</returns>
        public async Task CancelWfJobAsync(long wfJobId, WfJobCancelledReasonEnum cancelledReasonId)
        {
            _logger.LogInformation($"Cancelling workflow job {wfJobId} with reason {cancelledReasonEnum}");

            try
            {
                // Validate that the job exists and can be cancelled
                var existingJob = await GetWfJobDetailsAsync(wfJobId);
                if (existingJob == null)
                {
                    throw new ArgumentException($"Workflow job with ID {wfJobId} not found");
                }

                // Validate status values
                var cancelledStatusId = await _jobStatus.GetValidatedStatusIdFromEnumAsync(JobStatusEnum.Cancelled);
                var validatedCancelledReasonId = await _cancelledReason.GetValidatedReasonIdFromEnumAsync(cancelledReasonEnum);

                if (existingJob.WfJobStatusId == JobStatusEnum.Completed)
                {
                    throw new InvalidOperationException($"Cannot cancel workflow job {wfJobId} - it is already completed");
                }

                if (existingJob.WfJobStatusId == cancelledStatusId)
                {
                    throw new InvalidOperationException($"Workflow job {wfJobId} is already cancelled");
                }

                // Update the job status to cancelled
                string sql = @"
                    UPDATE [workflow].[WfJob]
                    SET [WfJobStatusId] = @cancelledStatusId,
                        [WfJobCancelledReasonId] = @cancelledReasonId,
                        [CompletedAt] = @completedAt,
                        [ModifiedOn] = @modifiedOn,
                        [ModifiedByName] = @modifiedByName
                    WHERE [WfJobId] = @wfJobId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
                {
                    command.AddArgument("wfJobId", wfJobId);
                    command.AddArgument("cancelledStatusId", cancelledStatusId);
                    command.AddArgument("cancelledReasonId", validatedCancelledReasonId);
                    command.AddArgument("completedAt", DateTimeOffset.UtcNow);
                    command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                    command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                    await command.Execute();
                }

                _logger.LogInformation($"Successfully cancelled workflow job {wfJobId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to cancel workflow job {wfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Get detailed information about a workflow job
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <returns>The workflow job details or null if not found</returns>
        public async Task<WfJobDto?> GetWfJobDetailsAsync(long wfJobId)
        {
            _logger.LogDebug($"Getting details for workflow job {wfJobId}");

            try
            {
                // Check cache first
                var cacheKey = $"WfJob_{wfJobId}";
                var (exists, cachedJob) = await _cache.GetCacheItem<WfJobDto>("WfJob", cacheKey);
                if (exists && cachedJob != null)
                {
                    return cachedJob;
                }

                string sql = @"
                    SELECT [WfJobId], [TenantId], [Name], [ScheduleId], [WorkflowId], [WorkflowConditionId], 
                           [ScheduledAt], [IsScheduled], [WfJobStatusId], [SchedulePurposeId], [OwningPartyId], 
                           [CompletedAt], [NextCheckDependenciesAt], [TimeoutDependencyWaitAt], [WorkflowStepId], 
                           [WfJobCancelledReasonId], [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted]
                    FROM [workflow].[WfJob]
                    WHERE [WfJobId] = @wfJobId AND [Deleted] = 0";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("wfJobId", wfJobId);
                    var job = await command.SelectSingle<WfJobDto>();

                    // Cache the result if found
                    if (job != null)
                    {
                        await _cache.SetCacheItem("WfJob", cacheKey, job, cacheForMinutes: 5);
                    }

                    return job;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get details for workflow job {wfJobId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId], [IsEnabled]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }

                if (!workflow.IsEnabled)
                {
                    throw new InvalidOperationException($"Workflow with ID {workflowId} is not enabled");
                }
            }
        }

        private async Task<int?> GetFirstWorkflowStepAsync(int workflowId)
        {
            string sql = @"
                SELECT [FirstWorkflowStepId]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                return await command.SelectSingle<int?>();
            }
        }

        private async Task<long> CreateWorkflowJobAsync(WfJobDto wfJobDto)
        {
            string sql = @"
                INSERT INTO [workflow].[WfJob]
                ([TenantId], [Name], [WorkflowId], [WorkflowConditionId], [ScheduledAt], [IsScheduled], 
                 [WfJobStatusId], [OwningPartyId], [WorkflowStepId], [CreatedOn], [CreatedByName])
                OUTPUT INSERTED.WfJobId
                VALUES
                (@TenantId, @Name, @WorkflowId, @WorkflowConditionId, @ScheduledAt, @IsScheduled, 
                 @WfJobStatusId, @OwningPartyId, @WorkflowStepId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(wfJobDto);
                return await command.SelectSingle<long>();
            }
        }

        #endregion
    }
}
