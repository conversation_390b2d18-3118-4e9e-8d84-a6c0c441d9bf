﻿namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Determines how a blob will be handled after it has been processed
    /// </summary>
    public enum BlobPostProcessingMode
    {
        /// <summary>
        /// Do not perform any post processing on the blob file.
        /// The blob file will remain
        /// </summary>
        None,
        /// <summary>
        /// Move the blob to an Archive folder
        /// </summary>
        Archive,
        /// <summary>
        /// Delete the blob
        /// </summary>
        Delete,
        /// <summary>
        /// Copy the blob file to another folder
        /// Existing file will remain
        /// </summary>
        Copy,
        /// <summary>
        /// Copy AND Archive the blob
        /// Create a copy in another folder
        /// Move bold to an Archive folder
        /// 2 copies will exist at the end (Archived folder and Copy folder)
        /// </summary>
        CopyAndArchive
    }
}