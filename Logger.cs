using Microsoft.Extensions.Logging;
using Microsoft.Data.SqlClient;
using System.Reflection;


namespace Redi.Prime3.MicroService.Logger
{
	public class AppLogger : ILogger
	{

		readonly LogLevel level;
		readonly string connectionString;
		readonly bool _isSentryEnabled = false;
		readonly string _loggedFrom;
		private IExternalScopeProvider _scopeProvider { get; set; }
        public AppLogger(LogLevel l, string conn, bool isSentryEnabled, string loggedFrom, IExternalScopeProvider scopeProvider)
		{
			level = l;
			connectionString = conn;
			_isSentryEnabled = isSentryEnabled;
			_loggedFrom = loggedFrom;
			_scopeProvider = scopeProvider;
        }

		public IDisposable? BeginScope<TState>(TState state) where TState : notnull
		{
			return _scopeProvider?.Push(state) ?? default!;
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            return level <= logLevel;
        }

		public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
		{
            if (!IsEnabled(logLevel))
            {
                return;
            }

            Task.Run(async () =>
			{
				// loggedFrom will contain the Azure Functions Domain so it can be easily identified
				var loggedFrom = Environment.GetEnvironmentVariable("WEBSITE_HOSTNAME")
								 + " | " + _loggedFrom + " | "
								 + (eventId.Name != null ? eventId.Name + " | " : "");

                if (loggedFrom.Length > 300) { loggedFrom = loggedFrom.Substring(0,300);}

				Dictionary<string, object> scopeVars = new Dictionary<string, object>();

				if (_scopeProvider != null) 
				{
					_scopeProvider.ForEachScope((scope, state) => AppendScope(state, scope), scopeVars);
				}

				var description2 = exception != null ? formatter(state, exception) + " | " + FormatScoped(scopeVars) : FormatScoped(scopeVars);
				if (description2 == "" || description2 == " | ") { description2 = null; }

				using (var conn = new SqlConnection(connectionString))
				{
					string table = "";

					switch (logLevel)
					{
						case LogLevel.Critical:
						case LogLevel.Error:
						case LogLevel.Warning:
							table = "_Log_Error";
							break;
						case LogLevel.Information:
						case LogLevel.Debug:
						case LogLevel.Trace:
							table = "_Log_Message";
							break;
						case LogLevel.None:
							return;
					}

					string sql = $@"
INSERT INTO [common].[{table}] (
       [Description]
      ,[Stack]
      ,[CreatedOnUtc]
      ,[Description2]
      ,[LoggedFrom]
      ,[Severity]
      ,[Deleted]
)
VALUES (
	@Description,
	@Stack,
	GETUTCDATE(),
	@Description2,
	@LoggedFrom,
	@Severity,
	0
);
					";

					using (var cmd = new SqlCommand(sql, conn))
					{
						await conn.OpenAsync();
						cmd.Parameters.AddWithValue("Description", exception != null ? FormatExceptionMessage(exception) : formatter(state, null));
						cmd.Parameters.AddWithValue("Stack", exception != null ? FormatExceptionStack(exception) : (object)DBNull.Value);
						cmd.Parameters.AddWithValue("Description2", description2 != null ? description2 : (object)DBNull.Value );
						cmd.Parameters.AddWithValue("LoggedFrom", loggedFrom != null ? loggedFrom : (object)DBNull.Value );
						cmd.Parameters.AddWithValue("Severity", (int)logLevel);

						await cmd.ExecuteNonQueryAsync();
					}
				}
			});

            SentryLogCapture(exception, logLevel, state, formatter, eventId.Name);
		}

		private static void AppendScope(IDictionary<string, object> dictionary, object? scope)
        {
            if (scope == null)
                return;

            // The scope can be defined using BeginScope or LogXXX methods.
            // - logger.BeginScope(new { Author = "meziantou" })
            if (scope is Dictionary<string, object> formattedLogValues)
            {
                if (formattedLogValues.Count > 0)
                {
                    foreach (var value in formattedLogValues)
                    {
                        if (value.Value is MethodInfo)
                            continue;

                        dictionary[value.Key] = value.Value;
                    }
                }
            }
        }

		string FormatScoped(Dictionary<string, object> scopeVars)
		{
			string resp = "";
			string prefixer = "";
			foreach(var key in scopeVars.Keys)
			{
				resp += $"{prefixer}{key}:{scopeVars[key]}";
				prefixer = ",";
            }

			return resp;
		}

		/// <summary>
		/// print all exceptions' messages
		/// </summary>
		/// <param name="e"></param>
		/// <returns></returns>
		string FormatExceptionMessage(Exception e, int count = 1)
		{
			string nl = Environment.NewLine;

			string rtn = (count > 1 ? "Inner " : "") + "Ex " + count + ": " + e.Message + (e.InnerException != null ? $"{nl}-- END --{nl}{nl}" : nl);
			if (e.InnerException != null)
			{
				rtn += FormatExceptionMessage(e.InnerException, count + 1);
			}
			return rtn;
		}

		/// <summary>
		/// print all exceptions' stack traces
		/// </summary>
		/// <param name="e"></param>
		/// <param name="count"></param>
		/// <returns></returns>
		string FormatExceptionStack(Exception e, int count = 1)
		{
			string nl = Environment.NewLine;
			string rtn = (count > 1 ? "Inner " : "") + "Ex " + count + " stack: " + nl + e.StackTrace + (e.InnerException != null ? $"{nl}-- END --{nl}{nl}" : nl);
			if (e.InnerException != null)
			{
				rtn += FormatExceptionStack(e.InnerException, count + 1);
			}
			return rtn;
		}

		private void SentryLogCapture<TState>(Exception? exception, LogLevel logLevel, TState state, Func<TState, Exception, string> formatter, string? _loggedFrom)
        {
			if (_isSentryEnabled)
			{
				if (exception != null)
				{
					SentrySdk.CaptureException(exception, scope => { scope.TransactionName = _loggedFrom; } );
				}
				else
				{
#pragma warning disable CS8604 // Possible null reference argument.
                    var message = formatter(state, exception);
#pragma warning restore CS8604 // Possible null reference argument.
                    var sentryLogLevel = MicrosoftLogLevelToSentryLogLevel(logLevel);
					SentrySdk.CaptureMessage(message, sentryLogLevel);
				}
			}
		}

		private SentryLevel MicrosoftLogLevelToSentryLogLevel(LogLevel logLevel)
		{
			SentryLevel sentryLogLevel;
			switch (logLevel)
			{
				case LogLevel.Critical:
					sentryLogLevel = SentryLevel.Fatal;
					break;
				case LogLevel.Error:
					sentryLogLevel = SentryLevel.Error;
					break;
				case LogLevel.Warning:
					sentryLogLevel = SentryLevel.Warning;
					break;
				case LogLevel.Information:
					sentryLogLevel = SentryLevel.Info;
					break;
				case LogLevel.Trace:
				case LogLevel.Debug:
					sentryLogLevel = SentryLevel.Debug;
					break;
				case LogLevel.None:
				default:
					sentryLogLevel = SentryLevel.Fatal;
					break;
			}
			return sentryLogLevel;
		}
	}
}
