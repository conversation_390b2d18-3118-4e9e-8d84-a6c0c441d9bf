using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WorkflowStep : BusinessLogicBase
    {
        private readonly Func<WorkflowStepType> _workflowStepTypeFactory;
        private readonly Func<Workflow> _workflowFactory;

        public WorkflowStep(IUnitOfWork unitOfWork, IMemoryCache memoryCache, Func<WorkflowStepType> workflowStepTypeFactory, Func<Workflow> workflowFactory)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
            _workflowStepTypeFactory = workflowStepTypeFactory;
            _workflowFactory = workflowFactory;
        }

        /// <summary>
        /// Get a single WorkflowStep by ID
        /// </summary>
        /// <param name="workflowStepId">The workflow step ID</param>
        /// <returns>WorkflowStepDto or null if not found</returns>
        internal async Task<WorkflowStepDto?> GetAsync(int workflowStepId)
        {
            string cacheKey = "WorkflowStep" + workflowStepId;

            if (_memoryCache.TryGetValue(cacheKey, out WorkflowStepDto? cacheValue))
            {
                if (cacheValue != null)
                {
                    return cacheValue.DeepClone();
                }
            }

            string sql = @"
                SELECT [WorkflowStepId]
                      ,[WorkflowId]
                      ,[WorkflowStepTypeId]
                      ,[SortOrder]
                      ,[Name]
                      ,[QueueName]
                      ,[NotificationEventTypeCode]
                FROM [workflow].[WorkflowStep]
                WHERE [WorkflowStepId] = @workflowStepId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowStepId", workflowStepId);
                var result = await command.SelectSingle<WorkflowStepDto>();

                if (result != null)
                {
                    _memoryCache.Set(cacheKey, result.DeepClone(), CacheOptions());
                }

                return result;
            }
        }

        /// <summary>
        /// Create a new WorkflowStep
        /// </summary>
        /// <param name="dto">The WorkflowStep data</param>
        internal async Task CreateAsync(WorkflowStepDto dto)
        {
            if (dto.WorkflowId <= 0)
            {
                throw new HttpRequestException("WorkflowId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.WorkflowStepTypeId <= 0)
            {
                throw new HttpRequestException("WorkflowStepTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                throw new HttpRequestException("Name cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            // Validate WorkflowStepType exists
            await _workflowStepTypeFactory().GetAsync(dto.WorkflowStepTypeId);

            // Validate Workflow exists
            await _workflowFactory().GetAsync(dto.WorkflowId);

            string sql = @"
                INSERT INTO [workflow].[WorkflowStep]
                ([WorkflowId], [WorkflowStepTypeId], [SortOrder], [Name], [QueueName], [NotificationEventTypeCode])
                VALUES
                (@WorkflowId, @WorkflowStepTypeId, @SortOrder, @Name, @QueueName, @NotificationEventTypeCode)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing WorkflowStep
        /// </summary>
        /// <param name="dto">The WorkflowStep data</param>
        internal async Task UpdateAsync(WorkflowStepDto dto)
        {
            if (dto.WorkflowStepId <= 0)
            {
                throw new HttpRequestException("WorkflowStepId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.WorkflowId <= 0)
            {
                throw new HttpRequestException("WorkflowId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (dto.WorkflowStepTypeId <= 0)
            {
                throw new HttpRequestException("WorkflowStepTypeId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                throw new HttpRequestException("Name cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WorkflowStepId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowStep with ID '{dto.WorkflowStepId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            // Validate WorkflowStepType exists
            await _workflowStepTypeFactory().GetAsync(dto.WorkflowStepTypeId);

            // Validate Workflow exists
            await _workflowFactory().GetAsync(dto.WorkflowId);

            string sql = @"
                UPDATE [workflow].[WorkflowStep]
                SET [WorkflowId] = @WorkflowId,
                    [WorkflowStepTypeId] = @WorkflowStepTypeId,
                    [SortOrder] = @SortOrder,
                    [Name] = @Name,
                    [QueueName] = @QueueName,
                    [NotificationEventTypeCode] = @NotificationEventTypeCode
                WHERE [WorkflowStepId] = @WorkflowStepId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a WorkflowStep
        /// </summary>
        /// <param name="workflowStepId">The workflow step ID</param>
        internal async Task DeleteAsync(int workflowStepId)
        {
            var exists = await GetAsync(workflowStepId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowStep with ID '{workflowStepId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                DELETE FROM [workflow].[WorkflowStep]
                WHERE [WorkflowStepId] = @workflowStepId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("workflowStepId", workflowStepId);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of WorkflowSteps with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="workflowStepTypeId">Filter by workflow step type ID</param>
        /// <returns>List of WorkflowSteps</returns>
        internal async Task<ListResponseDto<WorkflowStepListDto>> GetListAsync(StandardListParameters standardListParameters, int? workflowId = null, short? workflowStepTypeId = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WorkflowStepId", "[WorkflowStep].[WorkflowStepId]" },
                { "WorkflowId", "[WorkflowStep].[WorkflowId]" },
                { "WorkflowStepTypeId", "[WorkflowStep].[WorkflowStepTypeId]" },
                { "SortOrder", "[WorkflowStep].[SortOrder]" },
                { "Name", "[WorkflowStep].[Name]" }
            };

            string sql = @"
                SELECT [WorkflowStepId]
                      ,[WorkflowId]
                      ,[WorkflowStepTypeId]
                      ,[SortOrder]
                      ,[Name]
                      ,[QueueName]
                      ,[NotificationEventTypeCode]
                FROM [workflow].[WorkflowStep]
                WHERE 1=1";

            if (workflowId.HasValue)
            {
                sql += " AND [WorkflowId] = @workflowId";
            }

            if (workflowStepTypeId.HasValue)
            {
                sql += " AND [WorkflowStepTypeId] = @workflowStepTypeId";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WorkflowStepListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (workflowStepTypeId.HasValue)
                {
                    command.AddArgument("workflowStepTypeId", workflowStepTypeId.Value);
                }
                result.List = await command.SelectMany<WorkflowStepListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WorkflowStep]
                WHERE 1=1";

            if (workflowId.HasValue)
            {
                countSql += " AND [WorkflowId] = @workflowId";
            }

            if (workflowStepTypeId.HasValue)
            {
                countSql += " AND [WorkflowStepTypeId] = @workflowStepTypeId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (workflowStepTypeId.HasValue)
                {
                    command.AddArgument("workflowStepTypeId", workflowStepTypeId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
