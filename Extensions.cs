﻿using Microsoft.AspNetCore.Identity;
using System.Reflection;

namespace Redi.Prime3.MicroService.BaseLib
{
    public static class Extensions
    {
        public static IdentityResult ToIdentityError(this Exception e)
        {
            var error = new IdentityError();
            error.Code = "500";
            error.Description = e.ToString();
            return IdentityResult.Failed(error);
        }
        /// <summary>
        /// Helper to easily check if a Guid is Null or Empty
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        public static bool GuidIsNullOrEmpty(this Guid? guid)
        {
            return !guid.HasValue || guid == Guid.Empty;
        }

        /// <summary>
        /// Helper to easily check if a Guid is Null or Empty
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        public static bool GuidIsNullOrEmpty(this Guid guid)
        {
            return guid == Guid.Empty;
        }

        public static bool IsNullOrEmpty(this string? val)
        {
            return string.IsNullOrEmpty(val);
        }

        /// <summary>
        /// Trim number of decimal places (trims off excess digits does not round)
        /// </summary>
        /// <param name="number"></param>
        /// <param name="toNumberOfDecimalPlaces"></param>
        /// <returns></returns>
        public static decimal? DecimalTrimPlaces(decimal? number, int? toNumberOfDecimalPlaces)
        {
            if (toNumberOfDecimalPlaces == null)
            {
                return number;
            }
            if (number != null && number != null)
            {
                int placesBy100 = toNumberOfDecimalPlaces > 0 ? (int)toNumberOfDecimalPlaces * (int)100 : 1;
                return Decimal.Floor((decimal)number * (decimal)placesBy100) / placesBy100;
            }
            else return number;
        }

        /// <summary>
        /// Convert the first character of a string to upper case
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string? FirstCharToUpper(this string? input) =>
        input switch
        {
            null => null,
            "" => "",
            _ => string.Concat(input[0].ToString().ToUpper(), input.AsSpan(1))
        };

        public static void Map(this object target, object source)
        {
            if (source == null || target == null)
            {
                return;
            }

            // list of writable properties of the destination
            var targetType = target.GetType();
            var sourceType = source.GetType();
            if (!sourceType.IsAssignableFrom(targetType))
            {
                throw new InvalidOperationException("Target type must be a subtype of sourceType");
            }
            foreach (var prop in targetType.GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.SetProperty))
            {
                // check whether source object has the the property
                var sp = sourceType.GetProperty(prop.Name);
                if (sp != null)
                {
                    // if yes, copy the value to the matching property
                    object? value = null;
                    try
                    {
                        value = sp?.GetValue(source, null);
                    }
                    catch (Exception)
                    {
                        value = null;
                    }
                    prop.SetValue(target, value, null);
                }
            };
        }
    }
}
