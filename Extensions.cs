using Microsoft.Extensions.Logging;
using System;

namespace Microsoft.Extensions.Logging
{
	public static class AppLoggerExtensions
	{
		public static void LogCritical(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogCritical(new EventId(0, loggedFrom), message);
		}
		public static void LogDebug(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogDebug(new EventId(0, loggedFrom), message);
		}
		public static void LogError(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogError(new EventId(0, loggedFrom), message);
		}
		public static void LogInformation(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogInformation(new EventId(0, loggedFrom), message);
		}
		public static void LogTrace(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogTrace(new EventId(0, loggedFrom), message);
		}
		public static void LogWarning(this ILogger logger, string message, string loggedFrom)
		{
			logger.LogWarning(new EventId(0, loggedFrom), message);
		}
	}
}