using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get Schedule Type lookup data.
    /// </summary>
    [Route("api/ScheduleType")]
    public class ScheduleTypeController : AppController
    {
        private readonly ScheduleType _scheduleType;

        public ScheduleTypeController(ScheduleType scheduleType, IUnitOfWork unitOfWork)
        {
            _scheduleType = scheduleType;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Schedule Type
        /// </summary>
        /// <remarks>
        /// Returns a single Schedule Type record for a given ID.
        /// </remarks>
        /// <param name="scheduleTypeId">The ID of a schedule type record</param>
        /// <response code="200">Schedule Type returned, or null if not found</response>
        /// <response code="404">Schedule Type not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(ScheduleTypeDto), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(short scheduleTypeId)
        {
            var result = await _scheduleType.GetAsync(scheduleTypeId);
            return Ok(result);
        }

        /// <summary>
        /// Get a List of Schedule Types
        /// </summary>
        /// <remarks>
        /// Returns a list of Schedule Types with filtering and paging options.
        /// Returns only enabled types by default.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <response code="200">List of Schedule Types returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<ScheduleTypeListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            var result = await _scheduleType.GetListAsync(standardListParameters, showDisabled);
            return Ok(result);
        }
    }
}
