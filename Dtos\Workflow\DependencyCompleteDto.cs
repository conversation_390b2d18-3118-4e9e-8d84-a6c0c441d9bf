using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Every time something that is a potential dependency to workflow is completed a row is inserted in here.
    /// Scheduled jobs can then check if a dependency is met before they are run or continued with.
    /// </summary>
    [Mappable(nameof(DependencyCompleteId))]
    public class DependencyCompleteDto
    {
        /// <summary>
        /// The unique identifier for the dependency completion record
        /// </summary>
        public long DependencyCompleteId { get; set; }

        /// <summary>
        /// The dependency key is used to uniquely identify a dependency. Format:
        /// T{TenantId}{Tech}{Item}
        ///
        /// TenantId - only included for Tenant Specific items. Otherwise 0
        /// Tech - identifies the applicable technology that is a dependency. eg. adx, sql, api, file
        /// Item - Clearly identifies the item such as a table name.
        /// </summary>
        public string DependencyKey { get; set; } = string.Empty;

        /// <summary>
        /// The Date the dependency is relative to
        /// </summary>
        public DateTimeOffset? DependencyDate { get; set; }

        /// <summary>
        /// The date and time when this dependency completion was recorded
        /// </summary>
        public DateTimeOffset CreatedOn { get; set; }
    }

    /// <summary>
    /// List DTO for DependencyComplete
    /// </summary>
    public class DependencyCompleteListDto : DependencyCompleteDto
    {
    }
}
