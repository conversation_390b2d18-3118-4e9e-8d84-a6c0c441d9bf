using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for recur on position operations
    /// </summary>
    public class RecurOnPosition : BusinessLogicBase
    {
        public RecurOnPosition(IUnitOfWork unitOfWork, ILogger<RecurOnPosition> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single RecurOnPosition by ID
        /// </summary>
        /// <param name="recurOnPositionId">The recur on position ID</param>
        /// <returns>RecurOnPositionDto or null if not found</returns>
        public async Task<RecurOnPositionDto?> GetAsync(byte recurOnPositionId)
        {
            _logger.LogDebug($"Getting recur on position for ID: {recurOnPositionId}");

            try
            {
                // Check cache first
                var cacheKey = $"RecurOnPosition_{recurOnPositionId}";
                var (exists, cachedPosition) = await _cache.GetCacheItem<RecurOnPositionDto>("RecurOnPosition", cacheKey);
                if (exists && cachedPosition != null)
                {
                    return cachedPosition;
                }

                string sql = @"
                    SELECT [RecurOnPositionId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[RecurOnPosition]
                    WHERE [RecurOnPositionId] = @recurOnPositionId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("recurOnPositionId", recurOnPositionId);
                    var recurOnPosition = await command.SelectSingle<RecurOnPositionDto>();

                    // Cache the result if found
                    if (recurOnPosition != null)
                    {
                        await _cache.SetCacheItem("RecurOnPosition", cacheKey, recurOnPosition, cacheForMinutes: 30);
                    }

                    return recurOnPosition;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get recur on position for ID: {recurOnPositionId}");
                throw;
            }
        }
    }
}
