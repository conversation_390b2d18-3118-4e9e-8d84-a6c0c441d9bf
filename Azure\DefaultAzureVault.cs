﻿using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Read/Write Azure Blobs to default storage account
    /// </summary>
    public class DefaultAzureVault : AzureVaultBase
    {
        public DefaultAzureVault(IAzureClientFactory<SecretClient> secretClient, ILogger logger)
        {
            if (string.IsNullOrEmpty(ConfigBase.KeyVaultName))
            {
                throw new Exception("DefaultAzureVault has not been properly configured for use by this Function. The config setting KeyVaultName must be set");
            }
            _secretClient = secretClient.CreateClient("Default");
            _logger = logger;
        }
    }
}
