using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// Connect steps together in a Complex Workflow that has 1 or more conditional paths.
    /// A parent step may have 1 or more child steps with conditions that control which path is taken.
    /// </summary>
    [Mappable(nameof(WorkflowStepConnectorId))]
    public class WorkflowStepConnectorDto
    {
        /// <summary>
        /// The Workflow Step Connector Id to uniquely identify a path from a parent step to the next step. This may be conditional.
        /// </summary>
        public int WorkflowStepConnectorId { get; set; }
        
        /// <summary>
        /// Id that identifies a step within a workflow.
        /// The parent step in this relationship (parent is the From Step)
        /// </summary>
        public int ParentWorkflowStepId { get; set; }
        
        /// <summary>
        /// Id that identifies a step within a workflow.
        /// The child step. Child is the To step.
        /// </summary>
        public int ChildWorkflowStepId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// </summary>
        public int WorkflowId { get; set; }
        
        /// <summary>
        /// For a Parent with Multiple Conditional Child steps the sort order controls which path is taken. The first condition that evaluates to true will have its path taken (child).
        /// Note! a Branch parent step will take every path that is true (not just the first).
        /// </summary>
        public int? SortOrder { get; set; }
        
        /// <summary>
        /// The Workflow Condition Id identifies each unique condition that can be applied to a workflow.
        /// For this Step Connector the path to the Child will only be taken if the condition evaluates to true (or there is no condition)
        /// </summary>
        public int? WorkflowConditionId { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowStepConnector
    /// </summary>
    public class WorkflowStepConnectorListDto : WorkflowStepConnectorDto
    {
    }
}
