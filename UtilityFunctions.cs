﻿using System.Text;
using System.Reflection;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Google.Protobuf.WellKnownTypes;
using System.Net.Http;
using System.ComponentModel;
using Microsoft.AspNetCore.DataProtection.KeyManagement;

namespace Redi.Prime3.Function.BaseLib
{
	/// <summary>
	/// Various general purpose utilities for Prime3 Micro Services
	/// </summary>
	public class UtilityFunctions
	{
		readonly IHttpContextAccessor _httpContext;
		private readonly IWebHostEnvironment? _env;

		public UtilityFunctions(IHttpContextAccessor c, IWebHostEnvironment env)
		{
			_httpContext = c;
			_env = env;
		}

		public UtilityFunctions(IHttpContextAccessor c)
        {
			_httpContext = c;
		}

		/// <summary>
		/// Access Token (JWT) that was included as a Authorisation Header on the api request.
		/// </summary>
        public string AccessToken
        {
            get
            {
                if (_httpContext.HttpContext != null && !String.IsNullOrEmpty(_httpContext.HttpContext.Request.Headers.Authorization))
                {
                    return _httpContext.HttpContext.Request.Headers.Authorization.ToString().Replace("Bearer ", "");
                }

                return "";
            }
        }

		/// <summary>
		/// The API requestors IP Address
		/// </summary>
        public string? GetRemoteIpAddress
		{
			get
			{
				if (_httpContext.HttpContext != null)
				{
					StringValues ipAddress = "";
					// Use X-Forwarded-For header if it exists (Azure App Container Environment puts client ip here)
					_httpContext.HttpContext.Request.Headers.TryGetValue("x-forwarded-for", out ipAddress);
					if (string.IsNullOrEmpty(ipAddress))
					{
						// Try to use Cloudflares IP Address for client.
						_httpContext.HttpContext.Request.Headers.TryGetValue("cf-connecting-ip", out ipAddress);
					}
					if (string.IsNullOrEmpty(ipAddress))
					{
						ipAddress = _httpContext.HttpContext.Connection.RemoteIpAddress?.ToString();
					}
					return ipAddress;
				}

				return "";
			}
		}

		/// <summary>
		/// Requestors Browser User Agent
		/// </summary>
		public string? GetUserAgent
		{
			get
			{
				if (_httpContext.HttpContext != null)
				{
					return _httpContext.HttpContext.Request.Headers["User-Agent"].ToString();
				}

				return null;
			}
		}

		/// <summary>
		/// Current users UserId
		/// </summary>
		public Guid? UserId
		{
			get
			{
				if (_httpContext.HttpContext != null)
				{
					var userIdClaim = _httpContext.HttpContext?.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "userid");
                    if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out Guid rst))
                    {
                        return rst;
                    }
                }
				return null;

			}
        }

        /// <summary>
        /// Get the TenantId from the users JWT claims
        /// </summary>
        public int? TenantId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {

                    int tenantId;
					var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "tenantid");
                    if (Int32.TryParse(claim?.Value, out tenantId))
                    {
                        return tenantId;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Get the Tenant Type Id from the users JWT claims
        /// </summary>
        public int? TenantTypeId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    int tenantTypeId;
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "tenanttypeid");
                    if (Int32.TryParse(claim?.Value, out tenantTypeId))
                    {
                        return tenantTypeId;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Gets the Tenant Display Name that this user is associated with.
        /// </summary>
        public string? TenantDisplayName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
					var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "tenantdisplayname");
                    return claim?.Value;
                }
                return null;
            }
        }

		/// <summary>
		/// Users Email address - from JWT Claim
		/// </summary>
        public string? UserEmail
		{
			get
			{
				if (_httpContext.HttpContext != null)
				{
					var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "emailaddress" || x.Type.ToLower() == "email");
					return claim?.Value;
				}
				return null;
			}
		}

		/// <summary>
		/// Users CRM PartyId from JWT Claims
		/// </summary>
        public Guid? PartyId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    Guid partyId;
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "partyid");
                    if (Guid.TryParse(claim?.Value, out partyId))
                    {
                        return partyId;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Users (Party) Avatar Image Url
        /// </summary>
        public string? PartyAvatarImageUrl
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "partyavatarimageurl");
                    return claim?.Value ?? UserEmail;
                }
                return null;
            }
        }

        /// <summary>
        /// Users Full Name from JWT Claims
        /// </summary>
        public string? UserFullName
		{
			get
			{
				if (_httpContext.HttpContext != null)
				{
					var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "fullname");
					return claim?.Value ?? UserEmail;
				}
				return null;
			}
		}

        /// <summary>
        /// Users First Name from JWT Claims
        /// </summary>
        public string? UserFirstlName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "firstname");
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Users Last Name from JWT Claims
        /// </summary>
        public string? UserLastName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "lastname");
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Check if user has the requested claim
        /// </summary>
        /// <param name="claimName"></param>
        /// <returns>true - user has claim.</returns>
        public bool HasClaim(string claimName)
		{
			if (_httpContext.HttpContext != null)
			{
				var claim = _httpContext.HttpContext.User.FindFirst(claimName);
				if (claim == null)
				{
					claim = _httpContext.HttpContext.User.Claims.FirstOrDefault(x => String.Equals(x.Type, claimName, StringComparison.OrdinalIgnoreCase) == true);

                }
				return claim?.Value != null;
			}
			return false;
		}

		/// <summary>
		/// Get claim value
		/// </summary>
		/// <value></value>
		public string? GetClaim(string claimName)
		{
			if (_httpContext.HttpContext != null)
			{
				var claim = _httpContext.HttpContext.User.FindFirst(claimName);
                if (claim == null)
                {
                    claim = _httpContext.HttpContext.User.Claims.FirstOrDefault(x => String.Equals(x.Type, claimName, StringComparison.OrdinalIgnoreCase) == true);

                }
                return claim?.Value;
			}
			return null;
		}

        public string LowercaseFirstLetter(string word)
        {
            if (word.Length > 1)
            {
                return Char.ToLowerInvariant(word[0]) + word.Substring(1);
            }
            else
            {
                return word.ToLower();
            }
        }

        /// <summary>
        /// Returns an string of parameters taken from an Object
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string ObjectToParameters(object obj)
		{
			StringBuilder sb = new StringBuilder();

			PropertyInfo[] properties = obj.GetType().GetProperties();
			foreach (PropertyInfo property in properties)
			{
				string name = property.Name;
				object? value = property.GetValue(obj);
				string? stringValue = Convert.ToString(value);

				// Add the parameter to the string representation
				sb.Append($"{name}={stringValue}&");
			}

			string result = sb.ToString().TrimEnd('&');
			return result;
		}

		/// <summary>
		/// Generate a random password / phrase that is easy to remember
		/// Password is made up of
		/// - 3 random words
		/// - # or $
		/// - 0 - 9
		/// - 0 - 9
		/// Example: StarRuleGym$76, QuickLegDown#21
		/// 94 million possible combinations
		/// </summary>
		/// <returns></returns>
		public string GetNewRandomPassword()
		{
			if (_env?.IsDevelopment() == true || ConfigBase.IsRunningLocally == true)
			{
				// For test sites always use standard password.
				return "Letsgo.123";
			}
			Random random = new Random();

			// Generate the new password based on three phrases/words along with $# and a number - 94 million possible combinations
            string[] words = { "Sun", "Moon", "Star", "Sky", "Cloud", "Rain", "Wind", "Tree", "River", "Mountain", "Three", "Four", "Blue", "Red", "Min", "Max", "Only", "Pink", "Yuk"
					, "Link", "Face", "Foot", "Leg", "Cat", "Dog", "Pup", "Read", "Mint", "Good", "Age", "Start", "End", "Eye", "Tile", "Tire", "Wig", "Will", "Hole", "Pluto", "Big"
					, "Small", "Now", "Then", "Yes", "Fill", "Full", "Fold", "Pan", "Peter", "Don", "And", "Milk", "Bread", "Perth", "Aus", "Car", "Bus", "Van", "Ford", "Engine", "Boot", "Bat"
					, "Fun", "Rule", "Did", "Gym", "Run", "Jog", "Hut", "Quick", "Brown", "Down", "Up", "Which", "Doctor", "Call", "Truck", "Dolly"};
            StringBuilder password = new StringBuilder();

            for (int i = 0; i < 3; i++) 
            {
                string word = words[random.Next(words.Length)];
                password.Append(word);
            }

			password.Append(Convert.ToChar(random.Next(35, 36))); // #$
            password.Append(Convert.ToChar(random.Next(48, 57))); // 0-9
            password.Append(Convert.ToChar(random.Next(48, 57))); // 0-9

            return password.ToString();
		}

        /// <summary>
        /// Returns a formatted string for use in the WHERE clause of a SQL statement to filter on TenantId
        /// </summary>
        /// <param name="columnName">TenantId Column Name</param>
        /// <returns></returns>
        public string GetTenantWhereClause(string columnName = "[TenantId]")
        {
            return $@" ({columnName} IS NULL OR {columnName} = @tenantId) ";
        }

		/// <summary>
		/// Returns the current Australian financial year start date (eg. 2024-07-01)
		/// </summary>
        public DateTime CurrentAustralianFinancialYearStartDate
        {
            get
            {
                TimeZoneInfo aestTimeZone = TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time");
                DateTime currentAESTTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, aestTimeZone);
                int currentYear = currentAESTTime.Year;
                int currentMonth = currentAESTTime.Month;
                int financialYearStartMonth = 7;
                int financialYear = currentMonth < financialYearStartMonth ? currentYear - 1 : currentYear;

                return new DateTime(financialYear, 7, 1, 0, 0, 0);
            }
        }

        /// <summary>
        /// Returns the requested Request HTTP Header if it exists
        /// </summary>
        /// <param name="httpHeaderKey">The HTTP Request Header to be returned (eg. Origin, Referer, Accept, Priority, etc)</param>
        /// <returns>The http header if found as a string. Otherwise returns null.</returns>
        public string? GetHttpHeaderAsString(string httpHeaderKey)
		{
			if (_httpContext != null)
			{
				Microsoft.Extensions.Primitives.StringValues stringValue = "";
				_httpContext.HttpContext?.Request.Headers.TryGetValue(httpHeaderKey, out stringValue);
				if (!Microsoft.Extensions.Primitives.StringValues.IsNullOrEmpty(stringValue))
				{
					return stringValue.ToString();
				}
			}
			return null;
		}

        /// <summary>
        /// Returns the requested Request HTTP Header if it exists as the requested type
        /// </summary>
        /// <param name="httpHeaderKey">The HTTP Request Header to be returned (eg. Origin, Referer, Accept, Priority, etc)</param>
        /// <returns>The http header if found converted to the requested Type. Otherwise returns null.</returns>
        public dynamic? GetHttpHeader<T>(string httpHeaderKey)
        {
            if (_httpContext.HttpContext != null)
            {
                Microsoft.Extensions.Primitives.StringValues stringValue;
                _httpContext.HttpContext.Request.Headers.TryGetValue(httpHeaderKey, out stringValue);
                // Skip conversion if null or empty
                if (!Microsoft.Extensions.Primitives.StringValues.IsNullOrEmpty(stringValue))
                {
                    T? result;
                    // Throws error when unable to convert to chosen type
                    result = (T?)TypeDescriptor.GetConverter(typeof(T)).ConvertFromString(stringValue!);
                    return result;
                }
            }
            // Returns null when missing values
            return null;
        }

        /// <summary>
        /// Gets the clients Local-Time header that Prime3 Micro UI's add on all calls.
        /// This user/client device/browser local time with timezone offset
        /// </summary>
        /// <returns>Clients local time as a DateTimeOffset</returns>
        public DateTimeOffset? GetHttpHeaderLocalTime()
		{
			return GetHttpHeader<DateTimeOffset>("Local-Time");
		}

	}
}
