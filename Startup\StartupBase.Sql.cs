using Autofac;
using Microsoft.SqlServer.Types;
using Redi.Filter;
using Sql;

namespace Redi.Prime3.MicroService.BaseLib
{
	public partial class StartupBase
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="provider"></param>
        /// <param name="isDev"></param>
        public static void ApplySqlMiddleware(SqlProvider provider, bool isDev)
        {
            // add SqlGeography udt type
            provider.UdtTypeNameMap = new Dictionary<Type, string> { { typeof(SqlGeography), "Geography" } };

            // add deleted checks
            provider.SetDataMiddleware((context, next) =>
            {
                if (context.Type == CmdType.Select)
                {
                    var filter = new SelectFilter(context.Command, context.SingleArguments, isDev);

                    if (!context.Switches.ContainsKey(SqlSwitch.IgnoreDeletedFilter.ToString()))
                    {
                        filter.AddFilter<IDeletable>(x => x.Deleted == false);
                    }

                    if (filter.HasPendingChanges)
                    {
                        context.Command = filter.Apply();
                    }
                }

                return next();
            });

            // create a middleware to auto handle created ons etc
            provider.SetDataMiddleware((context, next) =>
            {
                var args = context.ObjectArguments;
                var utils = context.Container.Resolve<UtilityFunctions>();
                if (args.Count > 0)
                {
                    for (int i = 0; i < args.Count; i++)
                    {
                        var data = args[i].Data;
                        switch (context.Type)
                        {
                            case CmdType.Create:
                                {
                                    if (data is ICreateable dto)
                                    {
                                        dto.CreatedByName ??= (string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                                        dto.CreatedOn = DateTimeOffset.UtcNow;
                                    }

                                }
                                break;
                            case CmdType.Update:
                                {
                                    if (data is IModifiable dto)
                                    {
                                        dto.ModifiedByName ??= (string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                                        dto.ModifiedOn = DateTimeOffset.UtcNow;
                                    }

                                }
                                break;
                            case CmdType.Delete:
                                {
                                    if (data is IModifiable dto)
                                    {
                                        dto.ModifiedByName ??= (string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                                        dto.ModifiedOn = DateTimeOffset.UtcNow;
                                    }
                                    if (data is IDeletable dto2)
                                    {
                                        dto2.Deleted = true;
                                    }

                                }
                                break;
                            case CmdType.Select:
                            case CmdType.Unknown:
                            default:
                                break;
                        }
                    }

                }
                else
                {
                    // parameters to the param list
                    switch (context.Type)
                    {
                        case CmdType.Create:
                            {
                                // inject these params in
                                if (!context.SingleArguments.ContainsKey("CreatedByName")) context.SingleArguments.Add("CreatedByName", string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                                if (!context.SingleArguments.ContainsKey("CreatedOn")) context.SingleArguments.Add("CreatedOn", DateTimeOffset.UtcNow);
                            }
                            break;
                        case CmdType.Update:
                        case CmdType.Delete:
                            {
                                // inject these params in
                                if (!context.SingleArguments.ContainsKey("ModifiedByName")) context.SingleArguments.Add("ModifiedByName", string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                                if (!context.SingleArguments.ContainsKey("ModifiedOn")) context.SingleArguments.Add("ModifiedOn", DateTimeOffset.UtcNow);
                            }
                            break;
                        case CmdType.Select:
                        case CmdType.Unknown:
                        default:
                            break;
                    }
                }

                if (context.Switches.ContainsKey(SqlSwitch.AddCreatedOn.ToString()))
                {
                    if (!context.SingleArguments.ContainsKey("CreatedByName")) context.SingleArguments.Add("CreatedByName", string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                    if (!context.SingleArguments.ContainsKey("CreatedOn")) context.SingleArguments.Add("CreatedOn", DateTimeOffset.UtcNow);
                }
                if (context.Switches.ContainsKey(SqlSwitch.AddModifiedOn.ToString()))
                {
                    if (!context.SingleArguments.ContainsKey("ModifiedByName")) context.SingleArguments.Add("ModifiedByName", string.IsNullOrEmpty(utils.UserFullName) ? "System" : utils.UserFullName);
                    if (!context.SingleArguments.ContainsKey("ModifiedOn")) context.SingleArguments.Add("ModifiedOn", DateTimeOffset.UtcNow);
                }

                //Always add TenantId
                //if (!context.SingleArguments.ContainsKey("TenantId") && !context.SingleArguments.ContainsKey("tenantId"))
                //{
                //    context.SingleArguments.Add("TenantId", utils.TenantId);
                //}

                return next();
            });
        }
    }
}
