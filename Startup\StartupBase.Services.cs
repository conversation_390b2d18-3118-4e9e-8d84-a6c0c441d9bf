using Azure.Identity;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Azure;
using Microsoft.IdentityModel.Tokens;
using Microsoft.IdentityModel.JsonWebTokens;
using System.Reflection;
using System.Text;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Dapr.Client;
using Dapr.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Redi.Prime3.MicroService.Logger;

namespace Redi.Prime3.MicroService.BaseLib
{
	public partial class StartupBase
    {
		/// <summary>
		/// Configure environment services
		/// - Logging
		/// - Azure Clients
		/// - Memory Cache
		/// - Dapr
		/// - Header Forwarding
		/// - JWT Authentication
		/// </summary>
		/// <param name="services"></param>
		/// <param name="host"></param>
		/// <returns></returns>
		public AuthenticationBuilder ConfigureServices(IServiceCollection services, ConfigureHostBuilder host, ConfigureWebHostBuilder webHost, ConfigurationManager configuration)
		{
			webHost.UseSentry();
#pragma warning disable ASP0013 // Suggest switching from using Configure methods to WebApplicationBuilder.Configuration
            host.ConfigureAppConfiguration(config =>
            {
                var daprClient = new DaprClientBuilder().Build();
                config.AddDaprSecretStore(ConfigBase.SecretStore!, daprClient);
            })
            .ConfigureLogging((hostingContext, logging) =>
            {
                logging.ClearProviders();
                logging.AddConfiguration(hostingContext.Configuration);
                logging.AddDebug();
                logging.AddConsole();
                logging.AddProvider(new AppLoggerProvider(hostingContext.Configuration));
            });
#pragma warning restore ASP0013 // Suggest switching from using Configure methods to WebApplicationBuilder.Configuration

            services.AddAzureClients(cb => AddAzureClients(cb));
            services.AddMemoryCache(); // To be retired.
#pragma warning disable EXTEXP0018 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            services.AddHybridCache(options => {
				options.MaximumKeyLength = 1024;
				options.MaximumPayloadBytes = 1024 * 1024; // 1Mb
			});
#pragma warning restore EXTEXP0018 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
            services.AddCors();
			// Add services to the container.
			services.AddHttpClient();
			services.AddDaprClient();
			services.AddControllers()
				    .AddDapr()
					.AddNewtonsoftJson(opts =>
			{
				// ignore null values to save bandwidth
				opts.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
				opts.SerializerSettings.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Ignore;
				// serialize enums as their string value
				opts.SerializerSettings.Converters.Add(new Newtonsoft.Json.Converters.StringEnumConverter());
				// add geography sql type converters
				//opts.SerializerSettings.AddSqlConverters();
			});
			services.AddEndpointsApiExplorer();
			services.AddSwaggerGen(options =>
			{
				options.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo()
				{
					Title = "Prime3 " + AppId.Replace("redi-microservice-", "").ToUpperInvariant() + " Micro Service"
				});
                // Get all XML documentation files in the executing directory (each project will have its own xml file)
#pragma warning disable CS8604 // Possible null reference argument.
                foreach (var filePath in System.IO.Directory.GetFiles(Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)), "*.xml"))
                {
                    try
                    {
                        options.IncludeXmlComments(filePath);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                }
#pragma warning restore CS8604 // Possible null reference argument.
            });
			var authentaticonBuilder = AddCustomAuthentication(services, environmentRoot);
			services.Configure<ForwardedHeadersOptions>(options =>
			{
				options.ForwardedHeaders =
				   ForwardedHeaders.XForwardedFor |
				   ForwardedHeaders.XForwardedHost |
				   ForwardedHeaders.XForwardedProto;

				options.ForwardLimit = 2;  //Limit number of proxy hops trusted
				options.KnownNetworks.Clear();
				options.KnownProxies.Clear();
			});

			return authentaticonBuilder;
        }

		private static AuthenticationBuilder AddCustomAuthentication(IServiceCollection services, IWebHostEnvironment environmentRoot)
		{ 
            JsonWebTokenHandler.DefaultInboundClaimTypeMap.Clear(); // => remove default claims
			services.AddAuthorization();
			var authentaticonBuilder = services
				.AddAuthentication(options =>
				 {
					 options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
					 options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
					 options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;

				 })
				.AddJwtBearer(cfg =>
				 {
					 cfg.RequireHttpsMetadata = !environmentRoot.IsDevelopment();
					 cfg.SaveToken = true;
					 cfg.TokenValidationParameters = new TokenValidationParameters
					 {
						 ValidIssuer = ConfigBase.JwtIssuer,
						 ValidAudience = ConfigBase.JwtIssuer,

						 ValidateIssuer = true,
						 ValidateAudience = false,
						 ValidateLifetime = ConfigBase.JwtExpireDisabled != "true",
						 ValidateIssuerSigningKey = true,

						 IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(ConfigBase.JwtKey!)),
						 ClockSkew = TimeSpan.Zero // remove delay of token when expire
					 };

					 // We have to hook the OnMessageReceived event in order to
					 // allow the JWT authentication handler to read the access
					 // token from the query string when a WebSocket or 
					 // Server-Sent Events request comes in.
					 cfg.Events = new JwtBearerEvents
					 {
						 OnMessageReceived = context =>
						 {
							 var accessToken = context.Request.Query["access_token"];

							 // If the request is for our hub...
							 var path = context.HttpContext.Request.Path;
							 if (!string.IsNullOrEmpty(accessToken) && (path.StartsWithSegments("/ws")))
							 {
								 // Read the token out of the query string
								 context.Token = accessToken;
							 }
							 return Task.CompletedTask;
						 }
					 };
				 });

			return authentaticonBuilder;

        }

        private void AddAzureClients(AzureClientFactoryBuilder clientBuilder)
        {
            AddAzureQueueClientFactory(clientBuilder);
        }

        public Azure.Core.TokenCredential AzureTokenCredentials()
        {
            if (environmentRoot.IsProduction() || environmentRoot.IsDevelopment())
                return new DefaultAzureCredential();

            return new ClientSecretCredential(
                ConfigBase.AzureTenantId,
                ConfigBase.AzureClientId,
                ConfigBase.AzureClientSecret
            );
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="clientBuilder"></param>
        /// <param name="configRoot"></param>
        /// <param name="env"></param>
        private void AddAzureQueueClientFactory(AzureClientFactoryBuilder clientBuilder)
        {
            //Default
            if (!string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                clientBuilder.AddQueueServiceClient(new Uri($"https://{ConfigBase.AzureStorageAccountName}.queue.core.windows.net"))
                    .WithCredential(AzureTokenCredentials())
                    .WithName("Default");
            }
            // Notifications
            var notificationsStorageAccountName = ConfigBase.AzureStorageNotificationAccountName;
            if (!string.IsNullOrEmpty(notificationsStorageAccountName))
            {
                clientBuilder.AddQueueServiceClient(new Uri($"https://{notificationsStorageAccountName}.queue.core.windows.net"))
                    .WithCredential(AzureTokenCredentials())
                    .WithName("Notifications");
            }
        }
    }
}
