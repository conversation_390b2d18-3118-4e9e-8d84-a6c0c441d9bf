using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Text.Json;
using Microsoft.Extensions.Azure;
using Azure.Identity;
using Azure.Core;

namespace Redi.Prime3.Function.BaseLib
{
    public partial class StartupBase
    {
        /// <summary>
        /// This method gets called by the functions Program class during startup. Use this method to add services to the container. <para/>
        /// Adds<para/>
        /// - Hybrid Cache (use redi Cache class)<para/>
        /// - Azure Queue Client<para/>
        /// - Azure Blob Client<para/>
        /// - Azure Vault client
        /// </summary>
        /// <param name="services"></param>
        /// <param name="builder"></param>
        /// <exception cref="StartupException"></exception>
        public void ConfigureServices(IServiceCollection services,IHostBuilder builder)
        {
#pragma warning disable EXTEXP0018 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.

            services.AddHybridCache(options =>
            {
                options.MaximumKeyLength = 1024;
                options.MaximumPayloadBytes = 1024 * 1024; // 1Mb
            });
            services.Configure<JsonSerializerOptions>(jsonOptions =>
            {
                //jsonOptions.Converters.Add(new DateTimeJsonConverter());
            }).AddHttpClient<IHttpClientFactory>();

            // Add Azure Clients for Key Vault, Queues, and Blobs
            services.AddAzureClients(cb => AddAzureClients(cb));

#pragma warning restore EXTEXP0018 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.

        }

        private void AddAzureClients(AzureClientFactoryBuilder clientBuilder)
        {
            AddAzureQueueClientFactory(clientBuilder);
            AddAzureSecretClient(clientBuilder);
            AddAzureBlobClientFactory(clientBuilder);
        }

        private Azure.Core.TokenCredential AzureTokenCredentials()
        {
            if (ConfigBase.IsRunningLocally == false)
                return new DefaultAzureCredential();

            if (string.IsNullOrEmpty(ConfigBase.AzureTenantId)) { throw new StartupException("AzureTenantId missing. For local testing local.settings.json must have a valid AzureTenantId specified"); }
            if (string.IsNullOrEmpty(ConfigBase.AzureClientId)) { throw new StartupException("AzureClientId missing. For local testing local.settings.json must have a valid AzureClientId specified"); }
            if (string.IsNullOrEmpty(ConfigBase.AzureClientSecret)) { throw new StartupException("AzureClientSecret missing. For local testing local.settings.json must have a valid AzureClientSecret specified"); }

            return new ClientSecretCredential(
                ConfigBase.AzureTenantId,
                ConfigBase.AzureClientId,
                ConfigBase.AzureClientSecret
            );
        }

        /// <summary>
        /// Azure Queue CLient that enables access to queues.
        /// </summary>
        /// <param name="clientBuilder"></param>
        private void AddAzureQueueClientFactory(AzureClientFactoryBuilder clientBuilder)
        {
            //Default
            if (!string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                clientBuilder.AddQueueServiceClient(new Uri($"https://{ConfigBase.AzureStorageAccountName}.queue.core.windows.net"))
                    .WithCredential(AzureTokenCredentials())
                    .WithName("Default");
            }
            // Notifications
            if (!string.IsNullOrEmpty(ConfigBase.AzureStorageNotificationAccountName))
            {
                clientBuilder.AddQueueServiceClient(new Uri($"https://{ConfigBase.AzureStorageNotificationAccountName}.queue.core.windows.net"))
                    .WithCredential(AzureTokenCredentials())
                    .WithName("Notifications");
            }
        }

        /// <summary>
        /// Azure Secret Client that enables access to the KeyVault
        /// </summary>
        /// <param name="clientBuilder"></param>
        private void AddAzureSecretClient(AzureClientFactoryBuilder clientBuilder)
        {
            if (!string.IsNullOrEmpty(ConfigBase.KeyVaultName))
            {
                //Default
                clientBuilder.AddSecretClient(new Uri($"https://{ConfigBase.KeyVaultName}.vault.azure.net"))
                    .WithCredential(AzureTokenCredentials())
                    .ConfigureOptions(options =>
                    {
                        options.Retry.Delay = TimeSpan.FromSeconds(2);
                        options.Retry.MaxDelay = TimeSpan.FromSeconds(16);
                        options.Retry.MaxRetries = 5;
                        options.Retry.Mode = RetryMode.Exponential;
                    })
                    .WithName("Default");
            }
        }

        /// <summary>
        /// BlobClient factory that enables access to Azure Storage Blobs
        /// </summary>
        /// <param name="builder"></param>
        private void AddAzureBlobClientFactory(AzureClientFactoryBuilder clientBuilder)
        {
            //Default (General purpose file blob storage)
            if (!string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                clientBuilder.AddBlobServiceClient(new Uri($"https://{ConfigBase.AzureStorageAccountName}.blob.core.windows.net"))
                        .WithCredential(AzureTokenCredentials())
                        .WithName("Default");
            }
        }
    }
}
