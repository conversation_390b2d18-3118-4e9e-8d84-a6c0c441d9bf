using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;
using Microsoft.Data.SqlClient;
using System.Linq;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic
{
    public class SystemSetting : BusinessLogicBase
    {
        public SystemSetting(IUnitOfWork unitOfWork, ILogger logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        public async Task<BaseSystemSettingDto> GetSystemSettingAsync(string systemSettingCode)
        {
            string sql = @"
SELECT [SystemSettingCode]
      ,[Value]
      ,[Description]
  FROM [common].[SystemSetting]
WHERE [SystemSettingCode] = @systemSettingCode";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("systemSettingCode", systemSettingCode);

                return await command.SelectSingle<BaseSystemSettingDto>();
            }
        }

        public async Task<List<BaseSystemSettingDto>> GetSystemSettingsListAsync(params string[] systemSettingCodes)
        {
            string sql = @"
SELECT [SystemSettingCode]
      ,[Value]
      ,[Description]
  FROM [common].[SystemSetting]
WHERE [SystemSettingCode] IN (@systemSettingCode)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("systemSettingCodes", systemSettingCodes.ToList());

                return await command.SelectMany<BaseSystemSettingDto>();
            }
        }

        public static async Task<Dictionary<string, string?>> GetSystemSettingsAsync(string connectionString, string[] systemSettingCodes)
        {
            string sql = @"
IF EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='common' and t.name='SystemSetting')
SELECT [SystemSettingCode]
      ,[Value]
      ,[Description]
  FROM [common].[SystemSetting]
WHERE [SystemSettingCode] IN (@codes)";
            var resultset = new Dictionary<string, string?>();
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(sql, connection))
                {
                    var parameterNames = new List<string>();
                    for (int i = 0; i < systemSettingCodes.Count(); i++)
                    {
                        var paramName = $"@codes{i}";
                        command.Parameters.AddWithValue(paramName, systemSettingCodes.ElementAt(i));
                        parameterNames.Add(paramName);
                    }

                    command.CommandText = command.CommandText.Replace($"@codes", string.Join(",", parameterNames));

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var key = reader["SystemSettingCode"].ToString();
                            var val = reader["Value"].ToString();
                            if (key != null)
                            {
                                resultset.Add(key, val);
                            }
                        }
                    }
                }
            }
            return resultset;
        }
    }
}
