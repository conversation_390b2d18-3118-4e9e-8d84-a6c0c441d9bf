using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class Workflow : BusinessLogicBase
    {
        public Workflow(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single Workflow by ID
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <returns>WorkflowDto or null if not found</returns>
        internal async Task<WorkflowDto?> GetAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId]
                      ,[TenantId]
                      ,[Name]
                      ,[LastExecutedOn]
                      ,[IsEnabled]
                      ,[FirstWorkflowStepId]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId
                  AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                return await command.SelectSingle<WorkflowDto>();
            }
        }

        /// <summary>
        /// Create a new Workflow
        /// </summary>
        /// <param name="dto">The Workflow data</param>
        internal async Task CreateAsync(WorkflowDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                throw new HttpRequestException("Name cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string sql = @"
                INSERT INTO [workflow].[Workflow]
                ([TenantId], [Name], [LastExecutedOn], [IsEnabled], [FirstWorkflowStepId], [CreatedOn], [CreatedByName])
                VALUES
                (@TenantId, @Name, @LastExecutedOn, @IsEnabled, @FirstWorkflowStepId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing Workflow
        /// </summary>
        /// <param name="dto">The Workflow data</param>
        internal async Task UpdateAsync(WorkflowDto dto)
        {
            if (dto.WorkflowId <= 0)
            {
                throw new HttpRequestException("WorkflowId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Name))
            {
                throw new HttpRequestException("Name cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WorkflowId);
            if (exists == null)
            {
                throw new HttpRequestException($"Workflow with ID '{dto.WorkflowId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[Workflow]
                SET [TenantId] = @TenantId,
                    [Name] = @Name,
                    [LastExecutedOn] = @LastExecutedOn,
                    [IsEnabled] = @IsEnabled,
                    [FirstWorkflowStepId] = @FirstWorkflowStepId,
                    [ModifiedOn] = @ModifiedOn,
                    [ModifiedByName] = @ModifiedByName
                WHERE [WorkflowId] = @WorkflowId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a Workflow (soft delete)
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        internal async Task DeleteAsync(int workflowId)
        {
            var exists = await GetAsync(workflowId);
            if (exists == null)
            {
                throw new HttpRequestException($"Workflow with ID '{workflowId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[Workflow]
                SET [Deleted] = 1,
                    [ModifiedOn] = @ModifiedOn
                WHERE [WorkflowId] = @workflowId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("workflowId", workflowId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of Workflows with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <returns>List of Workflows</returns>
        internal async Task<ListResponseDto<WorkflowListDto>> GetListAsync(StandardListParameters standardListParameters, int? tenantId = null, bool? isEnabled = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WorkflowId", "[Workflow].[WorkflowId]" },
                { "Name", "[Workflow].[Name]" },
                { "TenantId", "[Workflow].[TenantId]" },
                { "IsEnabled", "[Workflow].[IsEnabled]" },
                { "LastExecutedOn", "[Workflow].[LastExecutedOn]" },
                { "CreatedOn", "[Workflow].[CreatedOn]" },
                { "ModifiedOn", "[Workflow].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [WorkflowId]
                      ,[TenantId]
                      ,[Name]
                      ,[LastExecutedOn]
                      ,[IsEnabled]
                      ,[FirstWorkflowStepId]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[Workflow]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (isEnabled.HasValue)
            {
                sql += " AND [IsEnabled] = @isEnabled";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "Name");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WorkflowListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                result.List = await command.SelectMany<WorkflowListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[Workflow]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (isEnabled.HasValue)
            {
                countSql += " AND [IsEnabled] = @isEnabled";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
