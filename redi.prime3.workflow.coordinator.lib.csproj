<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Redi.Prime3.WorkflowCoordinator.Lib</PackageId>
    <Version>1.0.0</Version>
    <Authors>Redi Software</Authors>
    <Company>Redi Software</Company>
    <Product>Redi Prime3 Workflow Coordinator Library</Product>
    <Description>A .NET 8 class library for workflow coordination functionality in Redi Prime3 applications</Description>
    <PackageTags>redi;prime3;workflow;coordinator;library</PackageTags>
    <RepositoryType>git</RepositoryType>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.1.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Azure.Storage.Queues" Version="12.21.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.9.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Redi.Sql" Version="8.0.0" />
    <PackageReference Include="Redi.Prime3.Logger" Version="1.0.0" />
    <PackageReference Include="System.ComponentModel.TypeConverter" Version="4.3.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

</Project>
