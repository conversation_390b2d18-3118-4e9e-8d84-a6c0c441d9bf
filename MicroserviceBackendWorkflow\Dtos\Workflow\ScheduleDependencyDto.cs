using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// A row for each dependency a schedule has.
    /// </summary>
    [Mappable(nameof(ScheduleDependencyId))]
    public class ScheduleDependencyDto
    {
        /// <summary>
        /// The unique identifier for the schedule dependency
        /// </summary>
        public int ScheduleDependencyId { get; set; }
        
        /// <summary>
        /// Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
        /// </summary>
        public int ScheduleId { get; set; }
        
        /// <summary>
        /// The dependency key is used to uniquely identify a dependency
        /// </summary>
        public string DependencyKey { get; set; } = string.Empty;
        
        /// <summary>
        /// Identifies how to validate the DependencyDate for a completed Dependency.
        /// {Column}{+/-}{number}{period}
        /// 
        /// Column: ScheduledAt, Now
        /// Period: d for days; h for hours; m for minutes, mt for months
        /// 
        /// ScheduledAt-1d
        /// To be true DependencyDate >= ScheduledAt minus 1 day.
        /// 
        /// ScheduledAt
        /// To be true DependencyDate >= ScheduledAt
        /// 
        /// ScheduledAt-2mt
        /// To be true DependencyDate >= ScheduledAt minus 2 months.
        /// 
        /// The required date will be stored in the wfJobDependency.RequiredDependencyDate column.
        /// </summary>
        public string? DependencyDateRule { get; set; }
        
        /// <summary>
        /// Indicates how the RequiredDependencyDate is compare to the Completed DependencyDate.
        /// Default is false and will allow DependencyDate to be greater than or equal to RequiredDependencyDate. 
        /// When true the dates must match.
        /// </summary>
        public bool IsDateMustMatch { get; set; }
    }

    /// <summary>
    /// List DTO for ScheduleDependency
    /// </summary>
    public class ScheduleDependencyListDto : ScheduleDependencyDto
    {
    }
}
