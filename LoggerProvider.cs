using System.Collections.Concurrent;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.MicroService.Logger
{
	public class AppLoggerProvider : ILoggerProvider
	{
		readonly IConfiguration _configuration;
		private IDisposable? _sentry;
		public AppLoggerProvider(IConfiguration conf)
		{
			_configuration = conf;
		}

		private readonly ConcurrentDictionary<string, AppLogger> _loggers = new ConcurrentDictionary<string, AppLogger>();
		private readonly LoggerExternalScopeProvider _scopeProvider = new LoggerExternalScopeProvider();

		public ILogger CreateLogger(string categoryName)
		{
			// Set log Level based on the logging settings
			LogLevel logLevel = LogLevel.Warning;
			if (Enum.TryParse(_configuration["Logging:LogLevel:default"], ignoreCase: true, out LogLevel defaultLogLevel)) { logLevel = defaultLogLevel;  };
            if (Enum.TryParse(_configuration[$"Logging:LogLevel:{categoryName}"], ignoreCase: true, out LogLevel custLogLevel)) { logLevel = custLogLevel; };
            double tracesSampleRate = 1;
			double.TryParse(_configuration["SENTRY_TRACES_SAMPLE_RATE"], out tracesSampleRate);
			_sentry = SentrySdk.Init(options =>
			{
				options.Environment = _configuration["Environment"];
				options.Dsn = _configuration["SENTRY_DSN"];
				options.TracesSampleRate = tracesSampleRate;
				options.Debug = false;
			});
			var configurationDbString = _configuration["ConnectionStringsBaseDbConn"];

            var isSentryEnabled = !string.IsNullOrEmpty(_configuration["SENTRY_DSN"]);

			if (categoryName != null && (categoryName.StartsWith("Microsoft") || categoryName.StartsWith("Azure"))) { logLevel = LogLevel.Error; }

			return _loggers.GetOrAdd(categoryName!, new AppLogger(logLevel, configurationDbString!, isSentryEnabled, categoryName!, _scopeProvider));
        }

        public void Dispose()
		{
			if (_sentry != null)
            {
				_sentry.Dispose();
			}
		}
	}
}
