using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using System.ComponentModel;
using System.Reflection;
using System.Security.Claims;
using System.Text;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic
{
    /// <summary>
    /// Various general purpose utilities for Prime3 Workflow Coordinator
    /// </summary>
    public class UtilityFunctions
    {
        readonly IHttpContextAccessor _httpContext;
        private readonly IWebHostEnvironment? _env;

        public UtilityFunctions(IHttpContextAccessor c, IWebHostEnvironment env)
        {
            _httpContext = c;
            _env = env;
        }

        public UtilityFunctions(IHttpContextAccessor c)
        {
            _httpContext = c;
        }

        /// <summary>
        /// Users TenantId from JWT Claims
        /// </summary>
        public int? TenantId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    int tenantId;
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "tenantid");
                    if (int.TryParse(claim?.Value, out tenantId))
                    {
                        return tenantId;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Users Email from JWT Claims
        /// </summary>
        public string? UserEmail
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Email);
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Users Full Name from JWT Claims
        /// </summary>
        public string? UserFullName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Name);
                    return claim?.Value ?? UserEmail;
                }
                return "System";
            }
        }

        /// <summary>
        /// Users First Name from JWT Claims
        /// </summary>
        public string? UserFirstName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.GivenName);
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Users Last Name from JWT Claims
        /// </summary>
        public string? UserLastName
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Surname);
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Users CRM PartyId from JWT Claims
        /// </summary>
        public Guid? PartyId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    Guid partyId;
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "partyid");
                    if (Guid.TryParse(claim?.Value, out partyId))
                    {
                        return partyId;
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// Users (Party) Avatar Image Url
        /// </summary>
        public string? PartyAvatarImageUrl
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type.ToLower() == "partyavatarimageurl");
                    return claim?.Value ?? UserEmail;
                }
                return null;
            }
        }

        /// <summary>
        /// Users Role from JWT Claims
        /// </summary>
        public string? UserRole
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.Role);
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Users UserId from JWT Claims
        /// </summary>
        public string? UserId
        {
            get
            {
                if (_httpContext.HttpContext != null)
                {
                    var claim = _httpContext.HttpContext.User?.Claims?.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
                    return claim?.Value;
                }
                return null;
            }
        }

        /// <summary>
        /// Check if the current user is in a specific role
        /// </summary>
        /// <param name="role">The role to check</param>
        /// <returns>True if user is in role, false otherwise</returns>
        public bool IsInRole(string role)
        {
            if (_httpContext.HttpContext != null)
            {
                return _httpContext.HttpContext.User?.IsInRole(role) ?? false;
            }
            return false;
        }

        /// <summary>
        /// Get the current request's IP address
        /// </summary>
        public string? GetClientIpAddress()
        {
            if (_httpContext.HttpContext != null)
            {
                return _httpContext.HttpContext.Connection.RemoteIpAddress?.ToString();
            }
            return null;
        }

        /// <summary>
        /// Get the current request's user agent
        /// </summary>
        public string? GetUserAgent()
        {
            if (_httpContext.HttpContext != null)
            {
                return _httpContext.HttpContext.Request.Headers["User-Agent"].FirstOrDefault();
            }
            return null;
        }

        public string LowercaseFirstLetter(string word)
        {
            if (word.Length > 1)
            {
                return Char.ToLowerInvariant(word[0]) + word.Substring(1);
            }
            else
            {
                return word.ToLower();
            }
        }

        /// <summary>
        /// Returns an string of parameters taken from an Object
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public string ObjectToParameters(object obj)
        {
            StringBuilder sb = new StringBuilder();

            PropertyInfo[] properties = obj.GetType().GetProperties();
            foreach (PropertyInfo property in properties)
            {
                string name = property.Name;
                object? value = property.GetValue(obj);
                string? stringValue = Convert.ToString(value);

                // Add the parameter to the string representation
                sb.Append($"{name}={stringValue}&");
            }

            string result = sb.ToString().TrimEnd('&');
            return result;
        }

        /// <summary>
        /// Generate a random password / phrase that is easy to remember
        /// Password is made up of
        /// - 3 random words
        /// - # or $
        /// - 0 - 9
        /// - 0 - 9
        /// Example: StarRuleGym$76, QuickLegDown#21
        /// 94 million possible combinations
        /// </summary>
        /// <returns></returns>
        public string GetNewRandomPassword()
        {
            if (_env?.IsDevelopment() == true)
            {
                // For test sites always use standard password.
                return "Letsgo.123";
            }
            Random random = new Random();

            // Generate the new password based on three phrases/words along with $# and a number - 94 million possible combinations
            string[] words = { "Sun", "Moon", "Star", "Sky", "Cloud", "Rain", "Wind", "Tree", "River", "Mountain", "Three", "Four", "Blue", "Red", "Min", "Max", "Only", "Pink", "Yuk"
                        , "Link", "Face", "Foot", "Leg", "Cat", "Dog", "Pup", "Read", "Mint", "Good", "Age", "Start", "End", "Eye", "Tile", "Tire", "Wig", "Will", "Hole", "Pluto", "Big"
                        , "Small", "Now", "Then", "Yes", "Fill", "Full", "Fold", "Pan", "Peter", "Don", "And", "Milk", "Bread", "Perth", "Aus", "Car", "Bus", "Van", "Ford", "Engine", "Boot", "Bat"
                        , "Fun", "Rule", "Did", "Gym", "Run", "Jog", "Hut", "Quick", "Brown", "Down", "Up", "Which", "Doctor", "Call", "Truck", "Dolly"};
            StringBuilder password = new StringBuilder();

            for (int i = 0; i < 3; i++)
            {
                string word = words[random.Next(words.Length)];
                password.Append(word);
            }

            password.Append(Convert.ToChar(random.Next(35, 36))); // #$
            password.Append(Convert.ToChar(random.Next(48, 57))); // 0-9
            password.Append(Convert.ToChar(random.Next(48, 57))); // 0-9

            return password.ToString();
        }

        /// <summary>
        /// Returns a formatted string for use in the WHERE clause of a SQL statement to filter on TenantId
        /// </summary>
        /// <param name="columnName">TenantId Column Name</param>
        /// <returns></returns>
        public string GetTenantWhereClause(string columnName = "[TenantId]")
        {
            return $@" ({columnName} IS NULL OR {columnName} = @tenantId) ";
        }

        /// <summary>
        /// Returns the current Australian financial year start date (eg. 2024-07-01)
        /// </summary>
        public DateTime CurrentAustralianFinancialYearStartDate
        {
            get
            {
                TimeZoneInfo aestTimeZone = TimeZoneInfo.FindSystemTimeZoneById("AUS Eastern Standard Time");
                DateTime currentAESTTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, aestTimeZone);
                int currentYear = currentAESTTime.Year;
                int currentMonth = currentAESTTime.Month;
                int financialYearStartMonth = 7;
                int financialYear = currentMonth < financialYearStartMonth ? currentYear - 1 : currentYear;

                return new DateTime(financialYear, 7, 1, 0, 0, 0);
            }
        }

        /// <summary>
        /// Gets a header value from the current HTTP request
        /// </summary>
        /// <typeparam name="T">The type to convert the header value to</typeparam>
        /// <param name="headerName">The name of the header</param>
        /// <returns>The header value converted to type T, or null if not found</returns>
        public T? GetHttpHeader<T>(string headerName)
        {
            if (_httpContext.HttpContext != null)
            {
                var headerValue = _httpContext.HttpContext.Request.Headers[headerName].FirstOrDefault();
                if (!string.IsNullOrEmpty(headerValue))
                {
                    try
                    {
                        var result = (T?)TypeDescriptor.GetConverter(typeof(T)).ConvertFromString(headerValue);
                        return result;
                    }
                    catch
                    {
                        // Conversion failed, return default
                    }
                }
            }
            return default(T);
        }

        /// <summary>
        /// Gets the clients Local-Time header that Prime3 Micro UI's add on all calls.
        /// This user/client device/browser local time with timezone offset
        /// </summary>
        /// <returns>Clients local time as a DateTimeOffset</returns>
        public DateTimeOffset? GetHttpHeaderLocalTime()
        {
            return GetHttpHeader<DateTimeOffset>("Local-Time");
        }

        /// <summary>
        /// Check if the application is running in development environment
        /// </summary>
        public bool IsDevelopment => _env?.IsDevelopment() ?? false;

        /// <summary>
        /// Check if the application is running in production environment
        /// </summary>
        public bool IsProduction => _env?.IsProduction() ?? false;

        /// <summary>
        /// Get the current environment name
        /// </summary>
        public string? EnvironmentName => _env?.EnvironmentName;
    }
}
