#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["MicroserviceBackendWorkflow/MicroserviceBackendWorkflow.csproj", "MicroserviceBackendWorkflow/"]
COPY ["MicroserviceContract/MicroserviceContract.csproj", "MicroserviceContract/"]
COPY ["MicroserviceDashboardContract/MicroserviceDashboardContract.csproj", "MicroserviceDashboardContract/"]
COPY ["NuGet.Config", "/"]
RUN dotnet restore "MicroserviceBackendWorkflow/MicroserviceBackendWorkflow.csproj"
COPY . .
RUN rm NuGet.Config
WORKDIR "/src/MicroserviceBackendWorkflow"
RUN dotnet build "MicroserviceBackendWorkflow.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MicroserviceBackendWorkflow.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MicroserviceBackendWorkflow.dll"]
