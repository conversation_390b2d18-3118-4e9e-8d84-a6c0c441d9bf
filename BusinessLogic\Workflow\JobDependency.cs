using RediAzurefunctionBase.Dtos;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class JobDependency : BusinessLogicBase
    {
        public JobDependency(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get a list of WfJobDependencies with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="wfJobId">Filter by workflow job ID</param>
        /// <param name="dependencyKey">Filter by dependency key</param>
        /// <returns>List of WfJobDependencies</returns>
        internal async Task<ListResponseDto<WfJobDependencyListDto>> GetListAsync(
            StandardListParameters? standardListParameters = null,
            long? wfJobId = null,
            string? dependencyKey = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobDependencyId", "[WfJobDependency].[WfJobDependencyId]" },
                { "WfJobId", "[WfJobDependency].[WfJobId]" },
                { "DependencyKey", "[WfJobDependency].[DependencyKey]" },
                { "RequiredDependencyDate", "[WfJobDependency].[RequiredDependencyDate]" }
            };

            string sql = @"
                SELECT [WfJobDependencyId]
                      ,[WfJobId]
                      ,[DependencyKey]
                      ,[RequiredDependencyDate]
                FROM [workflow].[WfJobDependency]
                WHERE 1=1";

            if (wfJobId.HasValue)
            {
                sql += " AND [WfJobId] = @wfJobId";
            }

            if (!string.IsNullOrEmpty(dependencyKey))
            {
                sql += " AND [DependencyKey] = @dependencyKey";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WfJobDependencyId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobDependencyListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }

                if (!string.IsNullOrEmpty(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }

                result.List = await command.SelectMany<WfJobDependencyListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJobDependency]
                WHERE 1=1";

            if (wfJobId.HasValue)
            {
                countSql += " AND [WfJobId] = @wfJobId";
            }

            if (!string.IsNullOrEmpty(dependencyKey))
            {
                countSql += " AND [DependencyKey] = @dependencyKey";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }

                if (!string.IsNullOrEmpty(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}