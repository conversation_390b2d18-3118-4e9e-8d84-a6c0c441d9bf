using RediAzurefunctionBase.Dtos;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class JobDependency : BusinessLogicBase
    {
        public JobDependency(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get a list of WfJobDependencies with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="wfJobId">Filter by workflow job ID</param>
        /// <param name="dependencyKey">Filter by dependency key</param>
        /// <param name="isCompleted">Filter by completion status - true for completed dependencies, false for incomplete, null for all</param>
        /// <returns>List of WfJobDependencies</returns>
        internal async Task<ListResponseDto<WfJobDependencyListDto>> GetListAsync(
            StandardListParameters? standardListParameters = null,
            long? wfJobId = null,
            string? dependencyKey = null,
            bool? isCompleted = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WfJobDependencyId", "[WfJobDependency].[WfJobDependencyId]" },
                { "WfJobId", "[WfJobDependency].[WfJobId]" },
                { "DependencyKey", "[WfJobDependency].[DependencyKey]" },
                { "RequiredDependencyDate", "[WfJobDependency].[RequiredDependencyDate]" }
            };

            string baseQuery = @"
                SELECT [wfJobDependency].[WfJobDependencyId]
                      ,[wfJobDependency].[WfJobId]
                      ,[wfJobDependency].[DependencyKey]
                      ,[wfJobDependency].[RequiredDependencyDate]
                FROM [workflow].[WfJobDependency] [wfJobDependency]
                INNER JOIN [workflow].[WfJob] [wfJob] ON [wfJob].[WfJobId] = [wfJobDependency].[WfJobId]";

            string completionCondition = @"
                EXISTS (
                    SELECT 1
                    FROM [workflow].[DependencyComplete] [dependencyComplete]
                    INNER JOIN [workflow].[ScheduleDependency] [scheduleDependency] ON [dependencyComplete].[ScheduleId] = [wfJob].[ScheduleId] AND [dependencyComplete].[DependencyKey] = [wfJobDependency].[DependencyKey]
                    WHERE [dependencyComplete].[DependencyKey] = [wfJobDependency].[DependencyKey]
                      AND (
                          ([scheduleDependency].[IsDateMustMatch] = 1 AND [dependencyComplete].[DependencyDate] = [wfJobDependency].[RequiredDependencyDate])
                          OR
                          ([scheduleDependency].[IsDateMustMatch] = 0 AND [dependencyComplete].[DependencyDate] >= [wfJobDependency].[RequiredDependencyDate])
                      )
                )";

            string sql = baseQuery + " WHERE 1=1";

            if (isCompleted.HasValue)
            {
                if (isCompleted.Value)
                {
                    sql += $" AND {completionCondition}";
                }
            }

            if (wfJobId.HasValue)
            {
                sql += " AND [wfJobDependency].[WfJobId] = @wfJobId";
            }

            if (!string.IsNullOrEmpty(dependencyKey))
            {
                sql += " AND [wfJobDependency].[DependencyKey] = @dependencyKey";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WfJobDependencyId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WfJobDependencyListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);

                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }

                if (!string.IsNullOrEmpty(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }

                result.List = await command.SelectMany<WfJobDependencyListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WfJobDependency] [wfJobDependency]
                INNER JOIN [workflow].[WfJob] [wfJob] ON [wfJob].[WfJobId] = [wfJobDependency].[WfJobId]
                WHERE 1=1";

            if (isCompleted.HasValue)
            {
                if (isCompleted.Value)
                {
                    countSql += $" AND {completionCondition}";
                }
            }

            if (wfJobId.HasValue)
            {
                countSql += " AND [wfJobDependency].[WfJobId] = @wfJobId";
            }

            if (!string.IsNullOrEmpty(dependencyKey))
            {
                countSql += " AND [wfJobDependency].[DependencyKey] = @dependencyKey";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (wfJobId.HasValue)
                {
                    command.AddArgument("wfJobId", wfJobId.Value);
                }

                if (!string.IsNullOrEmpty(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }

                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }

                /// <summary>
        /// Schedule a job dependency check by writing to the schedule-dependency-check queue
        /// </summary>
        /// <param name="wfJob">The workflow job DTO to check dependencies for</param>
        /// <returns>Task</returns>
        internal async Task ScheduleJobDependencyCheckAsync(WfJobListDto wfJob)
        {
            _logger.LogDebug($"Scheduling dependency check for job: {wfJob.WfJobId}");

            try
            {
                // Create WorkflowQueueDto from WfJobListDto
                var queueMessageDto = new WorkflowQueueDto
                {
                    ScheduleId = wfJob.ScheduleId,
                    WorkflowId = wfJob.WorkflowId,
                    WfJobId = wfJob.WfJobId,
                    WfStepId = wfJob.WorkflowStepId,
                    ConditionId = wfJob.WorkflowConditionId,
                    TenantId = wfJob.TenantId
                };

                // Write to 'schedule-dependency-check' queue with populated QueueMessageDto
                await _azureQueue.WriteToQueueAsync("schedule-dependency-check", queueMessageDto);

                _logger.LogInformation($"Successfully sent job {wfJob.WfJobId} to dependency check queue");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scheduling dependency check for job {wfJob.WfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Hard delete a WfJobDependency record
        /// </summary>
        /// <param name="wfJobDependencyId">The job dependency ID to delete</param>
        /// <returns>Number of rows affected</returns>
        internal async Task<int> DeleteJobDependencyAsync(long wfJobDependencyId)
        {
            string sql = @"
                DELETE FROM [workflow].[WfJobDependency]
                WHERE [WfJobDependencyId] = @wfJobDependencyId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("wfJobDependencyId", wfJobDependencyId);
                return await command.ExecuteNonQuery();
            }
        }
    }
}