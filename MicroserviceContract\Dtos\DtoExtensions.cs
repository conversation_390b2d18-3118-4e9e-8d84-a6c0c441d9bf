﻿using System.Text.Json;

namespace MicroserviceContract.Dtos
{
    public static class ObjectExtensions
    {
        /// <summary>
        /// Deepclone an object
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <returns></returns>
        //public static T DeepClone<T>(this T source)
        //{
        //    if (ReferenceEquals(source, null))
        //    {
        //        return default;
        //    }

        //    var deserializeSettings = new JsonSerializerOptions
        //    {
        //        IncludeFields = true,
        //        PropertyNameCaseInsensitive = true
        //    };

        //    return JsonSerializer.Deserialize<T>(JsonSerializer.Serialize(source), deserializeSettings);
        //}
    }
}
