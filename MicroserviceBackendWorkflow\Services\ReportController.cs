using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendWorkflow.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get and Manage Report Definitions.
    /// </summary>
    [Route("api/Report")]
    public class ReportController : AppController
    {
        private readonly Report _report;

        public ReportController(Report report, IUnitOfWork unitOfWork)
        {
            _report = report;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Report
        /// </summary>
        /// <remarks>
        /// Returns a single Report record for a given Report ID.
        /// </remarks>
        /// <param name="reportId">The ID of a report record</param>
        /// <response code="200">Report returned, or null if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(ReportDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(int reportId)
        {
            var result = await _report.GetAsync(reportId);
            return Ok(result);
        }

        /// <summary>
        /// Create a Report
        /// </summary>
        /// <remarks>
        /// Create a new Report definition
        /// </remarks>
        /// <param name="dto">A Report to be created</param>
        /// <response code="200">Report created</response>
        /// <response code="422">Report could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(ReportDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] ReportDto dto)
        {
            await _report.CreateAsync(dto);
            _unitOfWork.Commit();
            var result = await _report.GetAsync(dto.ReportId);
            return Ok(result);
        }

        /// <summary>
        /// Update a Report
        /// </summary>
        /// <remarks>
        /// Update an existing Report definition
        /// </remarks>
        /// <param name="dto">The Report data to update</param>
        /// <response code="200">Report updated</response>
        /// <response code="422">Report could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(ReportDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] ReportDto dto)
        {
            await _report.UpdateAsync(dto);
            _unitOfWork.Commit();
            var result = await _report.GetAsync(dto.ReportId);
            return Ok(result);
        }

        /// <summary>
        /// Delete a Report
        /// </summary>
        /// <remarks>
        /// Delete a Report with the given ID (soft delete).
        /// </remarks>
        /// <param name="reportId">The ID of a report record</param>
        /// <response code="200">Report has been deleted</response>
        /// <response code="404">Report not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] int reportId)
        {
            await _report.DeleteAsync(reportId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Reports
        /// </summary>
        /// <remarks>
        /// Returns a list of Reports with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="reportTypeId">Filter by report type ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <param name="scheduleId">Filter by schedule ID</param>
        /// <response code="200">List of Reports returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<ReportListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? tenantId = null, short? reportTypeId = null, bool? isEnabled = null, int? scheduleId = null)
        {
            var result = await _report.GetListAsync(standardListParameters, tenantId, reportTypeId, isEnabled, scheduleId);
            return Ok(result);
        }
    }
}
