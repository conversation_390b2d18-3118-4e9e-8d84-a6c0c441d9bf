using Azure.Identity;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Extensions
{
    /// <summary>
    /// Extension methods for IServiceCollection to configure Workflow Coordinator services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add Azure Queue Client Factory for Workflow Coordinator
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddWorkflowCoordinatorAzureClients(this IServiceCollection services)
        {
            services.AddAzureClients(clientBuilder =>
            {
                // Add Azure Queue Client for default storage account
                if (!string.IsNullOrEmpty(Config.AzureStorageConnectionString))
                {
                    clientBuilder.AddQueueServiceClient(Config.AzureStorageConnectionString)
                        .WithName("Default");
                }
                else if (!string.IsNullOrEmpty(Config.AzureStorageAccountName))
                {
                    clientBuilder.AddQueueServiceClient(new Uri($"https://{Config.AzureStorageAccountName}.queue.core.windows.net"))
                        .WithName("Default")
                        .WithCredential(new DefaultAzureCredential());
                }

                // Add Azure Queue Client for notification storage account
                if (!string.IsNullOrEmpty(Config.AzureStorageNotificationConnectionString))
                {
                    clientBuilder.AddQueueServiceClient(Config.AzureStorageNotificationConnectionString)
                        .WithName("Notification");
                }
                else if (!string.IsNullOrEmpty(Config.AzureStorageNotificationAccountName))
                {
                    clientBuilder.AddQueueServiceClient(new Uri($"https://{Config.AzureStorageNotificationAccountName}.queue.core.windows.net"))
                        .WithName("Notification")
                        .WithCredential(new DefaultAzureCredential());
                }

                // Use DefaultAzureCredential for authentication when using managed identity
                clientBuilder.UseCredential(new DefaultAzureCredential());
            });

            return services;
        }

        /// <summary>
        /// Add Workflow Coordinator services with Azure clients and logging
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddWorkflowCoordinator(this IServiceCollection services)
        {
            // Add Azure clients
            services.AddWorkflowCoordinatorAzureClients();

            // Add Hybrid Cache if not already added
            services.AddHybridCache();

            // Add logging if not already configured
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
            });

            return services;
        }

        /// <summary>
        /// Add Workflow Coordinator services with custom logger provider
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configureLogging">Action to configure logging</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddWorkflowCoordinator(this IServiceCollection services, Action<ILoggingBuilder> configureLogging)
        {
            // Add Azure clients
            services.AddWorkflowCoordinatorAzureClients();

            // Add Hybrid Cache if not already added
            services.AddHybridCache();

            // Add custom logging configuration
            services.AddLogging(configureLogging);

            return services;
        }
    }
}
