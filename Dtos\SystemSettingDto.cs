﻿using Sql;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// common.SystemSetting table dto
    /// </summary>
    [Mappable(nameof(SystemSettingCode))]
    public class BaseSystemSettingDto
    {
        /// <summary>
        /// Unique System Settings Code used to uniquely identify a setting record
        /// </summary>
        public string? SystemSettingCode { get; set; }
        /// <summary>
        /// The System Setting Value
        /// </summary>
        public string? Value { get; set; }
        /// <summary>
        /// Description of the purpose of the System Setting
        /// </summary>
        public string? Description { get; set; }
    }
}
