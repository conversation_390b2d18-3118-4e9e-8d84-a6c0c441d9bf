using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// When a Schedule will Recur On
    /// </summary>
    [Mappable(nameof(RecurOnId))]
    public class RecurOnDto
    {
        /// <summary>
        /// When a Schedule will Recur On.
        /// Works with RecurOnPositionId to support 3rd Tuesday of Month, 1st Weekday of Month, Last Friday of Month, Last Day of Month.
        /// </summary>
        public byte RecurOnId { get; set; }
        
        /// <summary>
        /// The display label for the recur on option
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this recur on option is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for RecurOn
    /// </summary>
    public class RecurOnListDto : RecurOnDto
    {
    }
}
