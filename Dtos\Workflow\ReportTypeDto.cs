using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// Identifies the type of report.
    /// 1 - Report
    /// 2 - Alert
    /// </summary>
    [Mappable(nameof(ReportTypeId))]
    public class ReportTypeDto
    {
        /// <summary>
        /// The ReportTypeId. 1 - Report, 2 - Alert
        /// </summary>
        public short ReportTypeId { get; set; }
        
        /// <summary>
        /// The display label for the report type
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this report type is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for ReportType
    /// </summary>
    public class ReportTypeListDto : ReportTypeDto
    {
    }
}
