using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// This is a generic workflow job table to track each occurrence of a schedule (ie. when it is to run) or manually run job.
    /// </summary>
    [Mappable(nameof(WfJobId))]
    public class WfJobDto : TenantDtoBase
    {
        /// <summary>
        /// The unique identifier for the workflow job
        /// </summary>
        public long WfJobId { get; set; }
        
        /// <summary>
        /// The name of the workflow job
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
        /// Optional
        /// </summary>
        public int? ScheduleId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// </summary>
        public int? WorkflowId { get; set; }
        
        /// <summary>
        /// The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.
        /// </summary>
        public int? WorkflowConditionId { get; set; }
        
        /// <summary>
        /// The date and time when this job is scheduled to run
        /// </summary>
        public DateTimeOffset? ScheduledAt { get; set; }
        
        /// <summary>
        /// Indicates whether this job is scheduled (true) or manually triggered (false)
        /// </summary>
        public bool IsScheduled { get; set; }
        
        /// <summary>
        /// The current status of the workflow job
        /// </summary>
        public short WfJobStatusId { get; set; }
        
        /// <summary>
        /// Identifies the purpose of the schedule
        /// </summary>
        public short? SchedulePurposeId { get; set; }
        
        /// <summary>
        /// The party that owns this workflow job
        /// </summary>
        public int? OwningPartyId { get; set; }
        
        /// <summary>
        /// The date and time when this job was completed
        /// </summary>
        public DateTimeOffset? CompletedAt { get; set; }
        
        /// <summary>
        /// The next time to check for dependencies
        /// </summary>
        public DateTimeOffset? NextCheckDependenciesAt { get; set; }
        
        /// <summary>
        /// When to timeout waiting for dependencies
        /// </summary>
        public DateTimeOffset? TimeoutDependencyWaitAt { get; set; }
        
        /// <summary>
        /// The current workflow step being executed
        /// </summary>
        public int? WorkflowStepId { get; set; }
        
        /// <summary>
        /// The reason why the job was cancelled (if applicable)
        /// </summary>
        public byte WfJobCancelledReasonId { get; set; }
    }

    /// <summary>
    /// List DTO for WfJob
    /// </summary>
    public class WfJobListDto : WfJobDto
    {
    }
}
