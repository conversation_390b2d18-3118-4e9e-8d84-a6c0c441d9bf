using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for schedule purpose operations
    /// </summary>
    public class SchedulePurpose : BusinessLogicBase
    {
        public SchedulePurpose(IUnitOfWork unitOfWork, ILogger<SchedulePurpose> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single SchedulePurpose by ID
        /// </summary>
        /// <param name="schedulePurposeId">The schedule purpose ID</param>
        /// <returns>SchedulePurposeDto or null if not found</returns>
        public async Task<SchedulePurposeDto?> GetAsync(short schedulePurposeId)
        {
            _logger.LogDebug($"Getting schedule purpose for ID: {schedulePurposeId}");

            try
            {
                // Check cache first
                var cacheKey = $"SchedulePurpose_{schedulePurposeId}";
                var (exists, cachedPurpose) = await _cache.GetCacheItem<SchedulePurposeDto>("SchedulePurpose", cacheKey);
                if (exists && cachedPurpose != null)
                {
                    return cachedPurpose;
                }

                string sql = @"
                    SELECT [SchedulePurposeId]
                          ,[Label]
                          ,[IsEnabled]
                    FROM [workflow].[SchedulePurpose]
                    WHERE [SchedulePurposeId] = @schedulePurposeId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("schedulePurposeId", schedulePurposeId);
                    var schedulePurpose = await command.SelectSingle<SchedulePurposeDto>();

                    // Cache the result if found
                    if (schedulePurpose != null)
                    {
                        await _cache.SetCacheItem("SchedulePurpose", cacheKey, schedulePurpose, cacheForMinutes: 30);
                    }

                    return schedulePurpose;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get schedule purpose for ID: {schedulePurposeId}");
                throw;
            }
        }
    }
}
