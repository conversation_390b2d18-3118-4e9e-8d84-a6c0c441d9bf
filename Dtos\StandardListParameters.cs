﻿using System.Collections.Specialized;

namespace Redi.Prime3.MicroService.BaseLib
{
	public class StandardListParameters
	{
		/// <summary>
		/// Number of records to be returned (max)
		/// </summary>
		public int? Limit { get; set; }

		/// <summary>
		/// Return records from offset count row.
		/// Offset = 100 would return results from the 100th row
		/// </summary>
		public int? Offset { get; set; }

		/// <summary>
		/// List of Sort parameters in the format "column1,column2,-column3"
		/// - prefix indicates descending order
		/// </summary>
		public string? SortBy { get; set; } = "";


		private bool? _isDeleted;
		/// <summary>
		/// Return deleted
		/// </summary>
		public bool? IsDeleted
		{
			get
			{
				if (_isDeleted == null)
				{
					_isDeleted = false;
				}
				return _isDeleted;
			}

			set
			{
				_isDeleted = value;
			}
		}

		public StandardListParameters()
		{
			Limit = 100;
			Offset = 0;
		}

		public StandardListParameters(int limit, int offset = 0)
		{
			Limit = limit;
			Offset = offset;
		}

		/// <summary>
		/// Evaluates the SortBy parameter returning a valid Order By statement for the SQL Database
		/// SortBy and defaultSort can be formated as
		/// COL1,COL2,-COL3
		/// This would equate to:
		/// Order By COL1 asc, COL2 asc, COL3 desc
		/// </summary>
		/// <param name="canBeSortedByDict">Dictionary of valid columns that can be sorted and the sort column names</param>
		/// <param name="defaultSort">optional default sort order</param>
		/// <returns></returns>
		public string EvaluateSortToSqlOrderBy(StringDictionary canBeSortedByDict, string defaultSort)
		{
            string OrderByStatement = " Order By ";
            if (string.IsNullOrEmpty(SortBy))
            {
                SortBy = defaultSort;
            }
            if (string.IsNullOrEmpty(SortBy))
            {
                throw new ApiErrorException($"SortBy parameter is required.");
            }

            var sortCols = SortBy.Split(',').Select(s => s.Trim()).ToList();
            string prefix = "";
            foreach (var col in sortCols)
            {
                if (canBeSortedByDict != null && canBeSortedByDict.ContainsKey(col.Trim('-')))
                {
                    string? colSort = canBeSortedByDict[col.Trim('-')];
                    if (colSort != null && (colSort.EndsWith("ASC") || colSort.EndsWith("DESC")))
                    {
                        OrderByStatement += prefix + colSort;
                    }
                    else
                    {
                        OrderByStatement += prefix + colSort + (col.StartsWith('-') ? " DESC " : " ASC ");
                    }
                    prefix = ",";
                }
                else
                {
                    throw new ApiErrorException($"SortBy column '{SortBy}' is not supported");
                }
            }
            return OrderByStatement;
        }
	}
}