# Redi Prime3 Logger

This logger writes to SQL Database tables **common._log_Error** and **common.Log_Message**

Errors and messages are also pushed to Sentry if the Sentry_DSN is configured.

Use standard settings to configure logging levels (in appsettings, or host.json).
```
"logging": {
  "logLevel": {
    "default": "Information"
  }}
```

For use by Microservices, Azure Functions, and general c# code.

## Common use in code.

logger.LogError("An Error Message"); 

logger.LogError(ex, "Another error message with exception also passed to logger");

logger.LogInformation("Some information message");
