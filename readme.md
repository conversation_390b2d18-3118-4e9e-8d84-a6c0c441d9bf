# Prime3 Azure Function Base

This project is the base Prime3 Azure Function that all developed Azure Functions should be forked or cloned from.

## Supports

Provides standard code for Queue, Blob, Http, and Timer triggered functions.

## How to use this

1. Clone or Fork
1. Update the Constants/FunctionConstants file with AppId and Version 
1. In the functions folder remove the function types that are not required.
1. Duplicate the required function types. You should have 1 class file per function that is in this project
1. In the BusinessLogic folder delete those that are not required, and rename those that are applicable.
1. Update local.settings.json to suit your local testing (do not push this file)
1. Add any required config values to be read from SystemSettings or Key Vault to program.cs systemSettingCodes and keyVaultSettings

# Replace this ReadMe with one that describes your new Function Project.