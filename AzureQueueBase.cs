﻿using Azure.Storage.Queues;
using Newtonsoft.Json;
using System.Text;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Base class for writing to Azure Storage Queue
    /// Create a class extending AzureQueueBase to use the Queue Writing ability
    /// </summary>
    public abstract class AzureQueueBase : BusinessLogicBase
    {
        protected QueueServiceClient? _queueServiceClient;

#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task InsertIntoQueue(object message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null)
        {
            await InsertIntoQueue(JsonConvert.SerializeObject(message), queueName, timeout, delay);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage

        /// <summary>
        /// Write a message to Azure Storage Queue
        /// </summary>
        /// <param name="message"></param>
        /// <param name="queueName"></param>
        /// <param name="timeout"></param>
        /// <param name="delay"></param>
        /// <param name="autoCreateQueue"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task InsertIntoQueue(string message, string queueName, TimeSpan? timeout = null, TimeSpan? delay = null, bool autoCreateQueue = true)
        {
            if (queueName == string.Empty || queueName == null)
            {
                throw new Exception("Queue name was not given");
            }
            if (_queueServiceClient == null)
            {
                throw new Exception("_queueServiceClient is null");
            }

            var queueClient = _queueServiceClient.GetQueueClient(queueName);

            if (!await queueClient.ExistsAsync())
            {
                if (autoCreateQueue)
                {
                    await _queueServiceClient.CreateQueueAsync(queueName);
                }
                else
                {
                    throw new Exception($"Queue of name {queueName} does not exist.");
                }
            }

            var messageBase64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(message));
            await queueClient.SendMessageAsync(messageBase64, timeout, delay);
        }
    }
}

