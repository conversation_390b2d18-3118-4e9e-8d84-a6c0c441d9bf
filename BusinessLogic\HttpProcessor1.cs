using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using Sql;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class HttpProcessor1 : BusinessLogicBase
    {
        public HttpProcessor1(ILogger logger, IUnitOfWork unitOfWork, Cache cache)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _cache = cache;
        }
        public async Task ProcessHttpRequest()
        {
        }
    }
}
