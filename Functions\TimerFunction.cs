using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using RediAzurefunctionBase.BusinessLogic;
using Sql;

namespace RediAzurefunctionBase.Functions
{
    public class TimerFunctionBase : Prime3FunctionTimerBase
    {
        private TimerProcessor1 _timerProcessor1;
        public TimerFunctionBase(IUnitOfWork unitOfWork
            , ILogger logger
            ,TimerProcessor1 timerProcessor1
            ,DefaultAzureQueue defaultAzureQueue
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _timerProcessor1 = timerProcessor1;
            _defaultAzureQueue = defaultAzureQueue;
        }

        [Function(nameof(TimerFunctionBase))]
        public async Task Run([TimerTrigger("0 */5 * * * *")] TimerInfo timeInfo, FunctionContext context)
        {
            // 1) Performs Standard Prime3 Pre Processing of a Timer (log)
            await PreProcess(timeInfo, context);

            // 2) Timer processing
            await _timerProcessor1.ProcessTimer();
            _unitOfWork.Commit();
            _unitOfWork.CloseConnection();

            // 2.1) Insert to queue to trigger subsequent processing.... DELETE IF NOT REQUIRED
            await _defaultAzureQueue.WriteToQueue("outputQueueName", "outputQueueMessageData");

            // 3) Performs standard Prime3 Post Processing of a timer (logging)
            await PostProcess(timeInfo, context);
        }
    }
}

