using RediAzurefunctionBase.Dtos;
using RediAzurefunctionBase.Enums;
using Sql;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class Workflow : BusinessLogicBase
    {
        public Workflow(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Process workflow logic for a job and determine next steps
        /// </summary>
        /// <param name="job">The workflow job to process</param>
        /// <returns>WorkflowProcessingResult containing next steps and status</returns>
        internal async Task<WorkflowProcessingResult> ProcessWorkflowStepsAsync(WfJobDto job)
        {
            var result = new WorkflowProcessingResult();

            // Get the first workflow step
            var firstStep = await GetFirstWorkflowStepAsync(job.WorkflowId.Value);
            
            if (firstStep == null)
            {
                throw new ArgumentException($"No workflow steps found for workflow ID {job.WorkflowId}");
            }

            var stepType = (WorkflowStepTypeEnum)firstStep.WorkflowStepTypeId;

            switch (stepType)
            {
                case WorkflowStepTypeEnum.Branch:
                case WorkflowStepTypeEnum.Condition:
                    // Send to condition evaluator queue
                    result.ShouldWriteToQueue = true;
                    result.QueueName = "wf-condition-evalulator";
                    result.ShouldSetStatus = true;
                    result.NewStatus = WfJobStatusEnum.CheckConditions;
                    break;

                case WorkflowStepTypeEnum.WaitForDependencies:
                    // Set job status to Waiting
                    result.ShouldSetStatus = true;
                    result.NewStatus = WfJobStatusEnum.Waiting;
                    break;

                case WorkflowStepTypeEnum.ExecuteViaQueue:
                case WorkflowStepTypeEnum.NotificationEvent:
                    // Build queue chain until first condition
                    var queueNames = await BuildQueueChainAsync(firstStep.WorkflowStepId);
                    result.ShouldWriteToQueue = true;
                    result.QueueName = firstStep.QueueName;
                    result.NextQueueNames = queueNames;
                    result.ShouldSetStatus = true;
                    result.NewStatus = WfJobStatusEnum.Executing;
                    break;
            }

            return result;
        }

        /// <summary>
        /// Get the first workflow step for a workflow
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <returns>First workflow step or null</returns>
        private async Task<WorkflowStepDto?> GetFirstWorkflowStepAsync(int workflowId)
        {
            string sql = @"
                SELECT TOP 1 [WorkflowStepId], [WorkflowId], [Name], [WorkflowStepTypeId], [QueueName], 
                       [WorkflowConditionId], [SortOrder]
                FROM [workflow].[WorkflowStep]
                WHERE [WorkflowId] = @workflowId
                  AND [Deleted] = 0
                ORDER BY [SortOrder]";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                return await command.SelectSingle<WorkflowStepDto>();
            }
        }

        /// <summary>
        /// Build queue chain from a starting step until first condition is encountered
        /// </summary>
        /// <param name="startingStepId">The starting workflow step ID</param>
        /// <returns>List of queue names in order</returns>
        private async Task<List<string>> BuildQueueChainAsync(int startingStepId)
        {
            var queueNames = new List<string>();
            var currentStepId = startingStepId;

            while (currentStepId > 0)
            {
                // Get next step via WorkflowStepConnector
                var nextStepInfo = await GetNextWorkflowStepAsync(currentStepId);
                
                if (nextStepInfo == null || nextStepInfo.HasCondition)
                {
                    // Stop if no next step or if we hit a condition
                    break;
                }

                // Add queue name if it exists
                if (!string.IsNullOrEmpty(nextStepInfo.QueueName))
                {
                    queueNames.Add(nextStepInfo.QueueName);
                }

                currentStepId = nextStepInfo.WorkflowStepId;
            }

            return queueNames;
        }

        /// <summary>
        /// Get the next workflow step via WorkflowStepConnector
        /// </summary>
        /// <param name="parentStepId">The parent workflow step ID</param>
        /// <returns>Next step information or null</returns>
        private async Task<NextStepInfo?> GetNextWorkflowStepAsync(int parentStepId)
        {
            string sql = @"
                SELECT ws.[WorkflowStepId], ws.[QueueName], 
                       CASE WHEN wsc.[WorkflowConditionId] IS NOT NULL THEN 1 ELSE 0 END AS HasCondition
                FROM [workflow].[WorkflowStepConnector] wsc
                INNER JOIN [workflow].[WorkflowStep] ws ON ws.[WorkflowStepId] = wsc.[ChildWorkflowStepId]
                WHERE wsc.[ParentWorkflowStepId] = @parentStepId
                  AND ws.[Deleted] = 0
                ORDER BY wsc.[SortOrder]";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("parentStepId", parentStepId);
                return await command.SelectSingle<NextStepInfo>();
            }
        }
    }

    /// <summary>
    /// Result of workflow processing
    /// </summary>
    public class WorkflowProcessingResult
    {
        public bool ShouldWriteToQueue { get; set; }
        public string? QueueName { get; set; }
        public List<string>? NextQueueNames { get; set; }
        public bool ShouldSetStatus { get; set; }
        public WfJobStatusEnum? NewStatus { get; set; }
    }

    /// <summary>
    /// Information about the next workflow step
    /// </summary>
    public class NextStepInfo
    {
        public int WorkflowStepId { get; set; }
        public string? QueueName { get; set; }
        public bool HasCondition { get; set; }
    }
}
