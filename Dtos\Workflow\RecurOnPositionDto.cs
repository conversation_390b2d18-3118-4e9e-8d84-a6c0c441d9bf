using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month
    /// </summary>
    [Mappable(nameof(RecurOnPositionId))]
    public class RecurOnPositionDto
    {
        /// <summary>
        /// When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
        /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
        /// Works with RecurOnId
        /// </summary>
        public byte RecurOnPositionId { get; set; }
        
        /// <summary>
        /// The display label for the recur on position
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this recur on position is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for RecurOnPosition
    /// </summary>
    public class RecurOnPositionListDto : RecurOnPositionDto
    {
    }
}
