using Redi.Prime3.MicroService.BaseLib;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public static class Config
    {
        public static string AzureContainer => ConfigBase.GetConfigValue("AzureContainer", defaultTo: "default");
        public static string AzureFilesBase => ConfigBase.GetConfigValue("AzureFilesBase", defaultTo: "https://rediteststorage.blob.core.windows.net/");
    }
}