using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Identifies the behaviour of each Workflow Step.
    /// 1 - Execute Via Queue: A message will be placed into the steps QueueName in order to execute the step. The function associated with the queue must be workflow enabled
    /// 2 - Notification Event: An event will be sent to the Notification Event Queue in order to trigger any required notification processing - email/sms/app, etc
    /// 3 - Condition: Evaluate conditions in order to determine next path to take - first true condition is the path taken
    /// 4 - Wait for Dependencies: Pause processing until one or more named dependencies are met. This could be input from an external system, end user input, etc
    /// 5 - Branch: Branch down multiple paths. If child path has a condition it will only be executed if it evaluates to true. All child paths with no condition will always be executed. Each path will result in a SUB WorkflowJob been created
    /// </summary>
    [Mappable(nameof(WorkflowStepTypeId))]
    public class WorkflowStepTypeDto
    {
        /// <summary>
        /// Defines the type of each step (how each step behaves):
        /// 1 - Execute Via Queue
        /// 2 - Notification Event
        /// 3 - Condition
        /// 4 - Wait for Dependencies
        /// 5 - Branch
        /// </summary>
        public short WorkflowStepTypeId { get; set; }
        
        /// <summary>
        /// The display label for the workflow step type
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this workflow step type is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// The sort order for displaying workflow step types
        /// </summary>
        public short SortOrder { get; set; }
        
        /// <summary>
        /// Detailed description of the workflow step type behavior
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowStepType
    /// </summary>
    public class WorkflowStepTypeListDto : WorkflowStepTypeDto
    {
    }
}
