using Autofac;
using Autofac.Extensions.DependencyInjection;
using RediAzurefunctionBase.Startup;
using Microsoft.Extensions.Hosting;
using RediAzurefunctionBase.Constants;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;

var builder = new HostBuilder();
var appName = FunctionConstants.AppId;
var version = FunctionConstants.Version;

// Confifg items to read from the common.SystemSettings table. These will be made available via ConfigBase
var systemSettingCodes = new string[] { "DefaultTimeZoneIanaId", "DefaultCurrency" };
// Config items to read from the Azure Key Vault. These will be made available via ConfigBase. Note! These are loaded in Final Stage 6.
var keyVaultSettings = new string[] { };

// Function StartUp Processing ***************************************************************************
IHost? app = null;

var startupBase = new Redi.Prime3.Function.BaseLib.StartupBase(args, AppContext.BaseDirectory, appName, version);
var startup = new Startup(ConfigBase.IConfig);
try
{
    // 1) Load System Settings, Check DB Connection String Valid
    await startupBase.StartupStage1(systemSettingCodes);

    // 2) Configure Autofac Container (Register Modules, Classes, etc.)
    builder.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>((context, containerBuilder) =>
    {
        startupBase.ConfigureContainer(containerBuilder); // AutoFac - Register StandardModules, Classes, etc.
        startup.ConfigureContainer(containerBuilder, appName); // AutoFac - Register User Modules, Classes, etc.
    });

    // 3) Configure Services - Cache, Azure (blob, queue, keyvault), etc.
    builder.ConfigureServices((services) =>
    {
        startupBase.ConfigureServices(services, builder); // Configure Standard Services
        startup.ConfigureServices(services, builder); // Configure This Functions Services
    });

    // 4) Configure the application - Middleware, Routing, etc.
    startup.Configure(builder); // Configure Middleware, Routing, etc.
    startupBase.Configure(builder, addHttpRequestAuthentication: false); // Configure Standard Middleware, Logging, Routing, etc.

    // 5) Build the application
    app = builder.Build();

    // 6) Final stage - validate storage connections - ** Set Storage Account Names to empty if you want to bypass storage accounts locally **
    await startupBase.StartupStageFinal(app, keyVaultSettings);
}
catch (Exception ex)
{
    startupBase.StartupLogger.LogError(ex, "Startup Failed");
    Thread.Sleep(1000); // Give the logger time to write to the error log table.
    throw;
}

// Startup complete *********************************************************************************************

// 7) Run the functio host application 
if (app != null) { startupBase.Run(app); }