using Autofac;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Hosting;
using Redi.Prime3.MicroService.Logger;
using Sql.Module;

namespace Redi.Prime3.MicroService.BaseLib
{
    public partial class StartupBase
    {

        public IContainer ApplicationContainer { get; private set; }

        public void ConfigureContainer(ContainerBuilder builder)
        {
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().InstancePerLifetimeScope();
            builder.RegisterType<UtilityFunctions>().InstancePerLifetimeScope();
            // Always register the business logic classes
            builder.RegisterAssemblyTypes(AppAssembliesRoot).Where(
                        t => t.BaseType == typeof(BusinessLogicBase) ||
                        t.BaseType == typeof(AzureQueueBase) ||
                        t.BaseType == typeof(Modules.ModulesBase)
                    ).InstancePerLifetimeScope();

            builder.RegisterModule(new LoggerModule(ConfigBase.IConfig, LoggerProviderRoot, AppId));
            var sqlMod = SqlModule.Create(ConfigBase.DatabaseConnectionString);
            builder.RegisterModule(sqlMod);
            ApplySqlMiddleware(sqlMod.Provider, environmentRoot.IsDevelopment());
            builder.RegisterType<MemoryCache>().As<IMemoryCache>().SingleInstance();
            builder.RegisterType<Cache>().As<Cache>().SingleInstance();
        }

    }
}
