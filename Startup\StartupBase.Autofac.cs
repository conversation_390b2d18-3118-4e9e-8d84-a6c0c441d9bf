using Autofac;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Sql.Module;
using Azure.Core;
using Azure.Identity;
using Redi.Prime3.MicroService.Logger;

namespace Redi.Prime3.Function.BaseLib
{
    public partial class StartupBase
    {
        public IContainer? ApplicationContainer { get; private set; }

        /// <summary>
        /// Configure Dependancy Injection Container (AutoFac) <para/>
        /// - Utilities <para/>
        /// - Cache <para/>
        /// - Logger <para/>
        /// - Sql <para/>
        /// - Http <para/>
        /// - BusinessLogic classes <para/>
        /// </summary>
        /// <param name="builder"></param>
        public void ConfigureContainer(ContainerBuilder builder)
        {
            builder.RegisterType<HttpContextAccessor>().As<IHttpContextAccessor>().InstancePerLifetimeScope();
            builder.RegisterType<UtilityFunctions>().InstancePerLifetimeScope();
            builder.RegisterType<Cache>().InstancePerLifetimeScope();
            // Always register the business logic classes
            builder.RegisterAssemblyTypes(AppAssembliesRoot).Where(
                        t => t.BaseType == typeof(BusinessLogicBase) ||
                        t.BaseType == typeof(AzureQueueBase) ||
                        t.BaseType == typeof(AzureBlobBase) ||
                        t.BaseType == typeof(AzureVaultBase) ||
                        t.BaseType == typeof(Modules.ModulesBase)
                    ).InstancePerLifetimeScope();

            builder.RegisterModule(new LoggerModule(ConfigBase.IConfig, LoggerProviderRoot, AppId));
            var sqlMod = SqlModule.Create(ConfigBase.DatabaseConnectionString);
            builder.RegisterModule(sqlMod);
        }
    }
}
