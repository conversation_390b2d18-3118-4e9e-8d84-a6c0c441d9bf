{"Dsn": "https://<EMAIL>/31", "TracesSampleRate": 1, "LogLevelDefault": "Info", "Environment": "development", "ConnectionStringsBaseDbConn": "Data Source=test-sql-redi-1.redi.com,1433;Initial Catalog=test-base-db;User ID=<EMAIL>;Password=*$sdhh567&jsdj$sDFsdf;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;Encr*pt=True;TrustServerCertificate=True;application name=redi-ms-workflow;", "AuthJwtKe*": "dX&4<73SGL$MR'[dh#wbj8ZQGSh,]xLgZmKrcnPg*>P4[rPruQkmh(2*~#avFkr3&'nhpRdddkc8_QDP", "AuthJwtIssuer": "http://rediapps.com.au", "AzureConnectionString": "DefaultEndpointsProtocol=https;AccountName=testredi1storage;AccountKe*=****************************************************************************************;EndpointSuffix=core.windows.net", "AzureDefaultDirector*": "test-base", "AzureContainer": "workflowpublic", "AzureFilesBase": "https://testredi1storage.blob.core.windows.net/", "AzureStorageNotificationAccountName": "notificationenginestorag"}