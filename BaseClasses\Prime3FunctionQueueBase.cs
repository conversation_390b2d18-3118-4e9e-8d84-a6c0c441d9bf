using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Azure Queue Triggered Functions.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class Prime3FunctionQueueBase : Prime3FunctionBase
	{
        protected string? _queueName;
      
        /// <summary>
        /// Pre Process the Queue Item returning the required type/object<para/>
        /// 1) Log queue message<para/>
        /// 2) Do base64 conversion and return as requested data type.
        /// </summary>
        /// <typeparam name="T">The object or data type to return</typeparam>
        /// <param name="queueItem">The queue item as passed into the function</param>
        /// <param name="context">Function context</param>
        /// <returns>T - object (deserialises json) or value of type T.</returns>
        /// <exception cref="ArgumentNullException">Thrown when queueItem is empty/null</exception>
        public async Task<T> PreProcess<T>(string queueItem, FunctionContext context)
        {
            if (context.BindingContext.BindingData.TryGetValue("QueueTrigger", out var queueTrigger) && queueTrigger != null)
            {
                _queueName = queueTrigger.ToString();
            } 
            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Triggered from Queue: {_queueName}");
            if (string.IsNullOrEmpty(queueItem)) { throw new ArgumentNullException("Queue Item is empty"); }
            if(queueItem.IsBase64String()) // Convert the base 64 string to normal string
            {
                byte[] queueByte = Convert.FromBase64String(queueItem);
                queueItem = System.Text.Encoding.UTF8.GetString(queueByte);
            }

            if (typeof(T) is object)
            {
                var obj = JsonConvert.DeserializeObject<T>(queueItem);
                if (obj == null) {throw new ArgumentNullException("Queue Item is empty");}
                return obj;
            }
            else
            {
                return (T)Convert.ChangeType(queueItem, typeof(T));
            }
        }

        /// <summary>
        /// Do Queue Post processing.<para/>
        /// 1) Log timer stats
        /// </summary>
        /// <param name="context">function context</param>
        public async Task PostProcess(FunctionContext context)
        {
            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Processing Complete for Queue: {_queueName} ");
        }
    }
}
