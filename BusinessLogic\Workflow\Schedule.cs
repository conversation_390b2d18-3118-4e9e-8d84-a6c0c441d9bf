using RediAzurefunctionBase.Dtos;
using Sql;
using System.Collections.Specialized;
using Microsoft.Extensions.Caching.Memory;

namespace RediAzurefunctionBase.BusinessLogic
{
    public class Schedule : BusinessLogicBase
    {
        public Schedule(IUnitOfWork unitOfWork, IMemoryCache memoryCache)
        {
            _unitOfWork = unitOfWork;
            _memoryCache = memoryCache;
        }

        /// <summary>
        /// Get a list of Schedules with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="isEnabled">Filter by enabled status</param>
        /// <param name="parentEntityId">Filter by parent entity ID</param>
        /// <param name="runAt">Optional DateTimeOffset parameter that filters on ScheduleStartsOn, ScheduleEndsOn, ScheduledNextRunAt, FromTime, and ToTime</param>
        /// <returns>List of Schedules</returns>
        internal async Task<ListResponseDto<ScheduleListDto>> GetListAsync(
            StandardListParameters? standardListParameters = null,
            int? tenantId = null,
            int? workflowId = null,
            bool? isEnabled = null,
            Guid? parentEntityId = null,
            DateTimeOffset? runAt = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ScheduleId", "[Schedule].[ScheduleId]" },
                { "Name", "[Schedule].[Name]" },
                { "TenantId", "[Schedule].[TenantId]" },
                { "WorkflowId", "[Schedule].[WorkflowId]" },
                { "ScheduleTypeId", "[Schedule].[ScheduleTypeId]" },
                { "ScheduleModeId", "[Schedule].[ScheduleModeId]" },
                { "IsEnabled", "[Schedule].[IsEnabled]" },
                { "ScheduledNextRunAt", "[Schedule].[ScheduledNextRunAt]" },
                { "CreatedOn", "[Schedule].[CreatedOn]" },
                { "ModifiedOn", "[Schedule].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [ScheduleId], [TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                       [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                       [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                       [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                       [RecurEveryX], [RecurOnDayOfMonth], [RecurOnMonth], [IncludeMonday], [IncludeTuesday], [IncludeWednesday],
                       [IncludeThursday], [IncludeFriday], [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode],
                       [CreatedOn], [CreatedByName], [ModifiedOn], [ModifiedByName], [Deleted]
                FROM [workflow].[Schedule]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            if (workflowId.HasValue)
            {
                sql += " AND [WorkflowId] = @workflowId";
            }

            if (isEnabled.HasValue)
            {
                sql += " AND [IsEnabled] = @isEnabled";
            }

            if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
            {
                sql += " AND [ParentEntityId] = @parentEntityId";
            }

            if (runAt.HasValue)
            {
                sql += @" AND (
                    ([ScheduleStartsOn] IS NULL OR [ScheduleStartsOn] <= @runAt) AND
                    ([ScheduleEndsOn] IS NULL OR [ScheduleEndsOn] >= @runAt) AND
                    ([ScheduledNextRunAt] IS NULL OR [ScheduledNextRunAt] <= @runAt) AND
                    ([FromTime] IS NULL OR [FromTime] <= CAST(@runAt AS TIME)) AND
                    ([ToTime] IS NULL OR [ToTime] >= CAST(@runAt AS TIME))
                )";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ScheduleId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ScheduleListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
                {
                    command.AddArgument("parentEntityId", parentEntityId.Value);
                }
                if (runAt.HasValue)
                {
                    command.AddArgument("runAt", runAt.Value);
                }
                
                result.List = await command.SelectMany<ScheduleListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[Schedule]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }
            if (workflowId.HasValue)
            {
                countSql += " AND [WorkflowId] = @workflowId";
            }
            if (isEnabled.HasValue)
            {
                countSql += " AND [IsEnabled] = @isEnabled";
            }
            if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
            {
                countSql += " AND [ParentEntityId] = @parentEntityId";
            }

            if (runAt.HasValue)
            {
                countSql += @" AND (
                    ([ScheduleStartsOn] IS NULL OR [ScheduleStartsOn] <= @runAt) AND
                    ([ScheduleEndsOn] IS NULL OR [ScheduleEndsOn] >= @runAt) AND
                    ([ScheduledNextRunAt] IS NULL OR [ScheduledNextRunAt] <= @runAt) AND
                    ([FromTime] IS NULL OR [FromTime] <= CAST(@runAt AS TIME)) AND
                    ([ToTime] IS NULL OR [ToTime] >= CAST(@runAt AS TIME))
                )";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (isEnabled.HasValue)
                {
                    command.AddArgument("isEnabled", isEnabled.Value);
                }
                if (parentEntityId.HasValue && parentEntityId != Guid.Empty)
                {
                    command.AddArgument("parentEntityId", parentEntityId.Value);
                }
                if (runAt.HasValue)
                {
                    command.AddArgument("runAt", runAt.Value);
                }
                
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
