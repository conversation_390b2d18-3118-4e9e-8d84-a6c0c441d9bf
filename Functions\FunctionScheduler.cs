using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RediAzurefunctionBase.BusinessLogic;

namespace RediAzurefunctionBase.Functions
{
    /// <summary>
    /// Timer-triggered function that runs the scheduler every 5 minutes
    /// </summary>
    public class FunctionScheduler
    {
        private readonly ILogger<FunctionScheduler> _logger;
        private readonly SchedulerProcessor _schedulerProcessor;

        public FunctionScheduler(ILogger<FunctionScheduler> logger, SchedulerProcessor schedulerProcessor)
        {
            _logger = logger;
            _schedulerProcessor = schedulerProcessor;
        }

        /// <summary>
        /// Timer trigger function that runs every 5 minutes
        /// </summary>
        /// <param name="myTimer">Timer information</param>
        /// <returns>Task</returns>
        [Function("FunctionScheduler")]
        public async Task Run([TimerTrigger("0 */5 * * * *")] TimerInfo myTimer)
        {
            _logger.LogInformation($"Scheduler function executed at: {DateTime.Now}");
            
            try
            {
                await _schedulerProcessor.ExecuteAsync();
                
                _logger.LogInformation($"Scheduler function completed successfully at: {DateTime.Now}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scheduler function execution");
                throw;
            }
        }
    }
}
