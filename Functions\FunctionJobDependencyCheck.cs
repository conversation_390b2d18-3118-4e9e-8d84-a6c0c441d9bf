using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using RediAzurefunctionBase.BusinessLogic;

namespace RediAzurefunctionBase.Functions
{
    /// <summary>
    /// Queue-triggered function that processes schedule-dependency-check messages
    /// </summary>
    public class FunctionJobDependencyCheck
    {
        private readonly ILogger<FunctionJobDependencyCheck> _logger;
        private readonly JobDependencyProcessor _jobDependencyProcessor;

        public FunctionJobDependencyCheck(ILogger<FunctionJobDependencyCheck> logger, JobDependencyProcessor jobDependencyProcessor)
        {
            _logger = logger;
            _jobDependencyProcessor = jobDependencyProcessor;
        }

        /// <summary>
        /// Queue trigger function for schedule-dependency-check queue
        /// </summary>
        /// <param name="queueMessage">The queue message containing workflow queue DTO</param>
        /// <returns>Task</returns>
        [Function("FunctionJobDependencyCheck")]
        public async Task Run([QueueTrigger("schedule-dependency-check")] string queueMessage)
        {
            _logger.LogInformation($"Dependency check function triggered");
            
            try
            {
                // Deserialize the queue message to WorkflowQueueDto
                var workflowQueueDto = JsonConvert.DeserializeObject<WorkflowQueueDto>(queueMessage);
                
                if (workflowQueueDto?.WfJobId == null)
                {
                    _logger.LogWarning("Invalid queue message: WfJobId is null");
                    return;
                }

                _logger.LogInformation($"Processing dependency check for job: {workflowQueueDto.WfJobId}");

                // Check if job has pending dependencies
                bool hasPendingDependencies = await _jobDependencyProcessor.CheckPendingDependenciesExistAsync(workflowQueueDto.WfJobId.Value);

                if (!hasPendingDependencies)
                {
                    // No pending dependencies, handle job schedule transition
                    await _jobDependencyProcessor.HandleJobScheduleTransitionAsync(workflowQueueDto);
                    _logger.LogInformation($"Job {workflowQueueDto.WfJobId} has no pending dependencies, processed schedule transition");
                }
                else
                {
                    _logger.LogInformation($"Job {workflowQueueDto.WfJobId} still has pending dependencies, no action taken");
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, $"Error deserializing queue message: {queueMessage}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing dependency check");
                throw;
            }
        }
    }
}
