version: '3.4'

networks:
  web:
    name: web
    external: true

services:
  listapiservice:
    image: jadredi/redi_microservices:prod-0.1.42
    container_name: listapiservice
    networks:
      - web
    labels:
      - "traefik.enable=true"
      #- "traefik.http.routers.listapi.entrypoints=web"
      #- "traefik.http.routers.listapi.rule=Host(`base.redi3.dev`) && PathPrefix(`/listapi`)"
      #- "traefik.http.middlewares.listapiredirecthttps.redirectscheme.scheme=https"
      #- "traefik.http.routers.listapi.middlewares=listapiredirecthttps"
      - "traefik.http.routers.listapisecure.entrypoints=websecure"
      - "traefik.http.routers.listapisecure.rule=Host(`base.redi3.dev`) && PathPrefix(`/listapi`)"
      - "traefik.http.routers.listapisecure.tls.certresolver=cloudflare"
      - "traefik.http.routers.listapisecure.tls=true"
      #- "traefik.http.routers.listapi.middlewares=listapi-strip"
      #- "traefik.http.middlewares.listapi-strip.stripprefix.prefixes=/listapi"
      - "traefik.http.middlewares.listapisecure-strip.stripprefix.prefixes=/listapi"
      - "traefik.http.middlewares.listapisecure-strip.stripprefix.forceSlash=false"
      - "traefik.http.routers.listapisecure.middlewares=listapisecure-strip"
      - "traefik.http.routers.listapisecure.service=listapiservice-redi-microservice-workflow"
      - "traefik.docker.network=web"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - SECRETS_STORE_NAME=secretstoredev
      - DAPR_HTTP_PORT=3500
      - DAPR_GRPC_PORT=50001
      - DAPR_APP_PORT=80
      - HOSTNAME=redi-microservice-workflow.docker.localhost:8443
    restart: unless-stopped

  listapiservice-dapr:
   image: "daprio/daprd:latest"
   command: [ "./daprd",
    "-app-id", "listapiservice",
    "-app-port", "8080",
    "-dapr-http-port", "3500",
    "-dapr-grpc-port", "50001",
    "-components-path", "/components",
    "-config", "/configuration/setup-config.yaml",
   ]
   volumes:
      - "./dapr/components/:/components"
      - "./dapr/configuration/:/configuration"
   depends_on:
    - listapiservice
   network_mode: "service:listapiservice"
