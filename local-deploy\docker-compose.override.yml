version: '3.4'

services:
  redi-microservice-workflow:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - SECRETS_STORE_NAME=secretstore
      - DAPR_HTTP_PORT=3500
      - DAPR_GRPC_PORT=50001
      - DAPR_APP_PORT=8080
      - HOSTNAME=redi-microservice-workflow.docker.localhost:8443
      - SENTRY_TRACES_SAMPLE_RATE=1.0
      - SENTRY_DSN=https://<EMAIL>/31
      - SENTRY_LOG_LEVEL_DEFAULT=debug
      - SENTRY_ENABLE_TRACING=true
      - SENTRY_ENABLE_DEBUG=true

  #microservicebackgroundprocess:
   # environment:
    #  - ASPNETCORE_ENVIRONMENT=Development

