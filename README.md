﻿# Redi Prime3 MicroService BaseLib

This is a Nuget package to be used by Prime3 Micro Services, it contains common code that is used across all Micro Services.

* Common Startup code (Autofac, Services, Auth, Config, Sql Middleware, Error Handler)
* Check Common schema exists before allowing startup to proceed
* Run Startup SQL from the Database folder - All files startig with S in that folder
* Standard Config value - use ConfigBase class
* ApiErrorException
* Event Writer - writes to common.Event
* Notifications Writer - sends notification events to notification event queue
* Utility functions
* Extensions
* Base Dto's
* Azure Queue Writer
* Google Captcha Verification
* Sentry - includes support for logging errors/messages to Sentry


## Making Changes 

Changes are made as normal.
Each commit will result in a bitbucket pipeline executing to build the library
This will be pushed to MyGet to support use via Nuget
Each pipieline build will automatically increment the last number on the package version.
eg. v1.0.0-46 (46th build)
Increment the VersionPrefix for each released change. Edit the Project file directly to change the VersionPrefix.


## Adding to a new Micro Service

See Redi MicroService Common program.cs for an example of how the startup should be implemented in a new project


## Exception Handling in Micro Services

___ApiErrorException___ - use this Exception type for all validation type errors. It will never return a stack trace. It logs to the Information log

___StartupErrorException___ - use this exception for errors during startup
