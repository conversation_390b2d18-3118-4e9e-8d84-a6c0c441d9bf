# Redi Prime3 Workflow Coordinator Library

A .NET 8 class library for managing workflow coordination, scheduling, and dependency validation with Azure Queue integration and comprehensive logging.

## Features

- **Workflow Management**: Create, schedule, and execute workflows with multiple steps
- **Dependency Validation**: Track and validate workflow dependencies
- **Azure Queue Integration**: Send messages to Azure Storage Queues for workflow execution
- **Notification System**: Send notifications through the Prime3 notification system
- **Caching**: Hybrid caching with object-based invalidation
- **Logging**: Integrated with Redi.Prime3.Logger for comprehensive logging
- **Autofac Integration**: Automatic dependency injection setup

## Installation

```xml
<PackageReference Include="Redi.Prime3.WorkflowCoordinator" Version="1.0.0" />
```

## Configuration

### 1. Add Services in Program.cs / Startup.cs

```csharp
using Redi.Prime3.WorkflowCoordinator.Lib.Extensions;

// Add Workflow Coordinator services
services.AddWorkflowCoordinator();

// Or with custom logging configuration
services.AddWorkflowCoordinator(logging =>
{
    logging.AddConsole();
    logging.AddApplicationInsights();
    // Add other logging providers
});
```

### 2. Register Autofac Module

```csharp
using Redi.Prime3.WorkflowCoordinator.Lib.Module;

// In your Autofac container builder
var builder = new ContainerBuilder();

// Register the Workflow Coordinator module
builder.RegisterModule(new WorkflowCoordinatorModule(
    configuration, 
    loggerProvider, 
    "YourAppId"
));
```

### 3. Configuration Settings

Add these settings to your `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "your-database-connection-string"
  },
  "AzureStorage": {
    "AccountName": "your-storage-account-name",
    "ConnectionString": "your-storage-connection-string",
    "NotificationAccountName": "your-notification-storage-account",
    "NotificationConnectionString": "your-notification-storage-connection-string"
  }
}
```

## Usage Examples

### 1. Creating and Running a Workflow

```csharp
public class WorkflowController : ControllerBase
{
    private readonly WorkflowJob _workflowJob;
    private readonly WorkflowSchedule _workflowSchedule;

    public WorkflowController(WorkflowJob workflowJob, WorkflowSchedule workflowSchedule)
    {
        _workflowJob = workflowJob;
        _workflowSchedule = workflowSchedule;
    }

    [HttpPost("run-workflow/{workflowId}")]
    public async Task<IActionResult> RunWorkflow(int workflowId)
    {
        try
        {
            var jobId = await _workflowJob.RunUnscheduledWfJobAsync(
                workflowId, 
                name: "Manual Workflow Execution",
                tenantId: 1
            );
            
            return Ok(new { JobId = jobId });
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpPost("schedule-workflow")]
    public async Task<IActionResult> ScheduleWorkflow(int workflowId, DateTime scheduledAt)
    {
        try
        {
            var jobId = await _workflowSchedule.CreateNewScheduleAsync(
                workflowId,
                scheduledAt,
                name: "Scheduled Workflow",
                tenantId: 1
            );
            
            return Ok(new { JobId = jobId });
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
```

### 2. Dependency Management

```csharp
public class DependencyController : ControllerBase
{
    private readonly WorkflowDependencyValidation _dependencyValidation;

    public DependencyController(WorkflowDependencyValidation dependencyValidation)
    {
        _dependencyValidation = dependencyValidation;
    }

    [HttpPost("publish-dependency")]
    public async Task<IActionResult> PublishDependency(string dependencyKey)
    {
        try
        {
            await _dependencyValidation.PublishDependencyMetAsync(dependencyKey);
            return Ok(new { Message = "Dependency published to queue successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
```

### 3. Job Status Management with Enums

```csharp
public class WorkflowManagementController : ControllerBase
{
    private readonly WorkflowJob _workflowJob;
    private readonly JobStatus _jobStatus;

    public WorkflowManagementController(WorkflowJob workflowJob, JobStatus jobStatus)
    {
        _workflowJob = workflowJob;
        _jobStatus = jobStatus;
    }

    [HttpPost("cancel-job/{jobId}")]
    public async Task<IActionResult> CancelJob(long jobId, WfJobCancelledReasonEnum reason)
    {
        try
        {
            await _workflowJob.CancelWfJobAsync(jobId, reason);
            return Ok(new { Message = "Job cancelled successfully" });
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }

    [HttpGet("job-status/{statusId}")]
    public async Task<IActionResult> GetJobStatus(short statusId)
    {
        try
        {
            var status = await _jobStatus.GetAsync(statusId);
            if (status == null)
            {
                return NotFound();
            }
            return Ok(status);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.Message);
        }
    }
}
```

### 4. Azure Queue Operations

```csharp
public class QueueService
{
    private readonly DefaultAzureQueue _azureQueue;
    private readonly Notifications _notifications;

    public QueueService(DefaultAzureQueue azureQueue, Notifications notifications)
    {
        _azureQueue = azureQueue;
        _notifications = notifications;
    }

    public async Task SendWorkflowMessage(long jobId, int stepId, object data)
    {
        await _azureQueue.WriteWorkflowJobMessage(
            "workflow-processing", 
            jobId, 
            stepId, 
            data
        );
    }

    public async Task SendNotification(string email, string subject, string message)
    {
        await _notifications.SendNotificationEvent(
            "WORKFLOW_NOTIFICATION",
            email,
            subject,
            message
        );
    }
}
```

### 5. Using Logging

The library automatically integrates with Redi.Prime3.Logger. All business logic classes inherit from `BusinessLogicBase` which provides access to the logger:

```csharp
public class CustomWorkflowLogic : BusinessLogicBase
{
    public CustomWorkflowLogic(IUnitOfWork unitOfWork, ILogger<CustomWorkflowLogic> logger, 
                              UtilityFunctions utilityFunctions, Cache cache)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _utilityFunctions = utilityFunctions;
        _cache = cache;
    }

    public async Task ProcessWorkflowStep(long jobId)
    {
        _logger.LogInformation($"Starting workflow step processing for job {jobId}");
        
        try
        {
            // Your workflow logic here
            _logger.LogDebug("Workflow step completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error processing workflow step for job {jobId}");
            throw;
        }
    }
}
```

## Architecture

### Namespace Structure
- `Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic` - Core business logic classes
- `Redi.Prime3.WorkflowCoordinator.Lib.Dtos` - Data transfer objects
- `Redi.Prime3.WorkflowCoordinator.Lib.Azure` - Azure integration classes
- `Redi.Prime3.WorkflowCoordinator.Lib.Utilities` - Utility functions and extensions
- `Redi.Prime3.WorkflowCoordinator.Lib.Module` - Autofac module registration
- `Redi.Prime3.WorkflowCoordinator.Lib.Enums` - Enum definitions for job statuses and cancellation reasons

### Enums and Validation

The library includes strongly-typed enums for workflow job management:

#### JobStatusEnum
- **Scheduled** (1) - Job is scheduled to run
- **Delayed** (2) - Job execution has been delayed
- **CheckConditions** (3) - Job is checking conditions before execution
- **Executing** (4) - Job is currently executing
- **Waiting** (5) - Job is waiting for dependencies or other conditions
- **Completed** (6) - Job has completed successfully
- **Cancelled** (7) - Job has been cancelled
- **Error** (8) - Job encountered an error

#### WfJobCancelledReasonEnum
- **NotCancelled** (1) - Job is not cancelled (default value)
- **FailedTrigger** (2) - Job was cancelled due to failed trigger
- **DependenciesNeverMet** (3) - Job was cancelled because dependencies were never met
- **ManualCancel** (4) - Job was manually cancelled by a user

All status updates are validated against the database lookup tables to ensure data integrity.

### Key Components
- **WorkflowSchedule**: Manages workflow scheduling and step progression with validated status IDs
- **WorkflowJob**: Handles workflow job execution and management with enum-based status validation
- **WorkflowDependencyValidation**: Publishes dependency completion to Azure queue 'dependency-met'
- **JobStatus**: Lookup table service for workflow job statuses with validation
- **WfJobCancelledReason**: Lookup table service for job cancellation reasons with validation
- **DefaultAzureQueue**: Azure Storage Queue operations
- **Notifications**: Notification system integration
- **UtilityFunctions**: Common utility functions for JWT claims, etc.
- **Cache**: Hybrid caching with object-based invalidation

## Dependencies

- .NET 8.0
- Autofac 8.0.0
- Azure.Storage.Queues 12.19.1
- Microsoft.Extensions.Azure 1.7.5
- Redi.Sql 8.0.0
- Redi.Prime3.Logger 1.0.0
- Microsoft.Extensions.Caching.Hybrid 9.0.0

## License

This library is part of the Redi Prime3 ecosystem.
