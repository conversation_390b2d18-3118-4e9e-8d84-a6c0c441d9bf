﻿# Redi Prime3 Azure Function BaseLib

This is a Nuget package to be used by Prime3 Azure Functions, it contains common code that is used across all Functions.
Supports function types: **Http**, **Blob**, **Queue**, and **Timer**.

* Common Startup code (Autofac, Services, Config, Error Handler)
* Check Common schema exists before allowing startup to proceed
* Standard Config value - use **ConfigBase** class
* ApiErrorException
* Event Writer - writes to common.Event
* Notifications Writer - sends notification events to notification event queue
* Utility functions
* Extensions
* Base Dto's
* Azure Queue Writer
* Azure Blob Writer
* Azure Key Vault Reader/Writer
* Pre and Post processing for Timer, Queue, Http, and Blob Triggered functions.
* Validate access to Azure Storage (for Queue/Blob)

## Base Classes

Base classes are available that Functions should inherit from.

* Prime3FunctionBlobBase - Blob triggered functions should inherit this
* Prime3FunctionHttpBase - Http triggered functions should inherit this
* Prime3FunctionQueueBase - Queue triggered functions should inherit this
* Prime3FunctionTimerBase - Timer triggered functions should inherit this

BusinessLogicBase is also available for the business logic classes to inherit from.

## Writing to Queues

If the standard azure storage account is been used (this should normally be the case) then the DefaultAzureQueue class can be used.
await _defaultAzureQueue.WriteToQueue("outputQueueName", "outputQueueMessageData");

## Writing to a Blob

If the standard azure storage account is been used (this should normally be the case) then the DefaultAzureBlob class can be used.
await _defaultAzureBlob.WriteBlobString("Test Blob Contents", testBlobName, isPublic: true, containerName: "MyAzureContainer");

## Access to Config Values

All config values can be accessed via the ConfigBase static class.
This pulls config in from: host.json, local.settings.jcon (local dev only), azure settings, common.SystemSettings, Azure Key Vault.
To pull common.SystemSettings into the available ConfigBase values set **systemSettingCodes** in Program.cs
To pull Azure Key Vault Secrets into the available ConfigBase values set **keyVaultSettings** in Program.cs

## Making Changes 

Changes are made as normal.
Each commit will result in a bitbucket pipeline executing to build the library
This will be pushed to MyGet to support use via Nuget
Each pipieline build will automatically increment the last number on the package version.
eg. v1.0.0-46 (46th build)
Increment the VersionPrefix for each released change. Edit the Project file directly to change the VersionPrefix.


## Adding to a new Function

See Redi Azure Function Base.cs for an example of how the startup should be implemented in a new project


## Exception Handling in Functions

___ApiErrorException___ - use this Exception type for all validation type errors. It will never return a stack trace. It logs to the Information log

___StartupErrorException___ - use this exception for errors during startup
