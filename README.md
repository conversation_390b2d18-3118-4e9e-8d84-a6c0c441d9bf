﻿# Workflow Micro Service

___

## Installing and Using this Micro Service Project

Following Microservice Software Architecture patterns, Microservices are loosely coupled autonomous services that collaborate with other services in a system. The idea that

services can be tested, built and deployed independently of other services that makeup the entirety of a system. Platform and language are not important in this regard, as services

talk to each other through the use of DAPR (Distributed Application Runtime) and deployed through Docker, the container of choice for Microservices. DAPR for its microservice connectivity

and vast integration of building blocks (Redis, Azure, SQL server), and Dockers availability to run on most modern systems.

As such, microservices should only handle data for its schema e.g. A Customer microservice handles data for only customer related tables.

### PREREQUISITES

1. Install DAPR CLI for Windows (https://docs.dapr.io/getting-started/install-dapr-cli/)

a. Open Command Prompt in Admin Mode and copy below line

powershell -Command "iwr -useb https://raw.githubusercontent.com/dapr/cli/master/install/install.ps1 | iex"

2. Install Docker (https://docs.docker.com/get-docker/)

### COMMANDS (CLI)

2. Init Dapr environment 

dapr init

3. Run Microservice (Without Docker) (https://docs.dapr.io/getting-started/get-started-api/)

dapr run --app-id redi-microservice-workflow --components-path ./components --config ./configuration/setup-config.yaml --app-port 3500 -- dotnet run

4. Run Microservice (With Docker) (https://docs.dapr.io/getting-started/get-started-api/)

If you are using Visual Studio. Right-click on the docker-compose file and set as 'Set as Start-up project', and press the 'Docker Compose' button at the top.

If you have Docker Desktop installed (which you should have), you can view the Containers running for each Service.

Notice that all the components in the docker-compose.yml file have their own container when running.

5. To connect to a local DB. 

Create a Security User Login. Then in your connection string point to the DB and port SQL server is running on. Something similar to below:

Data Source=host.docker.internal,1436;Initial Catalog=redi-base-db;User=admin;Password=**********;Min Pool Size=5;Max Pool Size=100;Pooling=True;multipleactiveresultsets=True;application name=redi-base-db;

6. Zipkin

You can visit Zipkin traces at http://zipkin:9411

7. Test, call 
	HTTP: http://redi-microservice-workflow.docker.localhost:8084/api/Test/Get to fetch the information within the Secret store. Visit the above Zipkin link to view traces.
	HTTPS: http://redi-microservice-workflow.docker.localhost:8443/api/Test/Get to fetch the information within the Secret store. Visit the above Zipkin link to view traces.

8. Visit Traefik (traefik/middlewares) at http://localhost:8084