using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// This is a generic Report table. 
    /// This can be used for scheduled reports or on-demand reports, alerts, etc. It is responsible for determining what is reported.
    /// The workflow.Schedule table defines when a report runs, Notification Event/Rules defines any delivery requirements (email, sms) for the report.
    /// </summary>
    [Mappable(nameof(ReportId))]
    public class ReportDto : TenantDtoBase
    {
        /// <summary>
        /// The unique identifier for the report
        /// </summary>
        public int ReportId { get; set; }
        
        /// <summary>
        /// The title of the report
        /// </summary>
        public string Title { get; set; } = string.Empty;
        
        /// <summary>
        /// The ReportTypeId. 1 - Report, 2 - Alert.
        /// Defaults to 1
        /// </summary>
        public short ReportTypeId { get; set; }
        
        /// <summary>
        /// Indicates whether this report is enabled and can be executed
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// Identifies a pre-defined schedule for reporting.
        /// The schedule may also just indicate the report only runs on-demand
        /// </summary>
        public int? ScheduleId { get; set; }
    }

    /// <summary>
    /// List DTO for Report
    /// </summary>
    public class ReportListDto : ReportDto
    {
    }
}
