﻿using Sql;

namespace Redi.Prime3.MicroService.BaseLib
{
    [Mappable(nameof(MessageChannelId))]
    public class MessageChannelDto
    {
        public int MessageChannelId { get; set; }
        public string? Name { get; set; }
        public DateTimeOffset? LastMessageAt { get; set; }
        public int MessageChannelTypeId { get; set; }
        public Guid? ChannelGlobalId { get; set; }
        public Guid? ParentEntityId { get; set; }
        public int? ParentEntityIntId { get; set; }
        public string? ParentEntityType { get; set; }
        public string? ParentEntityName { get; set; }
        public bool IsDirectMessage { get; set; }
        public bool IsPublic { get; set; }
        public int? TenantId { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
        public DateTimeOffset? ModifiedOn { get; set; }
        public string? ModifiedByName { get; set; }
        public bool Deleted { get ; set; }
    }
}
