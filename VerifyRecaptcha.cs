using Newtonsoft.Json;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Verify Google Recaptcha Token
    /// </summary>
    public class VerifyRecaptcha : BusinessLogicBase
    {
        private readonly HttpClient _httpClient;

        public VerifyRecaptcha(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// Verify Google Recaptcha token
        /// </summary>
        /// <param name="clientToken">To<PERSON> passed in from the browser</param>
        /// <returns>true - token is valid</returns>
        /// <exception cref="Exception"></exception>
        public async Task<bool> VerifyRecaptchaTokenAsync(string? clientToken)
        {
            var recaptchaSecret = ConfigBase.GoogleRecaptchaSecretKey;
            if (string.IsNullOrEmpty(clientToken) && string.IsNullOrEmpty(recaptchaSecret)) { return true; }
            if (string.IsNullOrEmpty(recaptchaSecret)) { throw new Exception("GoogleRecaptchaSecretKey config key is missing"); }
            var response = await _httpClient.PostAsync($"https://www.google.com/recaptcha/api/siteverify?secret={recaptchaSecret}&response={clientToken}", null);
            response.EnsureSuccessStatusCode();
            var stringResponse = await response.Content.ReadAsStringAsync();
            var recaptchaResponse = JsonConvert.DeserializeObject<RecaptchaResponse>(stringResponse);
            return recaptchaResponse?.Success ?? false;
        }
    }
}
