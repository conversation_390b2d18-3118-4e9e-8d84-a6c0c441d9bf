using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
    /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
    /// </summary>
    [Mappable(nameof(RecurOnPositionId))]
    public class RecurOnPositionDto
    {
        /// <summary>
        /// When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
        /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
        /// </summary>
        public byte RecurOnPositionId { get; set; }
        
        /// <summary>
        /// The display label for the recurrence position (e.g., "First", "Second", "Last")
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// The sort order for displaying recurrence positions
        /// </summary>
        public byte? SortOrder { get; set; }
        
        /// <summary>
        /// Indicates whether this recurrence position is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for RecurOnPosition
    /// </summary>
    public class RecurOnPositionListDto : RecurOnPositionDto
    {
    }
}
