using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;
        private readonly ScheduleType _scheduleType;
        private readonly ScheduleMode _scheduleMode;
        private readonly SchedulePurpose _schedulePurpose;
        private readonly RecurrenceFrequency _recurrenceFrequency;
        private readonly RecurOn _recurOn;
        private readonly RecurOnPosition _recurOnPosition;

        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache,
            JobStatus jobStatus, ScheduleType scheduleType, ScheduleMode scheduleMode, SchedulePurpose schedulePurpose,
            RecurrenceFrequency recurrenceFrequency, RecurOn recurOn, RecurOnPosition recurOnPosition)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
            _scheduleType = scheduleType;
            _scheduleMode = scheduleMode;
            _schedulePurpose = schedulePurpose;
            _recurrenceFrequency = recurrenceFrequency;
            _recurOn = recurOn;
            _recurOnPosition = recurOnPosition;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync()
        {
            // Empty method
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="scheduleDto">The schedule data to create</param>
        /// <returns>The created schedule ID</returns>
        public async Task<int> CreateNewScheduleAsync(ScheduleDto scheduleDto)
        {
            _logger.LogInformation($"Creating new schedule for workflow {scheduleDto.WorkflowId}");

            try
            {
                // Validate that the workflow exists
                if (scheduleDto.WorkflowId.HasValue)
                {
                    await ValidateWorkflowAsync(scheduleDto.WorkflowId.Value);
                }

                // Validate ScheduleTypeId
                if (scheduleDto.ScheduleTypeId > 0)
                {
                    await _scheduleType.GetAsync(scheduleDto.ScheduleTypeId, false);
                }
                else
                {
                    throw new ArgumentException("ScheduleTypeId must be greater than 0");
                }

                // Validate ScheduleModeId
                if (scheduleDto.ScheduleModeId > 0)
                {
                    await _scheduleMode.GetAsync(scheduleDto.ScheduleModeId, false);
                }
                else
                {
                    throw new ArgumentException("ScheduleModeId must be greater than 0");
                }

                // Validate SchedulePurposeId if provided
                if (scheduleDto.SchedulePurposeId.HasValue && scheduleDto.SchedulePurposeId > 0)
                {
                    await _schedulePurpose.GetAsync(scheduleDto.SchedulePurposeId.Value, false);
                }

                // Validate RecurrenceFrequencyId if provided
                if (scheduleDto.RecurrenceFrequencyId.HasValue && scheduleDto.RecurrenceFrequencyId > 0)
                {
                    await _recurrenceFrequency.GetAsync(scheduleDto.RecurrenceFrequencyId.Value, false);
                }

                // Validate RecurOnId if provided
                if (scheduleDto.RecurOnId.HasValue && scheduleDto.RecurOnId > 0)
                {
                    await _recurOn.GetAsync(scheduleDto.RecurOnId.Value, false);
                }

                // Validate RecurOnPositionId if provided
                if (scheduleDto.RecurOnPositionId.HasValue && scheduleDto.RecurOnPositionId > 0)
                {
                    await _recurOnPosition.GetAsync(scheduleDto.RecurOnPositionId.Value, false);
                }

                // Set audit fields
                scheduleDto.CreatedOn = DateTimeOffset.UtcNow;
                scheduleDto.CreatedByName = _utilityFunctions.UserFullName;

                // Insert into workflow.Schedule table
                string sql = @"
                    INSERT INTO [workflow].[Schedule]
                    ([TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                     [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                     [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                     [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                     [RecurEveryX], [IncludeMonday], [IncludeTuesday], [IncludeWednesday], [IncludeThursday], [IncludeFriday],
                     [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode], [CreatedOn], [CreatedByName])
                    OUTPUT INSERTED.ScheduleId
                    VALUES
                    (@TenantId, @Name, @WorkflowId, @ScheduleTypeId, @WorkflowConditionId, @ScheduleModeId,
                     @SchedulePurposeId, @SimpleChainedQueueNames, @ParentEntityId, @ParentEntityIntId, @ParentEntityType,
                     @ScheduledNextRunAt, @IsEnabled, @Note, @ScheduleStartsOn, @ScheduleEndsOn, @TimezoneIanaId,
                     @FromTime, @ToTime, @ScheduledWhenDescription, @RecurrenceFrequencyId, @RecurOnId, @RecurOnPositionId,
                     @RecurEveryX, @IncludeMonday, @IncludeTuesday, @IncludeWednesday, @IncludeThursday, @IncludeFriday,
                     @IncludeSaturday, @IncludeSunday, @NotificationEventTypeCode, @CreatedOn, @CreatedByName)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(scheduleDto);
                    var scheduleId = await command.SelectSingle<int>();

                    _logger.LogInformation($"Successfully created new schedule with ID {scheduleId} for workflow {scheduleDto.WorkflowId}");
                    return scheduleId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {scheduleDto.WorkflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }
            }
        }





        #endregion
    }
}
