using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;
        private readonly ScheduleType _scheduleType;
        private readonly ScheduleMode _scheduleMode;
        private readonly SchedulePurpose _schedulePurpose;
        private readonly RecurrenceFrequency _recurrenceFrequency;
        private readonly RecurOn _recurOn;
        private readonly RecurOnPosition _recurOnPosition;
        private readonly WorkflowStepConnector _workflowStepConnector;
        private readonly WorkflowJob _workflowJob;
        private readonly DefaultAzureQueue _azureQueue;

        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache,
            JobStatus jobStatus, ScheduleType scheduleType, ScheduleMode scheduleMode, SchedulePurpose schedulePurpose,
            RecurrenceFrequency recurrenceFrequency, RecurOn recurOn, RecurOnPosition recurOnPosition,
            WorkflowStepConnector workflowStepConnector, WorkflowJob workflowJob, DefaultAzureQueue azureQueue)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
            _scheduleType = scheduleType;
            _scheduleMode = scheduleMode;
            _schedulePurpose = schedulePurpose;
            _recurrenceFrequency = recurrenceFrequency;
            _recurOn = recurOn;
            _recurOnPosition = recurOnPosition;
            _workflowStepConnector = workflowStepConnector;
            _workflowJob = workflowJob;
            _azureQueue = azureQueue;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <param name="queueMessageDto">The queue message containing workflow context</param>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync(WorkflowQueueDto queueMessageDto)
        {
            _logger.LogInformation($"Processing step completion for WorkflowId: {queueMessageDto.WorkflowId}, WfJobId: {queueMessageDto.WfJobId}, WfStepId: {queueMessageDto.WfStepId}");

            try
            {
                // Check if one of WorkflowId, WfJobId, or WfStepId is supplied but others don't exist
                var hasWorkflowId = queueMessageDto.WorkflowId.HasValue;
                var hasWfJobId = queueMessageDto.WfJobId.HasValue;
                var hasWfStepId = queueMessageDto.WfStepId.HasValue;

                // If any one is provided, all three must be provided
                if (hasWorkflowId || hasWfJobId || hasWfStepId)
                {
                    if (!hasWorkflowId || !hasWfJobId || !hasWfStepId)
                    {
                        throw new ArgumentException("If one of WorkflowId, WfJobId, or WfStepId is provided, all three must be provided");
                    }

                    // All three exist - process workflow step completion
                    await ProcessWorkflowStepCompletionAsync(queueMessageDto);
                }
                else
                {
                    // None of the three exist - just process the queue
                    await ProcessQueueOnlyAsync(queueMessageDto);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to process step completion for WorkflowId: {queueMessageDto.WorkflowId}, WfJobId: {queueMessageDto.WfJobId}, WfStepId: {queueMessageDto.WfStepId}");
                throw;
            }
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="scheduleDto">The schedule data to create</param>
        /// <returns>The created schedule ID</returns>
        public async Task<int> CreateNewScheduleAsync(ScheduleDto scheduleDto)
        {
            _logger.LogInformation($"Creating new schedule for workflow {scheduleDto.WorkflowId}");

            try
            {
                // Validate that the workflow exists
                if (scheduleDto.WorkflowId.HasValue)
                {
                    await ValidateWorkflowAsync(scheduleDto.WorkflowId.Value);
                }

                // Validate ScheduleTypeId
                if (scheduleDto.ScheduleTypeId > 0)
                {
                    await _scheduleType.GetAsync(scheduleDto.ScheduleTypeId, false);
                }
                else
                {
                    throw new ArgumentException("ScheduleTypeId must be greater than 0");
                }

                // Validate ScheduleModeId
                if (scheduleDto.ScheduleModeId > 0)
                {
                    await _scheduleMode.GetAsync(scheduleDto.ScheduleModeId, false);
                }
                else
                {
                    throw new ArgumentException("ScheduleModeId must be greater than 0");
                }

                // Validate SchedulePurposeId if provided
                if (scheduleDto.SchedulePurposeId.HasValue && scheduleDto.SchedulePurposeId > 0)
                {
                    await _schedulePurpose.GetAsync(scheduleDto.SchedulePurposeId.Value, false);
                }

                // Validate RecurrenceFrequencyId if provided
                if (scheduleDto.RecurrenceFrequencyId.HasValue && scheduleDto.RecurrenceFrequencyId > 0)
                {
                    await _recurrenceFrequency.GetAsync(scheduleDto.RecurrenceFrequencyId.Value, false);
                }

                // Validate RecurOnId if provided
                if (scheduleDto.RecurOnId.HasValue && scheduleDto.RecurOnId > 0)
                {
                    await _recurOn.GetAsync(scheduleDto.RecurOnId.Value, false);
                }

                // Validate RecurOnPositionId if provided
                if (scheduleDto.RecurOnPositionId.HasValue && scheduleDto.RecurOnPositionId > 0)
                {
                    await _recurOnPosition.GetAsync(scheduleDto.RecurOnPositionId.Value, false);
                }

                // Set audit fields
                scheduleDto.CreatedOn = DateTimeOffset.UtcNow;
                scheduleDto.CreatedByName = _utilityFunctions.UserFullName;

                // Insert into workflow.Schedule table
                string sql = @"
                    INSERT INTO [workflow].[Schedule]
                    ([TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                     [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                     [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                     [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                     [RecurEveryX], [IncludeMonday], [IncludeTuesday], [IncludeWednesday], [IncludeThursday], [IncludeFriday],
                     [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode], [CreatedOn], [CreatedByName])
                    OUTPUT INSERTED.ScheduleId
                    VALUES
                    (@TenantId, @Name, @WorkflowId, @ScheduleTypeId, @WorkflowConditionId, @ScheduleModeId,
                     @SchedulePurposeId, @SimpleChainedQueueNames, @ParentEntityId, @ParentEntityIntId, @ParentEntityType,
                     @ScheduledNextRunAt, @IsEnabled, @Note, @ScheduleStartsOn, @ScheduleEndsOn, @TimezoneIanaId,
                     @FromTime, @ToTime, @ScheduledWhenDescription, @RecurrenceFrequencyId, @RecurOnId, @RecurOnPositionId,
                     @RecurEveryX, @IncludeMonday, @IncludeTuesday, @IncludeWednesday, @IncludeThursday, @IncludeFriday,
                     @IncludeSaturday, @IncludeSunday, @NotificationEventTypeCode, @CreatedOn, @CreatedByName)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(scheduleDto);
                    var scheduleId = await command.SelectSingle<int>();

                    _logger.LogInformation($"Successfully created new schedule with ID {scheduleId} for workflow {scheduleDto.WorkflowId}");
                    return scheduleId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {scheduleDto.WorkflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task ProcessWorkflowStepCompletionAsync(WorkflowQueueDto queueMessageDto)
        {
            // 6a. Get the WfJob through WfJobId
            var wfJob = await _workflowJob.GetWfJobDetailsAsync(queueMessageDto.WfJobId!.Value);
            if (wfJob == null)
            {
                throw new ArgumentException($"WfJob with ID {queueMessageDto.WfJobId} not found");
            }

            // 6b. Check if the WfStepId matches with Job record
            if (wfJob.WorkflowStepId != queueMessageDto.WfStepId)
            {
                var errorMessage = $"WfStepId {queueMessageDto.WfStepId} does not match the current step {wfJob.WorkflowStepId} in WfJob {queueMessageDto.WfJobId}";
                _logger.LogError(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }

            // 6c. Get WorkflowStepConnectors using WfStepId as ParentWorkflowStepId
            var connectors = await _workflowStepConnector.GetListAsync(
                queueMessageDto.WorkflowId!.Value,
                parentWorkflowStepId: queueMessageDto.WfStepId!.Value);

            if (!connectors.Any())
            {
                _logger.LogInformation($"No workflow step connectors found for WorkflowId: {queueMessageDto.WorkflowId}, ParentStepId: {queueMessageDto.WfStepId}. Workflow may be complete.");
                return;
            }
            var workflowStepConnector  = connectors.FirstOrDefault();

            // 6d. If WorkflowConditionId is not null, write to 'wf-condition-evaluator' queue
            if (workflowStepConnector.WorkflowConditionId.HasValue)
            {
                var conditionQueueDto = new WorkflowQueueDto
                {
                    ScheduleId = queueMessageDto.ScheduleId,
                    WorkflowId = queueMessageDto.WorkflowId,
                    WfJobId = queueMessageDto.WfJobId,
                    WfStepId = queueMessageDto.WfStepId,
                    TenantId = queueMessageDto.TenantId,
                    NotificationEventTypeCode = queueMessageDto.NotificationEventTypeCode,
                    MetaData = queueMessageDto.MetaData
                };

                await _azureQueue.WriteToQueueAsync("wf-condition-evaluator", conditionQueueDto);
                _logger.LogInformation($"Sent message to wf-condition-evaluator queue");
            }
            else
            {
                // 6e. If WorkflowConditionId is null, update WfJob and process next step
                await UpdateWfJobStepAsync(queueMessageDto.WfJobId!.Value, workflowStepConnector.ChildWorkflowStepId);

                // 6f. Update WorkflowQueueDto with new WfStepId and process next queue
                var updatedQueueDto = new WorkflowQueueDto
                {
                    ScheduleId = queueMessageDto.ScheduleId,
                    WorkflowId = queueMessageDto.WorkflowId,
                    WfJobId = queueMessageDto.WfJobId,
                    WfStepId = workflowStepConnector.ChildWorkflowStepId,
                    ConditionId = queueMessageDto.ConditionId,
                    TenantId = queueMessageDto.TenantId,
                    NextQueueNames = queueMessageDto.NextQueueNames,
                    NotificationEventTypeCode = queueMessageDto.NotificationEventTypeCode,
                    MetaData = queueMessageDto.MetaData
                };

                await ProcessNextQueueAsync(updatedQueueDto);
            }
        }

        private async Task ProcessQueueOnlyAsync(WorkflowQueueDto queueMessageDto)
        {
            // 7a. Update the WorkflowQueueDto by removing the first queue name and write to next queue
            await ProcessNextQueueAsync(queueMessageDto);
        }

        private async Task ProcessNextQueueAsync(WorkflowQueueDto queueMessageDto)
        {
            if (string.IsNullOrWhiteSpace(queueMessageDto.NextQueueNames))
            {
                _logger.LogInformation("No more queues to process.");
                return;
            }

            var queueNames = queueMessageDto.NextQueueNames.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                                           .Select(q => q.Trim())
                                                           .ToList();

            if (!queueNames.Any())
            {
                _logger.LogInformation("No valid queue names found. Workflow step completion finished.");
                return;
            }

            // Get the next queue name (first in the list)
            var nextQueueName = queueNames.First();

            // Remove the first queue name from the list
            queueNames.RemoveAt(0);

            // Update the NextQueueNames with remaining queues
            var updatedQueueDto = new WorkflowQueueDto
            {
                ScheduleId = queueMessageDto.ScheduleId,
                WorkflowId = queueMessageDto.WorkflowId,
                WfJobId = queueMessageDto.WfJobId,
                WfStepId = queueMessageDto.WfStepId,
                ConditionId = queueMessageDto.ConditionId,
                TenantId = queueMessageDto.TenantId,
                NextQueueNames = queueNames.Any() ? string.Join(",", queueNames) : null,
                NotificationEventTypeCode = queueMessageDto.NotificationEventTypeCode,
                MetaData = queueMessageDto.MetaData
            };

            // Write to the next queue
            await _azureQueue.WriteToQueueAsync(nextQueueName, updatedQueueDto);
            _logger.LogInformation($"Sent message to queue: {nextQueueName}");
        }

        private async Task UpdateWfJobStepAsync(long wfJobId, int newStepId)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WorkflowStepId] = @newStepId,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("newStepId", newStepId);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }

            _logger.LogInformation($"Updated WfJob {wfJobId} to step {newStepId}");
        }

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }
            }
        }





        #endregion
    }
}
