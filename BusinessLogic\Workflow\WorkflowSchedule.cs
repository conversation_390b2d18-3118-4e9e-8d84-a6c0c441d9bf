using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;

        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache, JobStatus jobStatus)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync()
        {
            // Empty method
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="scheduleDto">The schedule data to create</param>
        /// <returns>The created schedule ID</returns>
        public async Task<int> CreateNewScheduleAsync(ScheduleDto scheduleDto)
        {
            _logger.LogInformation($"Creating new schedule for workflow {scheduleDto.WorkflowId}");

            try
            {
                // Validate that the workflow exists
                if (scheduleDto.WorkflowId.HasValue)
                {
                    await ValidateWorkflowAsync(scheduleDto.WorkflowId.Value);
                }

                // Set audit fields
                scheduleDto.CreatedOn = DateTimeOffset.UtcNow;
                scheduleDto.CreatedByName = _utilityFunctions.UserFullName;

                // Insert into workflow.Schedule table
                string sql = @"
                    INSERT INTO [workflow].[Schedule]
                    ([TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                     [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                     [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                     [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                     [RecurEveryX], [IncludeMonday], [IncludeTuesday], [IncludeWednesday], [IncludeThursday], [IncludeFriday],
                     [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode], [CreatedOn], [CreatedByName])
                    OUTPUT INSERTED.ScheduleId
                    VALUES
                    (@TenantId, @Name, @WorkflowId, @ScheduleTypeId, @WorkflowConditionId, @ScheduleModeId,
                     @SchedulePurposeId, @SimpleChainedQueueNames, @ParentEntityId, @ParentEntityIntId, @ParentEntityType,
                     @ScheduledNextRunAt, @IsEnabled, @Note, @ScheduleStartsOn, @ScheduleEndsOn, @TimezoneIanaId,
                     @FromTime, @ToTime, @ScheduledWhenDescription, @RecurrenceFrequencyId, @RecurOnId, @RecurOnPositionId,
                     @RecurEveryX, @IncludeMonday, @IncludeTuesday, @IncludeWednesday, @IncludeThursday, @IncludeFriday,
                     @IncludeSaturday, @IncludeSunday, @NotificationEventTypeCode, @CreatedOn, @CreatedByName)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(scheduleDto);
                    var scheduleId = await command.SelectSingle<int>();

                    _logger.LogInformation($"Successfully created new schedule with ID {scheduleId} for workflow {scheduleDto.WorkflowId}");
                    return scheduleId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {scheduleDto.WorkflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }
            }
        }



        #endregion
    }
}
