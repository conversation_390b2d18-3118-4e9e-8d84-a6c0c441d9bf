using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="completedStepId">The ID of the step that was completed</param>
        /// <param name="nextStepId">The ID of the next step to schedule (optional)</param>
        /// <param name="scheduledAt">When to schedule the next step (optional, defaults to now)</param>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync(long wfJobId, int completedStepId, int? nextStepId = null, DateTimeOffset? scheduledAt = null)
        {
            _logger.LogInformation($"Completing step {completedStepId} and scheduling next step for workflow job {wfJobId}");

            try
            {
                // Start transaction for atomic operation
                using (var transaction = await _unitOfWork.BeginTransactionAsync())
                {
                    try
                    {
                        // Update the current workflow job to mark the step as completed
                        await UpdateWorkflowJobStepAsync(wfJobId, completedStepId, isCompleted: true);

                        // If a next step is specified, schedule it
                        if (nextStepId.HasValue)
                        {
                            await ScheduleWorkflowStepAsync(wfJobId, nextStepId.Value, scheduledAt ?? DateTimeOffset.UtcNow);
                        }
                        else
                        {
                            // If no next step, mark the entire workflow job as completed
                            await CompleteWorkflowJobAsync(wfJobId);
                        }

                        await transaction.CommitAsync();
                        _logger.LogInformation($"Successfully completed step {completedStepId} and scheduled next step for workflow job {wfJobId}");
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        _logger.LogError(ex, $"Error completing step {completedStepId} for workflow job {wfJobId}");
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to complete step and schedule next for workflow job {wfJobId}");
                throw;
            }
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="workflowId">The workflow ID to schedule</param>
        /// <param name="scheduledAt">When to schedule the workflow</param>
        /// <param name="name">Optional name for the scheduled workflow</param>
        /// <param name="tenantId">Optional tenant ID</param>
        /// <param name="owningPartyId">Optional owning party ID</param>
        /// <returns>The created workflow job ID</returns>
        public async Task<long> CreateNewScheduleAsync(int workflowId, DateTimeOffset scheduledAt, string? name = null, int? tenantId = null, int? owningPartyId = null)
        {
            _logger.LogInformation($"Creating new schedule for workflow {workflowId} at {scheduledAt}");

            try
            {
                // Validate that the workflow exists and is enabled
                await ValidateWorkflowAsync(workflowId);

                // Create new workflow job
                var wfJobDto = new WfJobDto
                {
                    Name = name ?? $"Scheduled Workflow {workflowId}",
                    WorkflowId = workflowId,
                    ScheduledAt = scheduledAt,
                    IsScheduled = true,
                    WfJobStatusId = 1, // Pending status
                    TenantId = tenantId,
                    OwningPartyId = owningPartyId,
                    CreatedOn = DateTimeOffset.UtcNow,
                    CreatedByName = _utilityFunctions.UserFullName
                };

                var wfJobId = await CreateWorkflowJobAsync(wfJobDto);

                _logger.LogInformation($"Successfully created new schedule with job ID {wfJobId} for workflow {workflowId}");
                return wfJobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {workflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task UpdateWorkflowJobStepAsync(long wfJobId, int stepId, bool isCompleted)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WorkflowStepId] = @stepId,
                    [CompletedAt] = @completedAt,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("stepId", stepId);
                command.AddArgument("completedAt", isCompleted ? DateTimeOffset.UtcNow : (DateTimeOffset?)null);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task ScheduleWorkflowStepAsync(long wfJobId, int nextStepId, DateTimeOffset scheduledAt)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WorkflowStepId] = @nextStepId,
                    [ScheduledAt] = @scheduledAt,
                    [WfJobStatusId] = 1,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("nextStepId", nextStepId);
                command.AddArgument("scheduledAt", scheduledAt);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task CompleteWorkflowJobAsync(long wfJobId)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WfJobStatusId] = 3,
                    [CompletedAt] = @completedAt,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("completedAt", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId], [IsEnabled]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }

                if (!workflow.IsEnabled)
                {
                    throw new InvalidOperationException($"Workflow with ID {workflowId} is not enabled");
                }
            }
        }

        private async Task<long> CreateWorkflowJobAsync(WfJobDto wfJobDto)
        {
            string sql = @"
                INSERT INTO [workflow].[WfJob]
                ([TenantId], [Name], [WorkflowId], [ScheduledAt], [IsScheduled], [WfJobStatusId], 
                 [OwningPartyId], [CreatedOn], [CreatedByName])
                OUTPUT INSERTED.WfJobId
                VALUES
                (@TenantId, @Name, @WorkflowId, @ScheduledAt, @IsScheduled, @WfJobStatusId, 
                 @OwningPartyId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(wfJobDto);
                return await command.SelectSingle<long>();
            }
        }

        #endregion
    }
}
