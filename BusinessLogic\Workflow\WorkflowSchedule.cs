using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;

        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache, JobStatus jobStatus)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync()
        {
            // Empty method
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="scheduleDto">The schedule data to create</param>
        /// <returns>The created schedule ID</returns>
        public async Task<int> CreateNewScheduleAsync(ScheduleDto scheduleDto)
        {
            _logger.LogInformation($"Creating new schedule for workflow {scheduleDto.WorkflowId}");

            try
            {
                // Validate that the workflow exists
                if (scheduleDto.WorkflowId.HasValue)
                {
                    await ValidateWorkflowAsync(scheduleDto.WorkflowId.Value);
                }

                // Validate ScheduleTypeId
                if (scheduleDto.ScheduleTypeId > 0)
                {
                    await ValidateScheduleTypeAsync(scheduleDto.ScheduleTypeId);
                }
                else
                {
                    throw new ArgumentException("ScheduleTypeId must be greater than 0");
                }

                // Validate ScheduleModeId
                if (scheduleDto.ScheduleModeId > 0)
                {
                    await ValidateScheduleModeAsync(scheduleDto.ScheduleModeId);
                }
                else
                {
                    throw new ArgumentException("ScheduleModeId must be greater than 0");
                }

                // Validate SchedulePurposeId if provided
                if (scheduleDto.SchedulePurposeId.HasValue && scheduleDto.SchedulePurposeId > 0)
                {
                    await ValidateSchedulePurposeAsync(scheduleDto.SchedulePurposeId.Value);
                }

                // Validate RecurrenceFrequencyId if provided
                if (scheduleDto.RecurrenceFrequencyId.HasValue && scheduleDto.RecurrenceFrequencyId > 0)
                {
                    await ValidateRecurrenceFrequencyAsync(scheduleDto.RecurrenceFrequencyId.Value);
                }

                // Validate RecurOnId if provided
                if (scheduleDto.RecurOnId.HasValue && scheduleDto.RecurOnId > 0)
                {
                    await ValidateRecurOnAsync(scheduleDto.RecurOnId.Value);
                }

                // Validate RecurOnPositionId if provided
                if (scheduleDto.RecurOnPositionId.HasValue && scheduleDto.RecurOnPositionId > 0)
                {
                    await ValidateRecurOnPositionAsync(scheduleDto.RecurOnPositionId.Value);
                }

                // Set audit fields
                scheduleDto.CreatedOn = DateTimeOffset.UtcNow;
                scheduleDto.CreatedByName = _utilityFunctions.UserFullName;

                // Insert into workflow.Schedule table
                string sql = @"
                    INSERT INTO [workflow].[Schedule]
                    ([TenantId], [Name], [WorkflowId], [ScheduleTypeId], [WorkflowConditionId], [ScheduleModeId],
                     [SchedulePurposeId], [SimpleChainedQueueNames], [ParentEntityId], [ParentEntityIntId], [ParentEntityType],
                     [ScheduledNextRunAt], [IsEnabled], [Note], [ScheduleStartsOn], [ScheduleEndsOn], [TimezoneIanaId],
                     [FromTime], [ToTime], [ScheduledWhenDescription], [RecurrenceFrequencyId], [RecurOnId], [RecurOnPositionId],
                     [RecurEveryX], [IncludeMonday], [IncludeTuesday], [IncludeWednesday], [IncludeThursday], [IncludeFriday],
                     [IncludeSaturday], [IncludeSunday], [NotificationEventTypeCode], [CreatedOn], [CreatedByName])
                    OUTPUT INSERTED.ScheduleId
                    VALUES
                    (@TenantId, @Name, @WorkflowId, @ScheduleTypeId, @WorkflowConditionId, @ScheduleModeId,
                     @SchedulePurposeId, @SimpleChainedQueueNames, @ParentEntityId, @ParentEntityIntId, @ParentEntityType,
                     @ScheduledNextRunAt, @IsEnabled, @Note, @ScheduleStartsOn, @ScheduleEndsOn, @TimezoneIanaId,
                     @FromTime, @ToTime, @ScheduledWhenDescription, @RecurrenceFrequencyId, @RecurOnId, @RecurOnPositionId,
                     @RecurEveryX, @IncludeMonday, @IncludeTuesday, @IncludeWednesday, @IncludeThursday, @IncludeFriday,
                     @IncludeSaturday, @IncludeSunday, @NotificationEventTypeCode, @CreatedOn, @CreatedByName)";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
                {
                    command.AddArguments(scheduleDto);
                    var scheduleId = await command.SelectSingle<int>();

                    _logger.LogInformation($"Successfully created new schedule with ID {scheduleId} for workflow {scheduleDto.WorkflowId}");
                    return scheduleId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {scheduleDto.WorkflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }
            }
        }



        private async Task ValidateScheduleTypeAsync(short scheduleTypeId)
        {
            string sql = @"
                SELECT [ScheduleTypeId]
                FROM [workflow].[ScheduleType]
                WHERE [ScheduleTypeId] = @scheduleTypeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleTypeId", scheduleTypeId);
                var scheduleType = await command.SelectSingle<ScheduleTypeDto>();

                if (scheduleType == null)
                {
                    throw new ArgumentException($"ScheduleType with ID {scheduleTypeId} not found");
                }
            }
        }

        private async Task ValidateScheduleModeAsync(short scheduleModeId)
        {
            string sql = @"
                SELECT [ScheduleModeId]
                FROM [workflow].[ScheduleMode]
                WHERE [ScheduleModeId] = @scheduleModeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleModeId", scheduleModeId);
                var scheduleMode = await command.SelectSingle<ScheduleModeDto>();

                if (scheduleMode == null)
                {
                    throw new ArgumentException($"ScheduleMode with ID {scheduleModeId} not found");
                }
            }
        }

        private async Task ValidateSchedulePurposeAsync(short schedulePurposeId)
        {
            string sql = @"
                SELECT [SchedulePurposeId]
                FROM [workflow].[SchedulePurpose]
                WHERE [SchedulePurposeId] = @schedulePurposeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("schedulePurposeId", schedulePurposeId);
                var schedulePurpose = await command.SelectSingle<dynamic>();

                if (schedulePurpose == null)
                {
                    throw new ArgumentException($"SchedulePurpose with ID {schedulePurposeId} not found");
                }
            }
        }

        private async Task ValidateRecurrenceFrequencyAsync(short recurrenceFrequencyId)
        {
            string sql = @"
                SELECT [RecurrenceFrequencyId]
                FROM [workflow].[RecurrenceFrequency]
                WHERE [RecurrenceFrequencyId] = @recurrenceFrequencyId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurrenceFrequencyId", recurrenceFrequencyId);
                var recurrenceFrequency = await command.SelectSingle<dynamic>();

                if (recurrenceFrequency == null)
                {
                    throw new ArgumentException($"RecurrenceFrequency with ID {recurrenceFrequencyId} not found");
                }
            }
        }

        private async Task ValidateRecurOnAsync(byte recurOnId)
        {
            string sql = @"
                SELECT [RecurOnId]
                FROM [workflow].[RecurOn]
                WHERE [RecurOnId] = @recurOnId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurOnId", recurOnId);
                var recurOn = await command.SelectSingle<dynamic>();

                if (recurOn == null)
                {
                    throw new ArgumentException($"RecurOn with ID {recurOnId} not found");
                }
            }
        }

        private async Task ValidateRecurOnPositionAsync(byte recurOnPositionId)
        {
            string sql = @"
                SELECT [RecurOnPositionId]
                FROM [workflow].[RecurOnPosition]
                WHERE [RecurOnPositionId] = @recurOnPositionId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("recurOnPositionId", recurOnPositionId);
                var recurOnPosition = await command.SelectSingle<dynamic>();

                if (recurOnPosition == null)
                {
                    throw new ArgumentException($"RecurOnPosition with ID {recurOnPositionId} not found");
                }
            }
        }

        #endregion
    }
}
