using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow scheduling operations
    /// </summary>
    public class WorkflowSchedule : BusinessLogicBase
    {
        private readonly JobStatus _jobStatus;

        public WorkflowSchedule(IUnitOfWork unitOfWork, ILogger<WorkflowSchedule> logger, UtilityFunctions utilityFunctions, Cache cache, JobStatus jobStatus)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
            _jobStatus = jobStatus;
        }

        /// <summary>
        /// Complete a workflow step and schedule the next step in the workflow
        /// </summary>
        /// <returns>Task</returns>
        public async Task StepCompleteScheduleNextAsync()
        {
            // Empty method as requested
        }

        /// <summary>
        /// Create a new workflow schedule
        /// </summary>
        /// <param name="scheduleDto">The schedule data to create</param>
        /// <returns>The created workflow job ID</returns>
        public async Task<long> CreateNewScheduleAsync(ScheduleDto scheduleDto)
        {
            _logger.LogInformation($"Creating new schedule for workflow {scheduleDto.WorkflowId}");

            try
            {
                // Validate that the workflow exists and is enabled if WorkflowId is provided
                if (scheduleDto.WorkflowId.HasValue)
                {
                    await ValidateWorkflowAsync(scheduleDto.WorkflowId.Value);
                }

                // Validate status values
                await _jobStatus.GetAsync(JobStatusEnum.Scheduled);

                // Create new workflow job
                var wfJobDto = new WfJobDto
                {
                    Name = scheduleDto.Name ?? $"Scheduled Workflow {scheduleDto.WorkflowId}",
                    WorkflowId = scheduleDto.WorkflowId,
                    WorkflowConditionId = scheduleDto.WorkflowConditionId,
                    ScheduledAt = scheduleDto.ScheduledNextRunAt ?? DateTimeOffset.UtcNow,
                    IsScheduled = true,
                    WfJobStatusId = (short)JobStatusEnum.Scheduled,
                    SchedulePurposeId = scheduleDto.SchedulePurposeId,
                    TenantId = scheduleDto.TenantId,
                    WfJobCancelledReasonId = (byte)WfJobCancelledReasonEnum.NotCancelled,
                    CreatedOn = DateTimeOffset.UtcNow,
                    CreatedByName = _utilityFunctions.UserFullName
                };

                var wfJobId = await CreateWorkflowJobAsync(wfJobDto);

                _logger.LogInformation($"Successfully created new schedule with job ID {wfJobId} for workflow {scheduleDto.WorkflowId}");
                return wfJobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to create new schedule for workflow {scheduleDto.WorkflowId}");
                throw;
            }
        }

        #region Private Helper Methods

        private async Task UpdateWorkflowJobStepAsync(long wfJobId, int stepId, bool isCompleted)
        {
            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WorkflowStepId] = @stepId,
                    [CompletedAt] = @completedAt,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("stepId", stepId);
                command.AddArgument("completedAt", isCompleted ? DateTimeOffset.UtcNow : (DateTimeOffset?)null);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task ScheduleWorkflowStepAsync(long wfJobId, int nextStepId, DateTimeOffset scheduledAt)
        {
            // Validate status values
            await _jobStatus.GetAsync(JobStatusEnum.Scheduled);

            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WorkflowStepId] = @nextStepId,
                    [ScheduledAt] = @scheduledAt,
                    [WfJobStatusId] = @scheduledStatusId,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("nextStepId", nextStepId);
                command.AddArgument("scheduledAt", scheduledAt);
                command.AddArgument("scheduledStatusId", (short)JobStatusEnum.Scheduled);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task CompleteWorkflowJobAsync(long wfJobId)
        {
            // Validate status values
            await _jobStatus.GetAsync(JobStatusEnum.Completed);

            string sql = @"
                UPDATE [workflow].[WfJob]
                SET [WfJobStatusId] = @completedStatusId,
                    [CompletedAt] = @completedAt,
                    [ModifiedOn] = @modifiedOn,
                    [ModifiedByName] = @modifiedByName
                WHERE [WfJobId] = @wfJobId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("wfJobId", wfJobId);
                command.AddArgument("completedStatusId", (short)JobStatusEnum.Completed);
                command.AddArgument("completedAt", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedOn", DateTimeOffset.UtcNow);
                command.AddArgument("modifiedByName", _utilityFunctions.UserFullName);

                await command.Execute();
            }
        }

        private async Task ValidateWorkflowAsync(int workflowId)
        {
            string sql = @"
                SELECT [WorkflowId], [IsEnabled]
                FROM [workflow].[Workflow]
                WHERE [WorkflowId] = @workflowId AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowId", workflowId);
                var workflow = await command.SelectSingle<WorkflowDto>();

                if (workflow == null)
                {
                    throw new ArgumentException($"Workflow with ID {workflowId} not found");
                }

                if (!workflow.IsEnabled)
                {
                    throw new InvalidOperationException($"Workflow with ID {workflowId} is not enabled");
                }
            }
        }

        private async Task<long> CreateWorkflowJobAsync(WfJobDto wfJobDto)
        {
            string sql = @"
                INSERT INTO [workflow].[WfJob]
                ([TenantId], [Name], [WorkflowId], [ScheduledAt], [IsScheduled], [WfJobStatusId], 
                 [OwningPartyId], [CreatedOn], [CreatedByName])
                OUTPUT INSERTED.WfJobId
                VALUES
                (@TenantId, @Name, @WorkflowId, @ScheduledAt, @IsScheduled, @WfJobStatusId, 
                 @OwningPartyId, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(wfJobDto);
                return await command.SelectSingle<long>();
            }
        }

        #endregion
    }
}
