using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job cancelled reason operations
    /// Acts as a lookup table for cancellation reasons
    /// </summary>
    public class WfJobCancelledReason : BusinessLogicBase
    {
        public WfJobCancelledReason(IUnitOfWork unitOfWork, ILogger<WfJobCancelledReason> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single WfJobCancelledReason by ID
        /// </summary>
        /// <param name="wfJobCancelledReasonId">The workflow job cancelled reason ID</param>
        /// <returns>WfJobCancelledReasonDto or null if not found</returns>
        public async Task<WfJobCancelledReasonDto?> GetAsync(byte wfJobCancelledReasonId)
        {
            _logger.LogDebug($"Getting workflow job cancelled reason for ID: {wfJobCancelledReasonId}");

            try
            {
                // Check cache first
                var cacheKey = $"WfJobCancelledReason_{wfJobCancelledReasonId}";
                var (exists, cachedReason) = await _cache.GetCacheItem<WfJobCancelledReasonDto>("WfJobCancelledReason", cacheKey);
                if (exists && cachedReason != null)
                {
                    return cachedReason;
                }

                string sql = @"
                    SELECT [WfJobCancelledReasonId]
                          ,[Label]
                          ,[IsEnabled]
                          ,[SortOrder]
                          ,[Description]
                    FROM [workflow].[WfJobCancelledReason]
                    WHERE [WfJobCancelledReasonId] = @wfJobCancelledReasonId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("wfJobCancelledReasonId", wfJobCancelledReasonId);
                    var reason = await command.SelectSingle<WfJobCancelledReasonDto>();

                    // Cache the result if found
                    if (reason != null)
                    {
                        await _cache.SetCacheItem("WfJobCancelledReason", cacheKey, reason, cacheForMinutes: 30);
                    }

                    return reason;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get workflow job cancelled reason for ID: {wfJobCancelledReasonId}");
                throw;
            }
        }

        /// <summary>
        /// Get all workflow job cancelled reasons
        /// </summary>
        /// <returns>List of WfJobCancelledReasonDto</returns>
        public async Task<List<WfJobCancelledReasonDto>> GetAllAsync()
        {
            _logger.LogDebug("Getting all workflow job cancelled reasons");

            try
            {
                // Check cache first
                var cacheKey = "AllWfJobCancelledReasons";
                var (exists, cachedReasons) = await _cache.GetCacheItem<List<WfJobCancelledReasonDto>>("WfJobCancelledReason", cacheKey);
                if (exists && cachedReasons != null)
                {
                    return cachedReasons;
                }

                string sql = @"
                    SELECT [WfJobCancelledReasonId]
                          ,[Label]
                          ,[IsEnabled]
                          ,[SortOrder]
                          ,[Description]
                    FROM [workflow].[WfJobCancelledReason]
                    ORDER BY [SortOrder], [Label]";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    var reasons = await command.SelectMany<WfJobCancelledReasonDto>();

                    // Cache the result
                    await _cache.SetCacheItem("WfJobCancelledReason", cacheKey, reasons, cacheForMinutes: 30);

                    return reasons;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get all workflow job cancelled reasons");
                throw;
            }
        }

        /// <summary>
        /// Validate that a cancelled reason ID exists and is enabled
        /// </summary>
        /// <param name="wfJobCancelledReasonId">The workflow job cancelled reason ID to validate</param>
        /// <returns>True if valid and enabled, false otherwise</returns>
        public async Task<bool> IsValidReasonAsync(byte wfJobCancelledReasonId)
        {
            try
            {
                var reason = await GetAsync(wfJobCancelledReasonId);
                return reason != null && reason.IsEnabled;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to validate workflow job cancelled reason ID: {wfJobCancelledReasonId}");
                return false;
            }
        }

        /// <summary>
        /// Get the enum value for a given cancelled reason ID
        /// </summary>
        /// <param name="wfJobCancelledReasonId">The workflow job cancelled reason ID</param>
        /// <returns>The corresponding WfJobCancelledReasonEnum value, or null if not found</returns>
        public WfJobCancelledReasonEnum? GetReasonEnum(byte wfJobCancelledReasonId)
        {
            if (Enum.IsDefined(typeof(WfJobCancelledReasonEnum), (byte)wfJobCancelledReasonId))
            {
                return (WfJobCancelledReasonEnum)wfJobCancelledReasonId;
            }
            return null;
        }

        /// <summary>
        /// Get the reason ID for a given enum value
        /// </summary>
        /// <param name="reasonEnum">The WfJobCancelledReasonEnum value</param>
        /// <returns>The corresponding reason ID</returns>
        public byte GetReasonId(WfJobCancelledReasonEnum reasonEnum)
        {
            return (byte)reasonEnum;
        }

        /// <summary>
        /// Validate and get the reason ID, throwing an exception if invalid
        /// </summary>
        /// <param name="wfJobCancelledReasonId">The workflow job cancelled reason ID to validate</param>
        /// <returns>The validated reason ID</returns>
        /// <exception cref="ArgumentException">Thrown when the reason ID is invalid</exception>
        public async Task<byte> ValidateAndGetReasonIdAsync(byte wfJobCancelledReasonId)
        {
            var isValid = await IsValidReasonAsync(wfJobCancelledReasonId);
            if (!isValid)
            {
                throw new ArgumentException($"Invalid or disabled workflow job cancelled reason ID: {wfJobCancelledReasonId}");
            }
            return wfJobCancelledReasonId;
        }

        /// <summary>
        /// Get reason ID from enum, ensuring it exists in the database
        /// </summary>
        /// <param name="reasonEnum">The WfJobCancelledReasonEnum value</param>
        /// <returns>The validated reason ID</returns>
        /// <exception cref="ArgumentException">Thrown when the enum value doesn't correspond to a valid database record</exception>
        public async Task<byte> GetValidatedReasonIdFromEnumAsync(WfJobCancelledReasonEnum reasonEnum)
        {
            var reasonId = GetReasonId(reasonEnum);
            return await ValidateAndGetReasonIdAsync(reasonId);
        }
    }
}
