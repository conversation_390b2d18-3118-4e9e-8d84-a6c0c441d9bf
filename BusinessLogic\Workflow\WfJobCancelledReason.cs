using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job cancelled reason operations
    /// Acts as a lookup table for cancellation reasons
    /// </summary>
    public class WfJobCancelledReason : BusinessLogicBase
    {
        public WfJobCancelledReason(IUnitOfWork unitOfWork, ILogger<WfJobCancelledReason> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single WfJobCancelledReason by enum
        /// </summary>
        /// <param name="cancelledReasonEnum">The cancelled reason enum</param>
        /// <param name="ignoreErrorIfNotExists">If true, returns null when not found instead of throwing an exception</param>
        /// <returns>WfJobCancelledReasonDto or null if not found</returns>
        public async Task<WfJobCancelledReasonDto?> GetAsync(WfJobCancelledReasonEnum cancelledReasonEnum, bool ignoreErrorIfNotExists = false)
        {
            return await GetAsync((byte)cancelledReasonEnum, ignoreErrorIfNotExists);
        }

        /// <summary>
        /// Get a single WfJobCancelledReason by ID
        /// </summary>
        /// <param name="wfJobCancelledReasonId">The workflow job cancelled reason ID</param>
        /// <param name="ignoreErrorIfNotExists">If true, returns null when not found instead of throwing an exception</param>
        /// <returns>WfJobCancelledReasonDto or null if not found</returns>
        public async Task<WfJobCancelledReasonDto?> GetAsync(byte wfJobCancelledReasonId, bool ignoreErrorIfNotExists = false)
        {
            _logger.LogDebug($"Getting workflow job cancelled reason for ID: {wfJobCancelledReasonId}");

            try
            {
                // Check cache first
                var cacheKey = $"WfJobCancelledReason_{wfJobCancelledReasonId}";
                var (exists, cachedReason) = await _cache.GetCacheItem<WfJobCancelledReasonDto>("WfJobCancelledReason", cacheKey);
                if (exists && cachedReason != null)
                {
                    return cachedReason;
                }

                string sql = @"
                    SELECT [WfJobCancelledReasonId]
                          ,[Label]
                          ,[IsEnabled]
                          ,[SortOrder]
                          ,[Description]
                    FROM [workflow].[WfJobCancelledReason]
                    WHERE [WfJobCancelledReasonId] = @wfJobCancelledReasonId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("wfJobCancelledReasonId", wfJobCancelledReasonId);
                    var reason = await command.SelectSingle<WfJobCancelledReasonDto>();

                    // Cache the result if found
                    if (reason != null)
                    {
                        await _cache.SetCacheItem("WfJobCancelledReason", cacheKey, reason, cacheForMinutes: 30);
                    }
                    else if (!ignoreErrorIfNotExists)
                    {
                        throw new ArgumentException($"WfJobCancelledReason with ID {wfJobCancelledReasonId} not found");
                    }

                    return reason;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get workflow job cancelled reason for ID: {wfJobCancelledReasonId}");
                throw;
            }
        }
    }
}
