using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Policies
{
	public class Policy
	{
        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        /// <param name="policies"></param>
        public static void Register(AuthorizationOptions options, List<PolicyTypeDefinition> policies)
        {
            foreach (var policy in policies)
            {
                options.AddPolicy(policy.Policy, policy.Action);
            }
        }
    }
}
