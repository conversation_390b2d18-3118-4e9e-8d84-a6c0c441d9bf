using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WorkflowStepConnector : BusinessLogicBase
    {
        public WorkflowStepConnector(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WorkflowStepConnector by ID
        /// </summary>
        /// <param name="workflowStepConnectorId">The workflow step connector ID</param>
        /// <returns>WorkflowStepConnectorDto or null if not found</returns>
        internal async Task<WorkflowStepConnectorDto?> GetAsync(int workflowStepConnectorId)
        {
            string sql = @"
                SELECT [WorkflowStepConnectorId]
                      ,[ParentWorkflowStepId]
                      ,[ChildWorkflowStepId]
                      ,[WorkflowId]
                      ,[SortOrder]
                      ,[WorkflowConditionId]
                FROM [workflow].[WorkflowStepConnector]
                WHERE [WorkflowStepConnectorId] = @workflowStepConnectorId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowStepConnectorId", workflowStepConnectorId);
                return await command.SelectSingle<WorkflowStepConnectorDto>();
            }
        }

        /// <summary>
        /// Get a list of WorkflowStepConnectors with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="parentWorkflowStepId">Filter by parent workflow step ID</param>
        /// <param name="childWorkflowStepId">Filter by child workflow step ID</param>
        /// <returns>List of WorkflowStepConnectors</returns>
        internal async Task<ListResponseDto<WorkflowStepConnectorListDto>> GetListAsync(StandardListParameters? standardListParameters = null, int? workflowId = null, int? parentWorkflowStepId = null, int? childWorkflowStepId = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WorkflowStepConnectorId", "[WorkflowStepConnector].[WorkflowStepConnectorId]" },
                { "WorkflowId", "[WorkflowStepConnector].[WorkflowId]" },
                { "ParentWorkflowStepId", "[WorkflowStepConnector].[ParentWorkflowStepId]" },
                { "ChildWorkflowStepId", "[WorkflowStepConnector].[ChildWorkflowStepId]" },
                { "SortOrder", "[WorkflowStepConnector].[SortOrder]" }
            };

            string sql = @"
                SELECT [WorkflowStepConnectorId]
                      ,[ParentWorkflowStepId]
                      ,[ChildWorkflowStepId]
                      ,[WorkflowId]
                      ,[SortOrder]
                      ,[WorkflowConditionId]
                FROM [workflow].[WorkflowStepConnector]
                WHERE 1=1";

            if (workflowId.HasValue)
            {
                sql += " AND [WorkflowId] = @workflowId";
            }

            if (parentWorkflowStepId.HasValue)
            {
                sql += " AND [ParentWorkflowStepId] = @parentWorkflowStepId";
            }

            if (childWorkflowStepId.HasValue)
            {
                sql += " AND [ChildWorkflowStepId] = @childWorkflowStepId";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "SortOrder");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WorkflowStepConnectorListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (parentWorkflowStepId.HasValue)
                {
                    command.AddArgument("parentWorkflowStepId", parentWorkflowStepId.Value);
                }
                if (childWorkflowStepId.HasValue)
                {
                    command.AddArgument("childWorkflowStepId", childWorkflowStepId.Value);
                }
                result.List = await command.SelectMany<WorkflowStepConnectorListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WorkflowStepConnector]
                WHERE 1=1";

            if (workflowId.HasValue)
            {
                countSql += " AND [WorkflowId] = @workflowId";
            }

            if (parentWorkflowStepId.HasValue)
            {
                countSql += " AND [ParentWorkflowStepId] = @parentWorkflowStepId";
            }

            if (childWorkflowStepId.HasValue)
            {
                countSql += " AND [ChildWorkflowStepId] = @childWorkflowStepId";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (workflowId.HasValue)
                {
                    command.AddArgument("workflowId", workflowId.Value);
                }
                if (parentWorkflowStepId.HasValue)
                {
                    command.AddArgument("parentWorkflowStepId", parentWorkflowStepId.Value);
                }
                if (childWorkflowStepId.HasValue)
                {
                    command.AddArgument("childWorkflowStepId", childWorkflowStepId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
