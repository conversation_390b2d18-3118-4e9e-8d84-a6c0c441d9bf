using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get Workflow Step Type lookup data.
    /// </summary>
    [Route("api/WorkflowStepType")]
    public class WorkflowStepTypeController : AppController
    {
        private readonly WorkflowStepType _workflowStepType;

        public WorkflowStepTypeController(WorkflowStepType workflowStepType, IUnitOfWork unitOfWork)
        {
            _workflowStepType = workflowStepType;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Workflow Step Type
        /// </summary>
        /// <remarks>
        /// Returns a single Workflow Step Type record for a given ID.
        /// </remarks>
        /// <param name="workflowStepTypeId">The ID of a workflow step type record</param>
        /// <response code="200">Workflow Step Type returned, or null if not found</response>
        /// <response code="404">Workflow Step Type not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(WorkflowStepTypeDto), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(short workflowStepTypeId)
        {
            var result = await _workflowStepType.GetAsync(workflowStepTypeId);
            return Ok(result);
        }

        /// <summary>
        /// Get a List of Workflow Step Types
        /// </summary>
        /// <remarks>
        /// Returns a list of Workflow Step Types with filtering and paging options.
        /// Returns only enabled types by default.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <response code="200">List of Workflow Step Types returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<WorkflowStepTypeListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            var result = await _workflowStepType.GetListAsync(standardListParameters, showDisabled);
            return Ok(result);
        }
    }
}
