using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// When a schedule will recur on.
    /// 1 - Day of Month, 2 - Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.
    /// Requires RecurOnPosition to also be set
    /// </summary>
    [Mappable(nameof(RecurOnId))]
    public class RecurOnDto
    {
        /// <summary>
        /// When a schedule will recur on.
        /// 1 - Day of Month, 2 - Week Day, 3 - Weekend Day, 11 - Monday, 12 - Tuesday, 13 - Wednesday, 14 - Thursday, 15 - Friday, 16 - Saturday, 17 - Sunday.
        /// Requires RecurOnPosition to also be set
        /// </summary>
        public byte RecurOnId { get; set; }
        
        /// <summary>
        /// The display label for what the schedule recurs on (e.g., "Monday", "Week day", "Day of month")
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// The sort order for displaying recurrence options
        /// </summary>
        public byte? SortOrder { get; set; }
        
        /// <summary>
        /// Indicates whether this recurrence option is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for RecurOn
    /// </summary>
    public class RecurOnListDto : RecurOnDto
    {
    }
}
