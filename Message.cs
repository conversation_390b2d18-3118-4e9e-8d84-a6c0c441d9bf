﻿using Sql;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Message is used to send messages between people, groups, teams, etc
    /// It supports Chat, Chat/Forum Groups, and general messaging
    /// </summary>
    public class Message : BusinessLogicBase
    {
        private readonly ILogger _logger;
        
        public Message(
            ILogger logger, IUnitOfWork 
            unitOfWork
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get Tenant Message Channel record
        /// (Where MessageChannelTypeId is 30)
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<MessageChannelDto> GetTenantMessageChannel(int tenantId)
        {
            string sql = @"
SELECT *
FROM [common].[MessageChannel]
WHERE [TenantId] = @tenantId AND Deleted = 0 AND MessageChannelTypeId = 30
";
            using(var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("tenantId", tenantId);
                return await command.SelectSingle<MessageChannelDto>();
            }
        }

        /// <summary>
        /// Get Message Channel record
        /// </summary>
        /// <param name="messageChannelId"></param>
        /// <returns>MessageChannelDto</returns>
        public async Task<MessageChannelDto> GetMessageChannel(long messageChannelId)
        {
            string sql = @"
SELECT *
FROM [common].[MessageChannel]
WHERE [messageChannelId] = @messageChannelId AND Deleted = 0
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("messageChannelId", messageChannelId);
                return await command.SelectSingle<MessageChannelDto>();
            }
        }

        /// <summary>
        /// Get Message Channel record
        /// </summary>
        /// <param name="messageChannelId"></param>
        /// <returns>MessageChannelDto</returns>
        public async Task<MessageChannelDto> GetMessageChannelByGlobalId(long messageChannelGlobalId)
        {
            string sql = @"
SELECT *
FROM [common].[MessageChannel]
WHERE [ChannelGlobalId] = @messageChannelId AND Deleted = 0
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("ChannelGlobalId", messageChannelGlobalId);
                return await command.SelectSingle<MessageChannelDto>();
            }
        }

        /// <summary>
        /// Check if a partyId is part of a message channel
        /// Returns the Members record
        /// </summary>
        /// <param name="partyId">CRM PartyId</param>
        /// <param name="messageChannelId">Message Channel Id</param>
        /// <returns>MessageChannelMemberDto or null</returns>
        public async Task<MessageChannelMemberDto> GetChannelMembership(Guid partyId, int messageChannelId)
        {
            string sql = @"
SELECT *
FROM [common].[MessageChannelMember]
WHERE [PartyId] = @partyId 
    AND [MessageChannelId] = @messageChannelId
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("partyId", partyId);
                command.AddArgument("messageChannelId", messageChannelId);
                return await command.SelectSingle<MessageChannelMemberDto>();
            }
        }

        /// <summary>
        /// Add Party to Tenant General Message Channel if not already a member
        /// If previously a deleted member then the record will be undeleted
        /// <param name="tenantId">TenantId</param>
        /// <param name="partyId">CRM PartyId</param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ApplicationException"></exception>
        public async Task AddPartyToTenantMessageChannel(Guid? partyId, int tenantId)
        {
            if(partyId.GuidIsNullOrEmpty())
            {
                throw new ArgumentNullException(nameof(partyId), "Cannot add null or empty PartyId to tenant message channel.");
            }

            var messageChannelDto = await GetTenantMessageChannel(tenantId)
                ?? throw new ApiErrorException("Cannot find tenant message channel.");

            var membership = await GetChannelMembership(partyId!.Value, messageChannelDto.MessageChannelId);
            if (membership is not null)
            {
                if (membership.Deleted == true)
                {
                    membership.Deleted = false;
                    await UpdateMemberDeletedFlag(membership);
                }
            }
            else
            {
                membership = new MessageChannelMemberDto
                {
                    MessageChannelId = messageChannelDto.MessageChannelId,
                    PartyId = partyId.Value,
                    HasUnreadMessages = false,
                    LastSeenAt = null,
                    SortOrder = 1,
                    IsMuted = false,
                    IsPinned = false,
                    CreatedOn = DateTimeOffset.UtcNow,
                    CreatedByName = "System",
                    ModifiedOn = null,
                    ModifiedByName = null,
                    Deleted = false,
                };
                await CreateMessageChannelMember(membership);
            }
            
        }

        /// <summary>
        /// Remove Party from Tenant Message Channel
        /// </summary>
        /// <param name="partyId"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ApplicationException"></exception>
        public async Task RemovePartyFromTenantMessageChannel(Guid? partyId, int tenantId)
        {
            if (partyId.GuidIsNullOrEmpty())
            {
                throw new ArgumentNullException(nameof(partyId), "Cannot remove null or empty PartyId from tenant message channel.");
            }

            var messageChannelDto = await GetTenantMessageChannel(tenantId)
                ?? throw new ApiErrorException("Cannot find tenant message channel.");

            var membership = await GetChannelMembership(partyId!.Value, messageChannelDto.MessageChannelId);
            if (membership is not null)
            {
                membership.Deleted = true;
                await UpdateMemberDeletedFlag(membership);
            }
        }

        /// <summary>
        /// Add Party to Message Channel if not already a member
        /// If previously a deleted member then the record will be undeleted
        /// <param name="messageChannelId">Message Channel Id</param>
        /// <param name="partyId">CRM PartyId</param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ApplicationException"></exception>
        public async Task AddPartyToMessageChannel(Guid? partyId, long messageChannelId)
        {
            if (partyId.GuidIsNullOrEmpty())
            {
                throw new ArgumentNullException(nameof(partyId), "Cannot add null or empty PartyId to tenant message channel.");
            }

            var messageChannelDto = await GetMessageChannel(messageChannelId)
                ?? throw new ApiErrorException("Cannot find message channel " + messageChannelId);

            var membership = await GetChannelMembership(partyId!.Value, messageChannelDto.MessageChannelId);
            if (membership is not null)
            {
                if (membership.Deleted == true)
                {
                    membership.Deleted = false;
                    await UpdateMemberDeletedFlag(membership);
                }
            }
            else
            {
                membership = new MessageChannelMemberDto
                {
                    MessageChannelId = messageChannelDto.MessageChannelId,
                    PartyId = partyId.Value,
                    HasUnreadMessages = false,
                    LastSeenAt = null,
                    SortOrder = 1,
                    IsMuted = false,
                    IsPinned = false,
                    CreatedOn = DateTimeOffset.UtcNow,
                    CreatedByName = "System",
                    ModifiedOn = null,
                    ModifiedByName = null,
                    Deleted = false,
                };
                await CreateMessageChannelMember(membership);
            }

        }

        /// <summary>
        /// Remove Party from Message Channel
        /// </summary>
        /// <param name="partyId"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ApplicationException"></exception>
        public async Task RemovePartyFromMessageChannel(Guid? partyId, long messageChannelId)
        {
            if (partyId.GuidIsNullOrEmpty())
            {
                throw new ArgumentNullException(nameof(partyId), "Cannot remove null or empty PartyId from tenant message channel.");
            }

            var messageChannelDto = await GetMessageChannel(messageChannelId)
                ?? throw new ApiErrorException("Cannot find message channel " + messageChannelId);

            var membership = await GetChannelMembership(partyId!.Value, messageChannelDto.MessageChannelId);
            if (membership is not null)
            {
                membership.Deleted = true;
                await UpdateMemberDeletedFlag(membership);
            }
        }

        /// <summary>
        /// Create Message Channel Member Record
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task<int> CreateMessageChannelMember(MessageChannelMemberDto dto)
        {
            string sql = @"
INSERT INTO [common].[MessageChannelMember]
           ([MessageChannelId]
           ,[PartyId]
           ,[HasUnreadMessages]
           ,[LastSeenAt]
           ,[SortOrder]
           ,[IsMuted]
           ,[IsPinned]
           ,[CreatedOn]
           ,[CreatedByName]
           ,[ModifiedOn]
           ,[ModifiedByName]
           ,[Deleted])
     VALUES
           (@MessageChannelId
           ,@PartyId
           ,@HasUnreadMessages
           ,@LastSeenAt
           ,@SortOrder
           ,@IsMuted
           ,@IsPinned
           ,@CreatedOn
           ,@CreatedByName
           ,@ModifiedOn
           ,@ModifiedByName
           ,@Deleted)
";

            using(var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                command.AddArguments(dto);
                return await command.ExecuteAndReturnIdentity();
            }
        }

        /// <summary>
        /// Update Message Channel Member Record
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task UpdateMessageChannelMember(MessageChannelMemberDto dto)
        {
            dto.ModifiedByName = "System";
            dto.ModifiedOn = DateTimeOffset.UtcNow;

            string sql = @"
UPDATE [common].[MessageChannelMember]
SET [Deleted] = @Deleted
   ,[ModifiedOn] = @ModifiedOn
   ,[ModifiedByName] = @ModifiedByName
   ,[HasUnreadMessages] = @HasUnreadMessages
   ,[LastSeenAt] = @LastSeenAt
   ,[SortOrder] = @SortOrder
   ,[IsMuted] = @IsMuted
   ,[IsPinned] = @IsPinned
WHERE [MessageChannelMemberId] = @MessageChannelMemberId
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update Message Channel Member Deleted Flag
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public async Task UpdateMemberDeletedFlag(MessageChannelMemberDto dto)
        {
            dto.ModifiedByName = "System";
            dto.ModifiedOn = DateTimeOffset.UtcNow;

            string sql = @"
UPDATE [common].[MessageChannelMember]
SET [Deleted] = @Deleted
   ,[ModifiedOn] = @ModifiedOn
   ,[ModifiedByName] = @ModifiedByName
WHERE [MessageChannelMemberId] = @MessageChannelMemberId
";
            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }
    }
}
