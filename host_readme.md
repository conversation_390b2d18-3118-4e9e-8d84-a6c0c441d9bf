# Instructions for the host.json file settings.

# General
### "functionTimeout": "00:05:00"
After the trigger starts function execution, the function needs to return/respond within the time-out duration
Max timeout depends on plan. Consumption Plan is 10 minutes. Other plans have no limit (including Flex Consumption Plan).
**Note!** Http requests have a max of 230seconds regardless of this setting

# "blobs" - BlobTrigger
See https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-storage-blob?tabs=isolated-process%2Cextensionv5%2Cextensionv3&pivots=programming-language-javascript#hostjson-settings

### "maxDegreeOfParallelism": 1
The integer number of concurrent invocations allowed for all blob-triggered functions in a given function app. The minimum allowed value is 1.

### "poisonBlobThreshold": 5
The integer number of times to try processing a message before moving it to the poison queue. The minimum allowed value is 1.



# "queues" - QueueTrigger
See https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-storage-queue?tabs=isolated-process%2Cextensionv5%2Cextensionv3&pivots=programming-language-javascript#host-json

### "maxPollingInterval": "00:00:02"
The maximum interval between queue polls. The minimum interval is 00:00:00.100 (100 ms). Intervals increment up to maxPollingInterval.

### "visibilityTimeout": "00:05:00"
The time interval between retries when processing of a message fails. A read message that fails will visible to be read and processed again after this timeout.

### "batchSize": 1
The number of queue messages that the Functions runtime retrieves simultaneously and processes in parallel. When the number being processed gets down to the newBatchThreshold, the runtime gets another batch and starts processing those messages. So the maximum number of concurrent messages being processed per function is batchSize plus newBatchThreshold. This limit applies separately to each queue-triggered function. f you want to avoid parallel execution for messages received on one queue, you can set batchSize to 1.

### "maxDequeueCount": 5
Number of times to try processing a message before moving it to the poison queue

### "newBatchThreshold": 1
Whenever the number of messages being processed concurrently gets down to this number, the runtime retrieves another batch.

### "messageEncoding": "base64"
It represents the encoding format for messages. Valid values are base64 and none. Set to base64 to Auto Encode


# "http" - HttpTrigger
See https://learn.microsoft.com/en-us/azure/azure-functions/functions-bindings-http-webhook?tabs=isolated-process%2Cfunctionsv2&pivots=programming-language-javascript#hostjson-settings

### "routePrefix": "api"
The route prefix that applies to all routes. Use an empty string to remove the default prefix.

### "customHeaders": { "X-Content-Type-Options": "nosniff" }
Allows you to set custom headers in the HTTP response. The previous example adds the X-Content-Type-Options header to the response to avoid content type sniffing. This custom header applies to all HTTP triggered functions in the function app.

### "dynamicThrottlesEnabled": true
When enabled, this setting causes the request processing pipeline to periodically check system performance counters like connections/threads/processes/memory/cpu/etc and if any of those counters are over a built-in high threshold (80%), requests will be rejected with a 429 "Too Busy" response until the counter(s) return to normal levels.

### "maxConcurrentRequests": 100
The maximum number of HTTP functions that are executed in parallel. This value allows you to control concurrency, which can help manage resource utilization. For example, you might have an HTTP function that uses a large number of system resources (memory/cpu/sockets) such that it causes issues when concurrency is too high. Or you might have a function that makes outbound requests to a third-party service, and those calls need to be rate limited. In these cases, applying a throttle here can help.

### "maxOutstandingRequests": 200
The maximum number of outstanding requests that are held at any given time. This limit includes requests that are queued but have not started executing, as well as any in progress executions. Any incoming requests over this limit are rejected with a 429 "Too Busy" response. That allows callers to employ time-based retry strategies, and also helps you to control maximum request latencies. This only controls queuing that occurs within the script host execution path. Other queues such as the ASP.NET request queue will still be in effect and unaffected by this setting.