using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// This is a generic workflow job table to track each occurrence of a schedule (ie. when it is to run) or manually run job.
    /// </summary>
    [Mappable(nameof(WfJobId))]
    public class WfJobDto : TenantDtoBase
    {
        /// <summary>
        /// The unique identifier for the workflow job
        /// </summary>
        public long WfJobId { get; set; }
        
        /// <summary>
        /// The name of the workflow job
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
        /// Optional
        /// </summary>
        public int? ScheduleId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// Optional - a job may not have an associated workflow
        /// </summary>
        public int? WorkflowId { get; set; }
        
        /// <summary>
        /// The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.
        /// </summary>
        public int? WorkflowConditionId { get; set; }
        
        /// <summary>
        /// When this was scheduled to run
        /// </summary>
        public DateTimeOffset? ScheduledAt { get; set; }
        
        /// <summary>
        /// Indicates whether this job is scheduled
        /// </summary>
        public bool IsScheduled { get; set; }
        
        /// <summary>
        /// 1 - Scheduled,
        /// 2 - Delayed
        /// 3 - Check Conditions
        /// 4 - Executing
        /// 5 - Waiting
        /// 6 - Completed
        /// 7 - Cancelled
        /// 8 - Error
        /// </summary>
        public byte WfJobStatusId { get; set; }
        
        /// <summary>
        /// Schedule purpose allows us to clearly identify schedules for different purposes.
        /// For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery
        /// </summary>
        public short SchedulePurposeId { get; set; }
        
        /// <summary>
        /// Optionally identifies a PartyId that owns this job. Could be an individual, group, team, or organisation.
        /// </summary>
        public Guid? OwningPartyId { get; set; }
        
        /// <summary>
        /// The datetime when the job completed
        /// </summary>
        public DateTimeOffset? CompletedAt { get; set; }
        
        /// <summary>
        /// if a schedule has dependancies such as data having arrived then this is the next time to check the dependancy is meet
        /// </summary>
        public DateTimeOffset? NextCheckDependenciesAt { get; set; }
        
        /// <summary>
        /// if waiting on dependancies after this time then auto Cancel job.
        /// </summary>
        public DateTimeOffset? TimeoutDependencyWaitAt { get; set; }
        
        /// <summary>
        /// Identifies the current step within a workflow.
        /// </summary>
        public int? WorkflowStepId { get; set; }
        
        /// <summary>
        /// The reason why the job was cancelled (if applicable)
        /// </summary>
        public byte WfJobCancelledReasonId { get; set; }
    }

    /// <summary>
    /// List DTO for WfJob
    /// </summary>
    public class WfJobListDto : WfJobDto
    {
    }
}
