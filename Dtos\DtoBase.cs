﻿using Sql.DtoInterfaces;

namespace Redi.Prime3.Function.BaseLib
{
	public abstract class DtoBase
	{
		public virtual DateTimeOffset? CreatedOn { get; set; }
		public virtual string? CreatedByName { get; set; }
		public virtual Guid? CreatedById { get; set; }
		public virtual DateTimeOffset? ModifiedOn { get; set; }
		public virtual string? ModifiedByName { get; set; }
		public virtual Guid? ModifiedById { get; set; }
		public virtual bool Deleted { get; set; }
	}
}
