namespace RediAzurefunctionBase.Enums
{
    /// <summary>
    /// Enum for WorkflowStepType values
    /// </summary>
    public enum WorkflowStepTypeEnum
    {
        /// <summary>
        /// A message will be placed into the steps QueueName in order to execute the step.
        /// The function associated with the queue must be workflow enabled
        /// </summary>
        ExecuteViaQueue = 1,

        /// <summary>
        /// An event will be sent to the Notification Event Queue in order to trigger any required
        /// notification processing - email/sms/app, etc
        /// </summary>
        NotificationEvent = 2,

        /// <summary>
        /// Evaluate conditions in order to determine next path to take - first true condition is the path taken
        /// </summary>
        Condition = 3,

        /// <summary>
        /// Pause processing until one or more named dependencies are met.
        /// This could be input from an external system, end user input, etc
        /// </summary>
        WaitForDependencies = 4,

        /// <summary>
        /// Branch down multiple paths. If child path has a condition it will only be executed if it evaluates to true.
        /// All child paths with no condition will always be executed.
        /// Each path will result in a SUB WorkflowJob been created.
        /// </summary>
        Branch = 5
    }
}