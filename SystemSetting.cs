﻿
using Microsoft.Data.SqlClient;

namespace Redi.Prime3.MicroService.BaseLib
{
    public class SystemSetting
    {
        public SystemSetting()
        {
        }

        /// <summary>
        /// Get SystemSetting records from common.SystemSetting table
        /// </summary>
        /// <param name="connectionString">Database Connection String</param>
        /// <param name="systemSettingCodes">array of system setting codes</param>
        /// <returns>Dictionary of system settings (Key, value)</returns>
        public static async Task<Dictionary<string, string?>> GetSystemSettingsAsync(string connectionString, string[] systemSettingCodes)
        {
            string sql = @"
IF EXISTS (SELECT * FROM sys.tables t join sys.schemas s ON (t.schema_id = s.schema_id) WHERE s.name='common' and t.name='SystemSetting')
SELECT [SystemSettingCode]
      ,[Value]
      ,[Description]
  FROM [common].[SystemSetting]
WHERE [SystemSettingCode] IN (@codes)";
            var resultset = new Dictionary<string, string?>();
            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(sql, connection))
                {
                    var parameterNames = new List<string>();
                    for (int i = 0; i < systemSettingCodes.Count(); i++)
                    {
                        var paramName = $"@codes{i}";
                        command.Parameters.AddWithValue(paramName, systemSettingCodes.ElementAt(i));
                        parameterNames.Add(paramName);
                    }

                    command.CommandText = command.CommandText.Replace($"@codes", string.Join(",", parameterNames));

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var key = reader["SystemSettingCode"].ToString();
                            var val = reader["Value"].ToString();
                            if (key != null)
                            {
                                resultset.Add(key, val);
                            }
                        }
                    }
                }
            }
            return resultset;
        }
    }
}
