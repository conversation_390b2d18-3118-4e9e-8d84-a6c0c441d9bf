using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class ReportAttribute : BusinessLogicBase
    {
        public ReportAttribute(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single ReportAttribute by ID
        /// </summary>
        /// <param name="reportAttributeId">The report attribute ID</param>
        /// <returns>ReportAttributeDto or null if not found</returns>
        internal async Task<ReportAttributeDto?> GetAsync(long reportAttributeId)
        {
            string sql = @"
                SELECT [ReportAttributeId]
                      ,[ReportId]
                      ,[AttributeCode]
                      ,[ValueString]
                      ,[ValueStringMax]
                      ,[ValueNumeric]
                      ,[ValueDateTime]
                FROM [workflow].[ReportAttribute]
                WHERE [ReportAttributeId] = @reportAttributeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("reportAttributeId", reportAttributeId);
                return await command.SelectSingle<ReportAttributeDto>();
            }
        }

        /// <summary>
        /// Get a list of ReportAttributes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="reportId">Filter by report ID</param>
        /// <param name="attributeCode">Filter by attribute code</param>
        /// <returns>List of ReportAttributes</returns>
        internal async Task<ListResponseDto<ReportAttributeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, int? reportId = null, string? attributeCode = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ReportAttributeId", "[ReportAttribute].[ReportAttributeId]" },
                { "ReportId", "[ReportAttribute].[ReportId]" },
                { "AttributeCode", "[ReportAttribute].[AttributeCode]" },
                { "ValueString", "[ReportAttribute].[ValueString]" },
                { "ValueNumeric", "[ReportAttribute].[ValueNumeric]" },
                { "ValueDateTime", "[ReportAttribute].[ValueDateTime]" }
            };

            string sql = @"
                SELECT [ReportAttributeId]
                      ,[ReportId]
                      ,[AttributeCode]
                      ,[ValueString]
                      ,[ValueStringMax]
                      ,[ValueNumeric]
                      ,[ValueDateTime]
                FROM [workflow].[ReportAttribute]
                WHERE 1=1";

            if (reportId.HasValue)
            {
                sql += " AND [ReportId] = @reportId";
            }

            if (!string.IsNullOrWhiteSpace(attributeCode))
            {
                sql += " AND [AttributeCode] = @attributeCode";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ReportAttributeId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ReportAttributeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (reportId.HasValue)
                {
                    command.AddArgument("reportId", reportId.Value);
                }
                if (!string.IsNullOrWhiteSpace(attributeCode))
                {
                    command.AddArgument("attributeCode", attributeCode);
                }
                result.List = await command.SelectMany<ReportAttributeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[ReportAttribute]
                WHERE 1=1";

            if (reportId.HasValue)
            {
                countSql += " AND [ReportId] = @reportId";
            }

            if (!string.IsNullOrWhiteSpace(attributeCode))
            {
                countSql += " AND [AttributeCode] = @attributeCode";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (reportId.HasValue)
                {
                    command.AddArgument("reportId", reportId.Value);
                }
                if (!string.IsNullOrWhiteSpace(attributeCode))
                {
                    command.AddArgument("attributeCode", attributeCode);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
