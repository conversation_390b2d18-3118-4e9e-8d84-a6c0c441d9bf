using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Azure Blob Triggered Functions.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class Prime3FunctionBlobBase : Prime3FunctionBase
	{
        protected string? _containerName;   
       
        /// <summary>
        /// Prime3 Function standard pre processing of blob.<para/>
        /// 1) log blob and container names
        /// </summary>
        /// <param name="triggeredFileStream"></param>
        /// <param name="name">the blobs name</param>
        /// <param name="context">function context</param>
        /// <returns></returns>
        public async Task PreProcess(Stream triggeredFileStream, string name, FunctionContext context)
        {
            // Get the Container Name (where the blob lives)
            if (context.BindingContext.BindingData.TryGetValue("BlobTrigger", out var blobTrigger) && blobTrigger != null)
            {
                var blobPath = blobTrigger.ToString();
                _containerName = blobPath?.Split('/')[0];
            }

            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Triggered by blob: {name}. Container: {_containerName}");
        }
    
        /// <summary>
        /// Read blob file stream into a List of type T.<para/>
        /// Returns a List typeparamref name="T"
        /// </summary>
        /// <typeparam name="T">Data Type records from blob will be converted to</typeparam>
        /// <param name="fileStream">input file stream</param>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<(List<T>, int)> GetBlobDataAsList<T>(Stream fileStream)
        {
            int recordsReadCount = 0;
            List<T> records = new List<T>();
            using var reader = new StreamReader(fileStream);

            while (!reader.EndOfStream)
            {
                var dataRecord = await reader.ReadLineAsync();
                if (dataRecord != null)
                {
                    if (typeof(T) is object)
                    {
                        var obj = JsonConvert.DeserializeObject<T>(dataRecord);
                        if (obj != null ) { records.Add(obj); }
                    }
                    else
                    {
                        var val = (T)Convert.ChangeType(dataRecord, typeof(T));
                        if (val != null ) { records.Add(val); }
                    }
                    recordsReadCount++;
                }
            }
            return (records, recordsReadCount);
        }

        /// <summary>
        /// <para>Do Blob Post processing.</para>
        /// <para>1) Set the blobPostProcessingMode value to control if blob is deleted, archived, copied, or left in place (none)</para>
        /// <para>2) Log blob stats</para>
        /// <para>Note! if moving, deleting or copying the blob fails an error will be written to the logs, but an Exception will not occur.</para>
        /// </summary>
        /// <param name="blobPostProcessingMode">Controls what is done with the blob at completion of post processing</param>
        /// <param name="recordsReadCount">Number of records read from blob</param>
        /// <param name="name">Blob name</param>
        /// <param name="context">function context</param>
        /// <param name="archiveToContainerName">Optional - archive to container name</param>
        /// <param name="copyToContainerName">Optional - copy to cotainer name</param>
        /// <returns></returns>
        public async Task PostProcess(BlobPostProcessingMode blobPostProcessingMode,
            int recordsReadCount, 
            string name, 
            FunctionContext context,
            string? archiveToContainerName = null,
            string? copyToContainerName = null
            )
        {
            if (!context.BindingContext.BindingData.ContainsKey("Uri")) { return; }
            var fullFileUri = context.BindingContext.BindingData["Uri"]?.ToString();
            if (string.IsNullOrEmpty(fullFileUri)) { return; }

            if (blobPostProcessingMode == BlobPostProcessingMode.Archive && string.IsNullOrEmpty(archiveToContainerName)) {throw new Exception ("archiveToContainerName is required for BlobPostProcessingMode.Archive");}
            if (blobPostProcessingMode == BlobPostProcessingMode.Copy && string.IsNullOrEmpty(copyToContainerName)) {throw new Exception ("copyToContainerName is required for BlobPostProcessingMode.Copy");}
            if (blobPostProcessingMode == BlobPostProcessingMode.CopyAndArchive && string.IsNullOrEmpty(archiveToContainerName)) {throw new Exception ("archiveToContainerName is required for BlobPostProcessingMode.CopyAndArchive");}
            if (blobPostProcessingMode == BlobPostProcessingMode.CopyAndArchive && string.IsNullOrEmpty(copyToContainerName)) {throw new Exception ("copyToContainerName is required for BlobPostProcessingMode.CopyAndArchive");}

            fullFileUri = fullFileUri.Replace("\"", string.Empty);

            try
            {
                // Do processing based on the Blob Post Processing Mode passed in
                switch (blobPostProcessingMode)
                {
                    case BlobPostProcessingMode.None:
                        break;
                    case BlobPostProcessingMode.Delete:
                        await _defaultAzureBlob.DeleteBlob(fullFileUri, _containerName);
                        break;
                    case BlobPostProcessingMode.Copy:
                        await _defaultAzureBlob.CopyBlobToAnotherContainer(fullFileUri, _containerName, copyToContainerName);
                        break;
                    case BlobPostProcessingMode.Archive:
                        await _defaultAzureBlob.MoveBlob(fullFileUri, _containerName, archiveToContainerName);
                        break;
                    case BlobPostProcessingMode.CopyAndArchive:
                        await _defaultAzureBlob.CopyBlobToAnotherContainer(fullFileUri, _containerName, copyToContainerName);
                        await _defaultAzureBlob.MoveBlob(fullFileUri, _containerName, archiveToContainerName);
                        break;
                    default:
                        throw new Exception($"Unsupported BlobPostProcessingMode {blobPostProcessingMode.ToString("G")}");

                }
            }
            catch (Exception ex) 
            {
                // Ignore any issues moving files.
                _logger.LogError(ex, $"{context.FunctionDefinition.Name} Function blob: {name} '{blobPostProcessingMode.ToString("G")}' Failed. uri:{fullFileUri}, container:{_containerName}");
            }

            _logger.LogInformation($"{context.FunctionDefinition.Name} Function Processing Complete for blob: {name}. Records Read: {recordsReadCount}");
        }

    }
}
