using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// Determines if a Schedule is of type Simple or Workflow
    /// </summary>
    [Mappable(nameof(ScheduleTypeId))]
    public class ScheduleTypeDto
    {
        /// <summary>
        /// Determines if a Schedule is of type 1 - Simple or 2 - Workflow
        /// </summary>
        public short ScheduleTypeId { get; set; }
        
        /// <summary>
        /// The display label for the schedule type
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this schedule type is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for ScheduleType
    /// </summary>
    public class ScheduleTypeListDto : ScheduleTypeDto
    {
    }
}
