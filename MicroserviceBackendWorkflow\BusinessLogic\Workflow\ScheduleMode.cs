using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class ScheduleMode : BusinessLogicBase
    {
        public ScheduleMode(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single ScheduleMode by ID
        /// </summary>
        /// <param name="scheduleModeId">The schedule mode ID</param>
        /// <returns>ScheduleModeDto or null if not found</returns>
        internal async Task<ScheduleModeDto?> GetAsync(short scheduleModeId, bool ignoreErrorIfNotExists = false)
        {
            string sql = @"
                SELECT [ScheduleModeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ScheduleMode]
                WHERE [ScheduleModeId] = @scheduleModeId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleModeId", scheduleModeId);
                var result = await command.SelectSingle<ScheduleModeDto>();

                if (result == null && !ignoreErrorIfNotExists)
                {
                    throw new HttpRequestException($"ScheduleMode with ID '{scheduleModeId}' not found", null, System.Net.HttpStatusCode.NotFound);
                }

                return result;
            }
        }

        /// <summary>
        /// Get a list of ScheduleModes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="showDisabled">Include disabled records</param>
        /// <returns>List of ScheduleModes</returns>
        internal async Task<ListResponseDto<ScheduleModeListDto>> GetListAsync(StandardListParameters? standardListParameters = null, bool showDisabled = false)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ScheduleModeId", "[ScheduleMode].[ScheduleModeId]" },
                { "Label", "[ScheduleMode].[Label]" },
                { "IsEnabled", "[ScheduleMode].[IsEnabled]" }
            };

            string sql = @"
                SELECT [ScheduleModeId]
                      ,[Label]
                      ,[IsEnabled]
                FROM [workflow].[ScheduleMode]
                WHERE 1=1";

            if (!showDisabled)
            {
                sql += " AND [IsEnabled] = 1";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ScheduleModeId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ScheduleModeListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                result.List = await command.SelectMany<ScheduleModeListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[ScheduleMode]
                WHERE 1=1";

            if (!showDisabled)
            {
                countSql += " AND [IsEnabled] = 1";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
