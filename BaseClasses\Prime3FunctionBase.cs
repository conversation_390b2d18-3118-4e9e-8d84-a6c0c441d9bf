using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Azure Functions.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class Prime3FunctionBase
	{
        #pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        protected IUnitOfWork _unitOfWork;
        protected ILogger _logger;
        protected DefaultAzureBlob _defaultAzureBlob;
        protected DefaultAzureQueue _defaultAzureQueue;
        #pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

    }
}
