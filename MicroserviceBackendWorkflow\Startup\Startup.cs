﻿using MicroserviceBackendWorkflow.BusinessLogic;
using Redi.Prime3.MicroService.Logger;

namespace MicroserviceBackendWorkflow.Startup
{
    public partial class Startup
    {
        IWebHostEnvironment environmentRoot { get; }
        ILoggerProvider loggerProviderRoot { get; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="environment"></param>
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        {
            environmentRoot = environment;
            loggerProviderRoot = new AppLoggerProvider(configuration);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void Configure(WebApplication app, IWebHostEnvironment env)
        {

        }
    }
}
