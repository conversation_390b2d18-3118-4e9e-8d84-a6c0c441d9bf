using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Redi.Prime3.Function.BaseLib;
using RediAzurefunctionBase.BusinessLogic;
using Sql;

namespace RediAzurefunctionBase.Functions
{
    public class BlobFunctionBase : Prime3FunctionBlobBase
    {
        private BlobProcessor1 _blobProcessor1;
        public BlobFunctionBase(IUnitOfWork unitOfWork
            , ILogger logger
            ,BlobProcessor1 blobProcessor1
            ,DefaultAzureQueue defaultAzureQueue
            ,DefaultAzureBlob defaultAzureBlob
            )
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _blobProcessor1 = blobProcessor1;
            _defaultAzureQueue = defaultAzureQueue;
            _defaultAzureBlob = defaultAzureBlob;
        }

        [Function(nameof(BlobFunctionBase))]
        public async Task Run([BlobTrigger("livedatafeed/CsvFiles/{name}", Connection = "StorageConnectionString")] Stream triggeredFileStream, string name, FunctionContext context)
        {
            // 1) Performs Standard Prime3 Pre Processing of a Blob (log)
            await PreProcess(triggeredFileStream, name, context);

            // 2) Read the Blob File (dataList will contain a list record for each record within the blob file).
            (var dataList, int recordsReadCount) = await GetBlobDataAsList<string>(triggeredFileStream);

            // 3) Process the Blob Data... Main Processing Here...
            await _blobProcessor1.ProcessBlobData(dataList);

            _unitOfWork.Commit();
            _unitOfWork.CloseConnection();

            // 3.1) Insert to queue to trigger subsequent processing.... DELETE IF NOT REQUIRED
            await _defaultAzureQueue.WriteToQueue("outputQueueName", "outputQueueMessageData");

            // 4) Performs standard Prime3 Post Processing of a blob (archive, copy, delete, etc)
            await PostProcess(BlobPostProcessingMode.Archive, recordsReadCount, name, context, archiveToContainerName: "archive", copyToContainerName: "");
        }
    }
}

