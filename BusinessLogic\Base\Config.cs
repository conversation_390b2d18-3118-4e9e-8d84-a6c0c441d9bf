using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic
{
    /// <summary>
    /// Common Configurations that are used across Workflow Coordinator Library.
    /// These may be pulled from SystemSettings table, or Secure Vault.
    /// </summary>
    public static class Config
    {
        private static IConfiguration? _config;
        public static IConfiguration IConfig { get { return _config!; } }

        /// <summary>
        /// Database Connection String
        /// </summary>
        public static string? DatabaseConnectionString => GetConfigValue("DatabaseConnectionString");

        /// <summary>
        /// Azure Storage Account Name
        /// </summary>
        public static string? AzureStorageAccountName => GetConfigValue("AzureStorageAccountName");

        /// <summary>
        /// Azure Storage Connection String
        /// </summary>
        public static string? AzureStorageConnectionString => GetConfigValue("AzureStorageConnectionString");

        /// <summary>
        /// Azure Storage Notification Account Name
        /// </summary>
        public static string? AzureStorageNotificationAccountName => GetConfigValue("AzureStorageNotificationAccountName");

        /// <summary>
        /// Azure Storage Notification Connection String
        /// </summary>
        public static string? AzureStorageNotificationConnectionString => GetConfigValue("AzureStorageNotificationConnectionString");

        /// <summary>
        /// Key Vault Name
        /// </summary>
        public static string? KeyVaultName => GetConfigValue("KeyVaultName");

        /// <summary>
        /// Application Insights Connection String
        /// </summary>
        public static string? ApplicationInsightsConnectionString => GetConfigValue("ApplicationInsightsConnectionString");

        /// <summary>
        /// Sentry DSN
        /// </summary>
        public static string? SentryDsn => GetConfigValue("SentryDsn");

        /// <summary>
        /// Environment Name
        /// </summary>
        public static string? EnvironmentName => GetConfigValue("ASPNETCORE_ENVIRONMENT", defaultTo: "Production");

        /// <summary>
        /// Indicates if running locally
        /// </summary>
        public static bool IsRunningLocally => EnvironmentName?.ToLower() == "development";

        /// <summary>
        /// Azure Container for file storage
        /// </summary>
        public static string AzureContainer => GetConfigValue("AzureContainer", defaultTo: "default");

        /// <summary>
        /// Azure Files Base URL
        /// </summary>
        public static string AzureFilesBase => GetConfigValue("AzureFilesBase", defaultTo: "https://rediteststorage.blob.core.windows.net/");

        public static void Initialise(IConfiguration config)
        {
            _config = config;
        }

        public static bool GetConfigBoolValue(string key, bool isRequired = false, bool defaultTo = false, bool toLower = false)
        {
            string? result = GetConfigValue(key, isRequired, defaultTo.ToString(), toLower);
            bool value;
            if (Boolean.TryParse(result, out value))
            {
                return value;
            }
            return defaultTo;
        }

        public static string? GetConfigValue(string key, bool isRequired = false, string? defaultTo = "", bool toLower = false)
        {
            if (_config == null) { throw new Exception("Initialise must be called first."); }

            if (_config.GetChildren().Any(x => x.Key.ToLower() == key.ToLower()))
            {
                var val = _config[key];
                if (toLower && val != null)
                {
                    return val.ToLower();
                }
                return val;
            }
            else if (isRequired)
            {
                throw new ApplicationException($"Required Configuration value is missing '{ key }'.");
            }
            else
            {
                return defaultTo;
            }
        }

        public static int? GetConfigIntValue(string key, bool isRequired = false, int? defaultTo = null)
        {
            string? result = GetConfigValue(key, isRequired, defaultTo.ToString());
            if (int.TryParse(result, out int value))
            {
                return value;
            }
            return defaultTo;
        }

        /// <summary>
        /// Validate Database Connection String
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public static bool ValidateDatabaseConnectionString(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                return false;
            }

            try
            {
                var builder = new SqlConnectionStringBuilder(connectionString);
                return !string.IsNullOrEmpty(builder.DataSource) && !string.IsNullOrEmpty(builder.InitialCatalog);
            }
            catch
            {
                return false;
            }
        }
    }
}
