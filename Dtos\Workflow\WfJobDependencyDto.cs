using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// A row for each dependency a <PERSON><PERSON><PERSON><PERSON> has.
    /// A dependency is met when the DependencyKey matches and the RequiredDependencyDate is <= Completed DependencyDate
    /// </summary>
    [Mappable(nameof(WfJobDependencyId))]
    public class WfJobDependencyDto
    {
        /// <summary>
        /// The unique identifier for the workflow job dependency
        /// </summary>
        public long WfJobDependencyId { get; set; }
        
        /// <summary>
        /// The job the dependency if related to.
        /// </summary>
        public long WfJobId { get; set; }
        
        /// <summary>
        /// The dependency key is used to uniquely identify a dependency. Format:
        /// T{TenantId}{Tech}{Item}
        /// 
        /// TenantId - only included for Tenant Specific items. Otherwise 0
        /// Tech - identifies the applicable technology that is a dependency. eg. adx, sql, api, file
        /// Item - Clearly identifies the item such as a table name.
        /// </summary>
        public string DependencyKey { get; set; } = string.Empty;
        
        /// <summary>
        /// When the row is created the DependencyDate will be set to the required datetime for matching completed dependencies.
        /// </summary>
        public DateTimeOffset RequiredDependencyDate { get; set; }
    }

    /// <summary>
    /// List DTO for WfJobDependency
    /// </summary>
    public class WfJobDependencyListDto : WfJobDependencyDto
    {
    }
}
