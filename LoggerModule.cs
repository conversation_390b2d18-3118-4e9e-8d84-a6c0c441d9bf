using Autofac;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.MicroService.Logger
{
	public class LoggerModule : Module
	{
		IConfiguration configuration;
		ILoggerProvider loggerProviderInstance;
		string? appName;


        public LoggerModule(IConfiguration c, ILoggerProvider l, string appId)
		{
			configuration = c;
			loggerProviderInstance = l;
			appName = appId;

        }

		protected override void Load(ContainerBuilder builder)
		{
#pragma warning disable CS8604 // Possible null reference argument.
            builder.RegisterInstance<AppLogger>(loggerProviderInstance.CreateLogger(appName) as AppLogger).As<ILogger>();
#pragma warning restore CS8604 // Possible null reference argument.
        }

	}
}
