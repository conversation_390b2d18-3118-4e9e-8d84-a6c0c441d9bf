using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using RediAzurefunctionBase.BusinessLogic;

namespace RediAzurefunctionBase.Functions
{
    /// <summary>
    /// Queue-triggered function that processes dependency-met messages
    /// </summary>
    public class FunctionJobDependencyMet
    {
        private readonly ILogger<FunctionJobDependencyMet> _logger;
        private readonly JobDependencyProcessor _jobDependencyProcessor;

        public FunctionJobDependencyMet(ILogger<FunctionJobDependencyMet> logger, JobDependencyProcessor jobDependencyProcessor)
        {
            _logger = logger;
            _jobDependencyProcessor = jobDependencyProcessor;
        }

        /// <summary>
        /// Queue trigger function for dependency-met queue
        /// </summary>
        /// <param name="queueMessage">The queue message containing dependency information</param>
        /// <returns>Task</returns>
        [Function("FunctionJobDependencyMet")]
        public async Task Run([QueueTrigger("dependency-met")] string queueMessage)
        {
            _logger.LogInformation($"Dependency met function triggered with message: {queueMessage}");
            
            try
            {
                // The message should contain the dependency key
                await _jobDependencyProcessor.ResolveDependencyMetAsync(queueMessage);
                
                _logger.LogInformation($"Successfully processed dependency met for: {queueMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing dependency met message: {queueMessage}");
                throw;
            }
        }
    }
}
