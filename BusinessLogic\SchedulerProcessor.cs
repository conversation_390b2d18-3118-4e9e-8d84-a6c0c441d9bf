using Microsoft.Extensions.Logging;
using RediAzurefunctionBase.Dtos;
using RediAzurefunctionBase.Azure;

namespace RediAzurefunctionBase.BusinessLogic
{
    /// <summary>
    /// Processor for scheduling workflow jobs
    /// </summary>
    public class SchedulerProcessor
    {
        private readonly ILogger<SchedulerProcessor> _logger;
        private readonly DefaultAzureQueue _azureQueue;
        private readonly Schedule _schedule;
        private readonly JobDependency _jobDependency;
        private readonly Job _job;

        public SchedulerProcessor(ILogger<SchedulerProcessor> logger, DefaultAzureQueue azureQueue, Schedule schedule, Job job, JobDependency jobDependency)
        {
            _logger = logger;
            _azureQueue = azureQueue;
            _schedule = schedule;
            _job = job;
            _jobDependency = jobDependency;
        }

        /// <summary>
        /// Main execution method for the scheduler
        /// </summary>
        /// <returns>Task</returns>
        public async Task ExecuteAsync()
        {
            _logger.LogInformation("Starting scheduler execution");

            try
            {
                // Get jobs that need to be scheduled
                var jobsToScheduleResult = await _schedule.GetListAsync(
                    isEnabled: true,
                    runAt: DateTimeOffset.UtcNow
                );
                
                if (jobsToScheduleResult.List != null) 
                {
                    foreach (var job in jobsToScheduleResult.List)
                    {
                        await ScheduleJobAsync(job);
                    }
                }

                // Get scheduled jobs that require dependencies checked
                var scheduledJobsResult = await _job.GetScheduledJobsAsync();
                if (scheduledJobsResult.List != null)
                {
                    foreach (var job in scheduledJobsResult.List)
                    {
                        await _jobDependency.ScheduleJobDependencyCheckAsync(job);
                    }
                }

                _logger.LogInformation("Scheduler execution completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during scheduler execution");
                throw;
            }
        }

        /// <summary>
        /// Schedule a job by writing to the schedule-job queue
        /// </summary>
        /// <param name="schedule">The schedule DTO to create a job for</param>
        /// <returns>Task</returns>
        public async Task ScheduleJobAsync(ScheduleListDto schedule)
        {
            _logger.LogDebug($"Scheduling job for schedule: {schedule.ScheduleId}");

            try
            {
                // Create WorkflowQueueDto from ScheduleListDto
                var queueMessageDto = new WorkflowQueueDto
                {
                    ScheduleId = schedule.ScheduleId,
                    WorkflowId = schedule.WorkflowId,
                    TenantId = schedule.TenantId,
                    NotificationEventTypeCode = schedule.NotificationEventTypeCode
                };

                // Write to 'schedule-job' queue with populated QueueMessageDto
                await _azureQueue.WriteToQueueAsync("schedule-job", queueMessageDto);

                _logger.LogInformation($"Successfully scheduled job for schedule {schedule.ScheduleId} to schedule-job queue");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error scheduling job for schedule {schedule.ScheduleId}");
                throw;
            }
        }
    }
}
