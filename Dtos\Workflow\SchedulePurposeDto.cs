using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// Schedule purpose allows us to clearly identify schedules for different purposes
    /// </summary>
    [Mappable(nameof(SchedulePurposeId))]
    public class SchedulePurposeDto
    {
        /// <summary>
        /// Schedule purpose allows us to clearly identify schedules for different purposes
        /// </summary>
        public short SchedulePurposeId { get; set; }
        
        /// <summary>
        /// The display label for the schedule purpose
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this schedule purpose is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for SchedulePurpose
    /// </summary>
    public class SchedulePurposeListDto : SchedulePurposeDto
    {
    }
}
