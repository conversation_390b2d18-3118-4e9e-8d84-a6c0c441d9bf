{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj", "projectName": "<PERSON><PERSON>", "projectPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\Logger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\Logger\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Web Application Projects\\redi-microservices\\redi-azure-function-base\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/api/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Sentry.AspNetCore": {"target": "Package", "version": "[3.36.0, )"}, "System.Data.Common": {"target": "Package", "version": "[4.3.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "autofac": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"NETStandard.Library": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.200\\RuntimeIdentifierGraph.json"}}}}}