using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class ScheduleDependency : BusinessLogicBase
    {
        public ScheduleDependency(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single ScheduleDependency by ID
        /// </summary>
        /// <param name="scheduleDependencyId">The schedule dependency ID</param>
        /// <returns>ScheduleDependencyDto or null if not found</returns>
        internal async Task<ScheduleDependencyDto?> GetAsync(int scheduleDependencyId)
        {
            string sql = @"
                SELECT [ScheduleDependencyId]
                      ,[ScheduleId]
                      ,[DependencyKey]
                      ,[DependencyDateRule]
                      ,[IsDateMustMatch]
                FROM [workflow].[ScheduleDependency]
                WHERE [ScheduleDependencyId] = @scheduleDependencyId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("scheduleDependencyId", scheduleDependencyId);
                return await command.SelectSingle<ScheduleDependencyDto>();
            }
        }

        /// <summary>
        /// Get a list of ScheduleDependencies with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="scheduleId">Filter by schedule ID</param>
        /// <param name="dependencyKey">Filter by dependency key</param>
        /// <returns>List of ScheduleDependencies</returns>
        internal async Task<ListResponseDto<ScheduleDependencyListDto>> GetListAsync(StandardListParameters? standardListParameters = null, int? scheduleId = null, string? dependencyKey = null)
        {
            if (standardListParameters == null)
            {
                standardListParameters = new StandardListParameters();
            }

            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "ScheduleDependencyId", "[ScheduleDependency].[ScheduleDependencyId]" },
                { "ScheduleId", "[ScheduleDependency].[ScheduleId]" },
                { "DependencyKey", "[ScheduleDependency].[DependencyKey]" },
                { "IsDateMustMatch", "[ScheduleDependency].[IsDateMustMatch]" }
            };

            string sql = @"
                SELECT [ScheduleDependencyId]
                      ,[ScheduleId]
                      ,[DependencyKey]
                      ,[DependencyDateRule]
                      ,[IsDateMustMatch]
                FROM [workflow].[ScheduleDependency]
                WHERE 1=1";

            if (scheduleId.HasValue)
            {
                sql += " AND [ScheduleId] = @scheduleId";
            }

            if (!string.IsNullOrWhiteSpace(dependencyKey))
            {
                sql += " AND [DependencyKey] = @dependencyKey";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "ScheduleDependencyId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<ScheduleDependencyListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                if (!string.IsNullOrWhiteSpace(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }
                result.List = await command.SelectMany<ScheduleDependencyListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[ScheduleDependency]
                WHERE 1=1";

            if (scheduleId.HasValue)
            {
                countSql += " AND [ScheduleId] = @scheduleId";
            }

            if (!string.IsNullOrWhiteSpace(dependencyKey))
            {
                countSql += " AND [DependencyKey] = @dependencyKey";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (scheduleId.HasValue)
                {
                    command.AddArgument("scheduleId", scheduleId.Value);
                }
                if (!string.IsNullOrWhiteSpace(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
