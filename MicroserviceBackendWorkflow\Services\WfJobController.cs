using MicroserviceBackendWorkflow.BusinessLogic;
using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Microsoft.AspNetCore.Mvc;
using Sql;
using MicroserviceBackendWorkflow.Policies;
using Microsoft.AspNetCore.Authorization;

namespace MicroserviceBackendWorkflow.Services
{
    /// <summary>
    /// Get and Manage Workflow Job Instances.
    /// </summary>
    [Route("api/WfJob")]
    public class WfJobController : AppController
    {
        private readonly WfJob _wfJob;

        public WfJobController(WfJob wfJob, IUnitOfWork unitOfWork)
        {
            _wfJob = wfJob;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a Single Workflow Job
        /// </summary>
        /// <remarks>
        /// Returns a single Workflow Job record for a given Job ID.
        /// </remarks>
        /// <param name="wfJobId">The ID of a workflow job record</param>
        /// <response code="200">Workflow Job returned, or null if not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("Get")]
        [ProducesResponseType(typeof(WfJobDto), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAsync(long wfJobId)
        {
            var result = await _wfJob.GetAsync(wfJobId);
            return Ok(result);
        }

        /// <summary>
        /// Create a Workflow Job
        /// </summary>
        /// <remarks>
        /// Create a new Workflow Job instance
        /// </remarks>
        /// <param name="dto">A Workflow Job to be created</param>
        /// <response code="200">Workflow Job created</response>
        /// <response code="422">Workflow Job could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Create")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WfJobDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateAsync([FromBody] WfJobDto dto)
        {
            await _wfJob.CreateAsync(dto);
            _unitOfWork.Commit();
            var result = await _wfJob.GetAsync(dto.WfJobId);
            return Ok(result);
        }

        /// <summary>
        /// Update a Workflow Job
        /// </summary>
        /// <remarks>
        /// Update an existing Workflow Job instance
        /// </remarks>
        /// <param name="dto">The Workflow Job data to update</param>
        /// <response code="200">Workflow Job updated</response>
        /// <response code="422">Workflow Job could not be processed</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Update")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(typeof(WfJobDto), 200)]
        [ProducesResponseType(422)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateAsync([FromBody] WfJobDto dto)
        {
            await _wfJob.UpdateAsync(dto);
            _unitOfWork.Commit();
            var result = await _wfJob.GetAsync(dto.WfJobId);
            return Ok(result);
        }

        /// <summary>
        /// Delete a Workflow Job
        /// </summary>
        /// <remarks>
        /// Delete a Workflow Job with the given ID (soft delete).
        /// </remarks>
        /// <param name="wfJobId">The ID of a workflow job record</param>
        /// <response code="200">Workflow Job has been deleted</response>
        /// <response code="404">Workflow Job not found</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpPost]
        [Route("Delete")]
        [Authorize(Policy = PolicyType.GlobalAdminPolicy)]
        [ProducesResponseType(200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteAsync([FromQuery] long wfJobId)
        {
            await _wfJob.DeleteAsync(wfJobId);
            _unitOfWork.Commit();
            return Ok();
        }

        /// <summary>
        /// Get a List of Workflow Jobs
        /// </summary>
        /// <remarks>
        /// Returns a list of Workflow Jobs with filtering and paging options.
        /// </remarks>
        /// <param name="standardListParameters">Determines the limit, offset, sortby</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <param name="scheduleId">Filter by schedule ID</param>
        /// <param name="workflowId">Filter by workflow ID</param>
        /// <param name="wfJobStatusId">Filter by job status ID</param>
        /// <param name="owningPartyId">Filter by owning party ID</param>
        /// <response code="200">List of Workflow Jobs returned</response>
        /// <response code="500">Oops! Can't process this request now</response>
        [HttpGet]
        [Route("List")]
        [ProducesResponseType(typeof(ListResponseDto<WfJobListDto>), 200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetListAsync([FromQuery] StandardListParameters standardListParameters, int? tenantId = null, int? scheduleId = null, int? workflowId = null, byte? wfJobStatusId = null, Guid? owningPartyId = null)
        {
            var result = await _wfJob.GetListAsync(standardListParameters, tenantId, scheduleId, workflowId, wfJobStatusId, owningPartyId);
            return Ok(result);
        }
    }
}
