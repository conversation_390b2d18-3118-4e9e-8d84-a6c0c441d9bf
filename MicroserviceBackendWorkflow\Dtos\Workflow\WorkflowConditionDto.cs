using Sql;

namespace MicroserviceBackendWorkflow.Dtos
{
    /// <summary>
    /// This is a generic Condition table. 
    /// This can be used for determining if a next step within a workflow should be executed. It can also control if a scheduled job meets conditions to be run.
    /// </summary>
    [Mappable(nameof(WorkflowConditionId))]
    public class WorkflowConditionDto : TenantDtoBase
    {
        /// <summary>
        /// The Workflow Condition Id identifies each unique condition that can be applied to a workflow or a schedule.
        /// </summary>
        public int WorkflowConditionId { get; set; }
        
        /// <summary>
        /// The C# expression that will be evaluated to determine if the condition is true
        /// </summary>
        public string Expression { get; set; } = string.Empty;
        
        /// <summary>
        /// Comma separated list of FieldId's that are used in the expression.
        /// </summary>
        public string? FieldIds { get; set; }
        
        /// <summary>
        /// Comma Separated list of VariableId's that are used in the expression.
        /// </summary>
        public string? VariableIds { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowCondition
    /// </summary>
    public class WorkflowConditionListDto : WorkflowConditionDto
    {
    }
}
