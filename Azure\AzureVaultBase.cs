﻿using Azure.Security.KeyVault.Secrets;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for reading/writing to Azure Vault
    /// Create a class extending AzureVaultBase to use the vault
    /// </summary>
    public abstract class AzureVaultBase : BusinessLogicBase
    {
        protected SecretClient? _secretClient;

        /// <summary>
        /// Get a secret from the Azure Key Vault
        /// </summary>
        /// <param name="secretId">Key Vault Record to read</param>
        /// <param name="ignoreNotFound">Default false. When true a not found record will return a null result instead of thowing an error</param>
        /// <returns>KeyVaultDto</returns>
        /// <exception cref="Exception"></exception>
        public async Task<KeyVaultDto?> GetSecret(string secretId, bool ignoreNotFound = false)
        {
            if (secretId == string.Empty || secretId == null)
            {
                throw new Exception("secretId was not given");
            }
            if (_secretClient == null)
            {
                throw new Exception("_secretClient is null");
            }

            try
            {
                var result = await _secretClient.GetSecretAsync(secretId);

                var keyVault = new KeyVaultDto();
                keyVault.Id = result.Value.Id.ToString();
                keyVault.Name = result.Value.Name;
                keyVault.Value = result.Value.Value;

                return keyVault;
            }
            catch (Azure.RequestFailedException ex)
            {
                //handle key not found here
                if (ex.ErrorCode == "SecretNotFound")
                {
                    if (ignoreNotFound)
                    {
                        return null;
                    }
                    throw new Exception($"No key vault key found for Id {secretId}");
                }
                throw;
            }
            catch (Exception)
            {
                //handle any other error here if needed
                throw;
            }

        }

        /// <summary>
        /// Create or Update Key Vault value
        /// </summary>
        /// <param name="secretId"></param>
        /// <param name="value"></param>
        /// <returns>KeyVaultDto</returns>
        /// <exception cref="Exception"></exception>
        public async Task<KeyVaultDto> SetSecret(string secretId, string value)
        {
            if (secretId == string.Empty || secretId == null)
            {
                throw new Exception("secretId was not given");
            }
            if (_secretClient == null)
            {
                throw new Exception("_secretClient is null");
            }

            var result = await _secretClient.SetSecretAsync(secretId, value);
            if (result == null)
            {
                throw new Exception($"Key Vault write failed for Id {secretId}");
            }

            var keyVault = new KeyVaultDto();
            keyVault.Id = result.Value.Id.ToString();
            keyVault.Name = result.Value.Name;
            keyVault.Value = result.Value.Value;

            return keyVault;
        }



        /// <summary>
        /// Test access to read the key vault
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            if (_secretClient == null)
            {
                throw new Exception("_secretClient is null");
            }

            try
            {
                var result = await _secretClient.GetSecretAsync("dummyKeyVaultRec");
            }
            catch (Exception ex)
            {
                throw new StartupException($"Key Vault Connection failed. {ex.Message}. VaultUri:{_secretClient.VaultUri}");
            }
        }
    }
}

