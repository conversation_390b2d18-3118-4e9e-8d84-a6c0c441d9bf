using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

namespace Redi.Prime3.MicroService.BaseLib
{
    /// <summary>
    /// Common Configurations that are used across Micro Services.
    /// These may be pulled from SystemSettings table, or Secure Vault.
    /// </summary>
    public static class ConfigBase
    {
        private static IConfiguration? _config;
        public static IConfiguration IConfig { get { return _config!; } }

        /// <summary>
        /// The Secret Store Name
        /// </summary>
        public static string? SecretStore => GetConfigValue("SECRETS_STORE_NAME");
        /// <summary>
        /// SQL Database Connection String
        /// </summary>
        public static string? DatabaseConnectionString => GetConfigValue("ConnectionStringsBaseDbConn");
        /// <summary>
        /// Azure Storage Connection String
        /// </summary>
        public static string? AzureStorageConnectionString => GetConfigValue("AzureConnectionString");
        /// <summary>
        /// Business Name / Company Name
        /// </summary>
        public static string? BusinessName { get { var _val = GetConfigValue("CommsBusinessName", true); return _val?.Length > 11 ? _val[..11] : _val; } }
        /// <summary>
        /// JWT Encryption Key
        /// </summary>
        public static string? JwtKey => GetConfigValue("AuthJwtKey", defaultTo: "dX&4<73SGL$MR'[dh#wbj8ZQGSh,]xLgZmKrcnPgy>P4[rPruQkmh(2y~#avFkr3&'nhpRdddkc8_QDP");
        /// <summary>
        /// JWT Expiry in Days
        /// </summary>
        public static string? JwtExpireDays => GetConfigValue("AuthJwtExpireDays", defaultTo: "30");
        /// <summary>
        /// JWT Expiry Disabled when true
        /// </summary>
        public static string? JwtExpireDisabled => GetConfigValue("AuthJwtExpireDisabled", defaultTo: "false", toLower: true);
        /// <summary>
        /// JWT Issuer Name
        /// </summary>
        public static string? JwtIssuer => GetConfigValue("AuthJwtIssuer", defaultTo: "https://redisoftware.com.au");
        /// <summary>
        /// The Host Name
        /// </summary>
        public static string? Hostname => GetConfigValue("Hostname", true);
        /// <summary>
        /// Azure Storage Account Name - this is the default storage account
        /// </summary>
        public static string? AzureStorageAccountName => GetConfigValue("AzureStorageAccountName", false, defaultTo: "testredi1storage");
        /// <summary>
        /// Storage account key
        /// </summary>
        public static string? AzureStorageAccountKey => GetConfigValue("AzureStorageAccountKey", defaultTo: "****************************************************************************************");
        /// <summary>
        /// Notifications Azure Storage Account Name (could be the same as default)
        /// </summary>
        public static string? AzureStorageNotificationAccountName => GetConfigValue("AzureStorageNotificationAccountName", false);
        /// <summary>
        /// Deletion Service Storage Account Name
        /// </summary>
        public static string? AzureStorageDeleteAccountServiceAccountName => GetConfigValue("AzureStorageDeleteAccountServiceAccountName", false);

        /// <summary>
        /// The Azure Client Id
        /// Used when supporting Microsoft Login or Azure Storage Queue
        /// </summary>
        public static string? AzureClientId => ConfigBase.GetConfigValue("AzureClientId");
        /// <summary>
        /// The Azure Client Secret
        /// Used when supporting Microsoft Login or Azure Storage Queue
        /// </summary>
        public static string? AzureClientSecret => ConfigBase.GetConfigValue("AzureClientSecret");
        /// <summary>
        /// The Azure Tenant Id.
        /// Used when supporting Microsoft login OR Azure Storage Queue
        /// </summary>
        public static string? AzureTenantId => ConfigBase.GetConfigValue("AzureTenantId");
        /// <summary>
        /// Google Recaptcha Secret Key
        /// </summary>
        public static string? GoogleRecaptchaSecretKey => GetConfigValue("GoogleRecaptchaSecretKey");

        public static void Initialise(IConfiguration config)
        {
            _config = config;
        }

        private static Dictionary<string, string>? _schemas;

        /// <summary>
        /// Dictionary of all Schema's in the Database
        /// </summary>
        public static Dictionary<string, string> Schemas
        {
            get
            {
                if (_schemas == null)
                {
                    _schemas = ConfigBase.GetDatabaseSchemas().Result;
                }
                return _schemas;
            }
        }



        public static bool GetConfigBoolValue(string key, bool isRequired = false, bool defaultTo = false, bool toLower = false)
        {
            string? result = GetConfigValue(key, isRequired, defaultTo.ToString(), toLower);
            bool value;
            if (Boolean.TryParse(result, out value))
            {
                return value;
            }
            return defaultTo;
        }

        public static string? GetConfigValue(string key, bool isRequired = false, string? defaultTo = "", bool toLower = false)
        {
            if (_config == null) { throw new Exception("Initialise must be called first."); }

            if (_config.GetChildren().Any(x => x.Key.ToLower() == key.ToLower()))
            {
                var val = _config[key];
                if (toLower && val != null)
                {
                    return val.ToLower();
                }
                return val;
            }
            else if (isRequired)
            {
                throw new ApplicationException($"Required Configuration value is missing '{ key }'.");
            }
            else
            {
                return defaultTo;
            }
        }

        public static int? GetConfigIntValue(string key, bool isRequired = false, int? defaultTo = null)
        {
            string? result = GetConfigValue(key, isRequired, defaultTo.ToString());
            if (int.TryParse(result, out int value))
            {
                return value;
            }
            return defaultTo;
        }

        public static Guid? GetConfigGuidValue(string key, bool isRequired = false, string? defaultTo = "")
        {
            string? result = GetConfigValue(key, isRequired, defaultTo);
            if (Guid.TryParse(result, out Guid value))
            {
                return value;
            }
            if (!string.IsNullOrEmpty(result))
            {
                throw new Exception($"{key} - invalid Guid value {result}");
            }
            return null;
        }

        public static List<string> GetConfigValueList(string key, bool isRequired = false, List<string>? defaultTo = null, bool toLower = false)
        {
            if (_config == null) { throw new Exception("Initialise must be called first."); }

            var list = new List<string>();
            if (_config.GetChildren().Any(x => x.Key.ToLower() == key.ToLower()))
            {
                var val = _config[key];
                if(!string.IsNullOrEmpty(val))
                { 
                    list = (toLower ? val.ToLower() : val).Split(',').ToList<string>();
                }
                return list;
            }
            else if (isRequired)
            {
                throw new ApplicationException($"Required Configuration value is missing '{key}'.");
            }
            else
            {
                return defaultTo ?? list;
            }
        }

        public static async Task<bool> GetSystemSettingBoolValueAsync(string key, bool isRequired = false, bool defaultTo = false, bool toLower = false)
        {
            string result = await GetSystemSettingValueAsync(key, isRequired, defaultTo.ToString(), toLower);
            bool value;
            if (Boolean.TryParse(result, out value))
            {
                return value;
            }
            return defaultTo;
        }

        public static async Task<string> GetSystemSettingValueAsync(string key, bool isRequired = false, string defaultTo = "", bool toLower = false)
        {
            var result = await GetSystemSettingAsync(key);
            if (!String.IsNullOrEmpty(result)) 
            {
                if (toLower)
                {
                    return result.ToLower();
                }
                return result;
            }

            if (isRequired)
            {
                throw new ApplicationException($"Required Configuration value is missing '{ key }'.");
            }
            return defaultTo;
        }

        public static async Task<string?> GetSystemSettingAsync(string systemSettingCode)
        {
            string sql = @"
SELECT [Value]
  FROM [common].[SystemSetting]
WHERE [SystemSettingCode] = @systemSettingCode";

            using (var connection = new SqlConnection(DatabaseConnectionString))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@systemSettingCode", systemSettingCode);

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            return reader["Value"].ToString();
                        }
                    }
                }

                return null;
            }
        }

        /// <summary>
        /// Returns a dictionary of Database Schemas that are on the connected Database
        /// </summary>
        /// <returns></returns>
        private static async Task<Dictionary<string, string>> GetDatabaseSchemas()
        {
            Dictionary<string, string> schemas = new Dictionary<string, string>();
            string sql = @"
SELECT name FROM sys.schemas";

            using (var connection = new SqlConnection(DatabaseConnectionString))
            {
                await connection.OpenAsync();

                using (var command = new SqlCommand(sql, connection))
                {
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string? name = reader["name"].ToString();
                            if (name != null)
                            {
                                schemas.Add(name, name);
                            }
                        }
                    }
                }

                return schemas;
            }
        }
    }
}