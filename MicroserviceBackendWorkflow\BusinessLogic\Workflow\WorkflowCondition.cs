using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class WorkflowCondition : BusinessLogicBase
    {
        public WorkflowCondition(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single WorkflowCondition by ID
        /// </summary>
        /// <param name="workflowConditionId">The workflow condition ID</param>
        /// <returns>WorkflowConditionDto or null if not found</returns>
        internal async Task<WorkflowConditionDto?> GetAsync(int workflowConditionId)
        {
            string sql = @"
                SELECT [WorkflowConditionId]
                      ,[TenantId]
                      ,[Expression]
                      ,[FieldIds]
                      ,[VariableIds]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[WorkflowCondition]
                WHERE [WorkflowConditionId] = @workflowConditionId
                  AND [Deleted] = 0";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("workflowConditionId", workflowConditionId);
                return await command.SelectSingle<WorkflowConditionDto>();
            }
        }

        /// <summary>
        /// Create a new WorkflowCondition
        /// </summary>
        /// <param name="dto">The WorkflowCondition data</param>
        internal async Task CreateAsync(WorkflowConditionDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Expression))
            {
                throw new HttpRequestException("Expression cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string sql = @"
                INSERT INTO [workflow].[WorkflowCondition]
                ([TenantId], [Expression], [FieldIds], [VariableIds], [CreatedOn], [CreatedByName])
                VALUES
                (@TenantId, @Expression, @FieldIds, @VariableIds, @CreatedOn, @CreatedByName)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing WorkflowCondition
        /// </summary>
        /// <param name="dto">The WorkflowCondition data</param>
        internal async Task UpdateAsync(WorkflowConditionDto dto)
        {
            if (dto.WorkflowConditionId <= 0)
            {
                throw new HttpRequestException("WorkflowConditionId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.Expression))
            {
                throw new HttpRequestException("Expression cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.WorkflowConditionId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowCondition with ID '{dto.WorkflowConditionId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[WorkflowCondition]
                SET [TenantId] = @TenantId,
                    [Expression] = @Expression,
                    [FieldIds] = @FieldIds,
                    [VariableIds] = @VariableIds,
                    [ModifiedOn] = @ModifiedOn,
                    [ModifiedByName] = @ModifiedByName
                WHERE [WorkflowConditionId] = @WorkflowConditionId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                dto.ModifiedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a WorkflowCondition (soft delete)
        /// </summary>
        /// <param name="workflowConditionId">The workflow condition ID</param>
        internal async Task DeleteAsync(int workflowConditionId)
        {
            var exists = await GetAsync(workflowConditionId);
            if (exists == null)
            {
                throw new HttpRequestException($"WorkflowCondition with ID '{workflowConditionId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[WorkflowCondition]
                SET [Deleted] = 1,
                    [ModifiedOn] = @ModifiedOn
                WHERE [WorkflowConditionId] = @workflowConditionId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArgument("workflowConditionId", workflowConditionId);
                command.AddArgument("ModifiedOn", DateTimeOffset.UtcNow);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of WorkflowConditions with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="tenantId">Filter by tenant ID</param>
        /// <returns>List of WorkflowConditions</returns>
        internal async Task<ListResponseDto<WorkflowConditionListDto>> GetListAsync(StandardListParameters standardListParameters, int? tenantId = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "WorkflowConditionId", "[WorkflowCondition].[WorkflowConditionId]" },
                { "TenantId", "[WorkflowCondition].[TenantId]" },
                { "Expression", "[WorkflowCondition].[Expression]" },
                { "CreatedOn", "[WorkflowCondition].[CreatedOn]" },
                { "ModifiedOn", "[WorkflowCondition].[ModifiedOn]" }
            };

            string sql = @"
                SELECT [WorkflowConditionId]
                      ,[TenantId]
                      ,[Expression]
                      ,[FieldIds]
                      ,[VariableIds]
                      ,[CreatedOn]
                      ,[CreatedByName]
                      ,[ModifiedOn]
                      ,[ModifiedByName]
                      ,[Deleted]
                FROM [workflow].[WorkflowCondition]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                sql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "WorkflowConditionId");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<WorkflowConditionListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                result.List = await command.SelectMany<WorkflowConditionListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[WorkflowCondition]
                WHERE [Deleted] = 0";

            if (tenantId.HasValue)
            {
                countSql += " AND ([TenantId] = @tenantId OR [TenantId] IS NULL)";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (tenantId.HasValue)
                {
                    command.AddArgument("tenantId", tenantId.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
