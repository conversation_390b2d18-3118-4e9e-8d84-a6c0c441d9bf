using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow step connector operations
    /// </summary>
    public class WorkflowStepConnector : BusinessLogicBase
    {
        public WorkflowStepConnector(IUnitOfWork unitOfWork, ILogger<WorkflowStepConnector> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single WorkflowStepConnector by ID
        /// </summary>
        /// <param name="workflowStepConnectorId">The workflow step connector ID</param>
        /// <param name="ignoreErrorIfNotExists">If true, returns null when not found instead of throwing an exception</param>
        /// <returns>WorkflowStepConnectorDto or null if not found</returns>
        public async Task<WorkflowStepConnectorDto?> GetAsync(int workflowStepConnectorId, bool ignoreErrorIfNotExists = false)
        {
            _logger.LogDebug($"Getting workflow step connector for ID: {workflowStepConnectorId}");

            try
            {
                // Check cache first
                var cacheKey = $"WorkflowStepConnector_{workflowStepConnectorId}";
                var (exists, cachedConnector) = await _cache.GetCacheItem<WorkflowStepConnectorDto>("WorkflowStepConnector", cacheKey);
                if (exists && cachedConnector != null)
                {
                    return cachedConnector;
                }

                string sql = @"
                    SELECT [WorkflowStepConnectorId]
                          ,[WorkflowId]
                          ,[ParentWorkflowStepId]
                          ,[ChildWorkflowStepId]
                          ,[WorkflowConditionId]
                          ,[SortOrder]
                          ,[IsEnabled]
                          ,[Description]
                    FROM [workflow].[WorkflowStepConnector]
                    WHERE [WorkflowStepConnectorId] = @workflowStepConnectorId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("workflowStepConnectorId", workflowStepConnectorId);
                    var connector = await command.SelectSingle<WorkflowStepConnectorDto>();

                    // Cache the result if found
                    if (connector != null)
                    {
                        await _cache.SetCacheItem("WorkflowStepConnector", cacheKey, connector, cacheForMinutes: 30);
                    }
                    else if (!ignoreErrorIfNotExists)
                    {
                        throw new ArgumentException($"WorkflowStepConnector with ID {workflowStepConnectorId} not found");
                    }

                    return connector;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get workflow step connector for ID: {workflowStepConnectorId}");
                throw;
            }
        }

        /// <summary>
        /// Get list of WorkflowStepConnectors by criteria
        /// </summary>
        /// <param name="workflowId">The workflow ID</param>
        /// <param name="parentWorkflowStepId">The parent workflow step ID (optional)</param>
        /// <param name="childWorkflowStepId">The child workflow step ID (optional)</param>
        /// <returns>List of WorkflowStepConnectorDto</returns>
        public async Task<List<WorkflowStepConnectorDto>> GetListAsync(int workflowId, int? parentWorkflowStepId = null, int? childWorkflowStepId = null)
        {
            _logger.LogDebug($"Getting workflow step connectors for WorkflowId: {workflowId}, ParentStepId: {parentWorkflowStepId}, ChildStepId: {childWorkflowStepId}");

            try
            {
                // Build cache key based on parameters
                var cacheKey = $"WorkflowStepConnectorList_{workflowId}_{parentWorkflowStepId}_{childWorkflowStepId}";
                var (exists, cachedList) = await _cache.GetCacheItem<List<WorkflowStepConnectorDto>>("WorkflowStepConnector", cacheKey);
                if (exists && cachedList != null)
                {
                    return cachedList;
                }

                // Build dynamic SQL based on provided parameters
                var whereConditions = new List<string> { "[WorkflowId] = @workflowId", "[IsEnabled] = 1" };
                
                if (parentWorkflowStepId.HasValue)
                {
                    whereConditions.Add("[ParentWorkflowStepId] = @parentWorkflowStepId");
                }
                
                if (childWorkflowStepId.HasValue)
                {
                    whereConditions.Add("[ChildWorkflowStepId] = @childWorkflowStepId");
                }

                string sql = $@"
                    SELECT [WorkflowStepConnectorId]
                          ,[WorkflowId]
                          ,[ParentWorkflowStepId]
                          ,[ChildWorkflowStepId]
                          ,[WorkflowConditionId]
                          ,[SortOrder]
                          ,[IsEnabled]
                          ,[Description]
                    FROM [workflow].[WorkflowStepConnector]
                    WHERE {string.Join(" AND ", whereConditions)}
                    ORDER BY [SortOrder], [WorkflowStepConnectorId]";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("workflowId", workflowId);
                    
                    if (parentWorkflowStepId.HasValue)
                    {
                        command.AddArgument("parentWorkflowStepId", parentWorkflowStepId.Value);
                    }
                    
                    if (childWorkflowStepId.HasValue)
                    {
                        command.AddArgument("childWorkflowStepId", childWorkflowStepId.Value);
                    }

                    var connectors = await command.SelectMany<WorkflowStepConnectorDto>();

                    // Cache the result
                    await _cache.SetCacheItem("WorkflowStepConnector", cacheKey, connectors, cacheForMinutes: 30);

                    return connectors;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get workflow step connectors for WorkflowId: {workflowId}");
                throw;
            }
        }
    }
}
