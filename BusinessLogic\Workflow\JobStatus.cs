using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.Dtos;
using Redi.Prime3.WorkflowCoordinator.Lib.Enums;
using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic.Workflow
{
    /// <summary>
    /// Business logic for workflow job status operations
    /// Acts as a lookup table similar to WfJobStatus.cs in redi-prime3-ms-workflow-v1
    /// </summary>
    public class JobStatus : BusinessLogicBase
    {
        public JobStatus(IUnitOfWork unitOfWork, ILogger<JobStatus> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Get a single WfJobStatus by ID
        /// </summary>
        /// <param name="wfJobStatusId">The workflow job status ID</param>
        /// <returns>WfJobStatusDto or null if not found</returns>
        public async Task<WfJobStatusDto?> GetAsync(short wfJobStatusId)
        {
            _logger.LogDebug($"Getting workflow job status for ID: {wfJobStatusId}");

            try
            {
                // Check cache first
                var cacheKey = $"WfJobStatus_{wfJobStatusId}";
                var (exists, cachedStatus) = await _cache.GetCacheItem<WfJobStatusDto>("WfJobStatus", cacheKey);
                if (exists && cachedStatus != null)
                {
                    return cachedStatus;
                }

                string sql = @"
                    SELECT [WfJobStatusId]
                          ,[Label]
                          ,[IsEnabled]
                          ,[SortOrder]
                          ,[Description]
                    FROM [workflow].[WfJobStatus]
                    WHERE [WfJobStatusId] = @wfJobStatusId";

                using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
                {
                    command.AddArgument("wfJobStatusId", wfJobStatusId);
                    var status = await command.SelectSingle<WfJobStatusDto>();

                    // Cache the result if found
                    if (status != null)
                    {
                        await _cache.SetCacheItem("WfJobStatus", cacheKey, status, cacheForMinutes: 30);
                    }

                    return status;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get workflow job status for ID: {wfJobStatusId}");
                throw;
            }
        }
    }
}
