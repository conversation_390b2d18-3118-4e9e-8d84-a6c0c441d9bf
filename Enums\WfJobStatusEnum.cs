namespace RediAzurefunctionBase.Enums
{
    /// <summary>
    /// Enum for WfJobStatus values
    /// </summary>
    public enum WfJobStatusEnum
    {
        /// <summary>
        /// Job is scheduled to run
        /// </summary>
        Scheduled = 1,

        /// <summary>
        /// Job execution is delayed
        /// </summary>
        Delayed = 2,

        /// <summary>
        /// Job is checking conditions
        /// </summary>
        CheckConditions = 3,

        /// <summary>
        /// Job is currently executing
        /// </summary>
        Executing = 4,

        /// <summary>
        /// Job is waiting for dependencies
        /// </summary>
        Waiting = 5,

        /// <summary>
        /// Job has completed successfully
        /// </summary>
        Completed = 6,

        /// <summary>
        /// Job has failed
        /// </summary>
        Failed = 7,

        /// <summary>
        /// Job has been cancelled
        /// </summary>
        Cancelled = 8
    }
}
