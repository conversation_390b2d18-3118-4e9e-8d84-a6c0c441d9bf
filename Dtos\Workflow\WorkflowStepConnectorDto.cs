using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// DTO for workflow step connectors that define the flow between workflow steps
    /// </summary>
    [Mappable(nameof(WorkflowStepConnectorId))]
    public class WorkflowStepConnectorDto
    {
        /// <summary>
        /// The unique identifier for the workflow step connector
        /// </summary>
        public int WorkflowStepConnectorId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// </summary>
        public int WorkflowId { get; set; }
        
        /// <summary>
        /// The parent workflow step ID (the step that was completed)
        /// </summary>
        public int ParentWorkflowStepId { get; set; }
        
        /// <summary>
        /// The child workflow step ID (the next step to execute)
        /// </summary>
        public int ChildWorkflowStepId { get; set; }
        
        /// <summary>
        /// Optional condition that must be met for this connector to be used
        /// </summary>
        public int? WorkflowConditionId { get; set; }
        
        /// <summary>
        /// The order in which this connector should be evaluated if multiple connectors exist for the same parent step
        /// </summary>
        public int SortOrder { get; set; }
        
        /// <summary>
        /// Indicates whether this connector is enabled and should be used
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// Optional description of this connector
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// List DTO for WorkflowStepConnector
    /// </summary>
    public class WorkflowStepConnectorListDto : WorkflowStepConnectorDto
    {
    }
}
