using Microsoft.Extensions.Logging;
using Sql;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for Business Logic type classes.
    /// Defines Unit of Work and Memory Cache
    /// </summary>
	public class BusinessLogicBase
	{
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        protected IUnitOfWork _unitOfWork;
        protected ILogger _logger;
        protected Cache _cache;
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
    }
}

namespace Redi.Prime3.Function.BaseLib.Modules
{
	//base class for Services.BusinessLogic.Modules used to add classes to the container
	public class ModulesBase
	{
	}
}