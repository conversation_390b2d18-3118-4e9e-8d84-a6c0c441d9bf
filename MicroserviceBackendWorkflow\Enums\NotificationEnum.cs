﻿namespace MicroserviceBackendWorkflow.Enums
{
    /// <summary>
    /// Notification Events - These can be used to trigger Emails/SMS/Mobile Notifications/etc
    /// </summary>
    public enum NotificationEventCodeEnum
    {
        /// <summary>
        /// A booking has been cancelled
        /// </summary>
        BookingCancelled,
        /// <summary>
        /// A booking has been created with status Booked
        /// </summary>
        BookingBooked,
        /// <summary>
        /// A booking has completed and is closed
        /// This happens after the booking has ended
        /// </summary>
        BookingClosed

    }
}
