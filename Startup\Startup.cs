﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Redi.Prime3.MicroService.Logger;

namespace RediAzurefunctionBase.Startup
{
    public partial class Startup
    {
        ILoggerProvider loggerProviderRoot { get; }
        IConfiguration configurationRoot { get; }

        public Startup(IConfiguration configuration)
        {
            configurationRoot = configuration;
            loggerProviderRoot = new AppLoggerProvider(configuration);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        public void Configure(IHostBuilder app )
        {

        }
    }
}
