﻿using Azure.Storage.Queues;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Write to Queues on the Default Storage account
    /// </summary>
    public class DefaultAzureQueue : AzureQueueBase
    {
        public DefaultAzureQueue(IAzureClientFactory<QueueServiceClient> queueServiceClient, ILogger logger)
        {
            if (string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                throw new Exception("DefaultAzureQueue has not been properly configured for use by this Function. The config setting AzureStorageAccountName must be set");
            }
            _queueServiceClient = queueServiceClient.CreateClient("Default");
            _logger = logger;
        }

        /// <summary>
        /// Write a message to an Azure Storage Queue under the Default Storage Account.
        /// Queue will be created if it does not exist
        /// </summary>
        /// <param name="queueName">Azure Storage Queue Name</param>
        /// <param name="message">Message to write to queue</param>
        /// <returns></returns>
#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task WriteToQueue(string queueName, string message)
        {
            await InsertIntoQueue(message, queueName, autoCreateQueue: true);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage
    }
}
