using Azure.Storage.Queues;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;
using Redi.Prime3.WorkflowCoordinator.Lib.BusinessLogic;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Azure
{
    /// <summary>
    /// Write to Queues on the Default Storage account
    /// </summary>
    public class DefaultAzureQueue : AzureQueueBase
    {
        public DefaultAzureQueue(IAzureClientFactory<QueueServiceClient> queueServiceClient, ILogger<DefaultAzureQueue> logger, UtilityFunctions utilityFunctions, Cache cache)
        {
            if (string.IsNullOrEmpty(Config.AzureStorageAccountName))
            {
                throw new Exception("DefaultAzureQueue has not been properly configured for use by this library. The config setting AzureStorageAccountName must be set");
            }
            _queueServiceClient = queueServiceClient.CreateClient("Default");
            _logger = logger;
            _utilityFunctions = utilityFunctions;
            _cache = cache;
        }

        /// <summary>
        /// Write a message to a specific queue
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <param name="message">The message to write</param>
        /// <param name="timeout">Optional timeout for the message</param>
        /// <param name="delay">Optional delay before the message becomes visible</param>
        /// <param name="autoCreateQueue">Whether to automatically create the queue if it doesn't exist</param>
        /// <returns>Task</returns>
        public async Task WriteToQueue(string queueName, string message, TimeSpan? timeout = null, TimeSpan? delay = null, bool autoCreateQueue = true)
        {
            try
            {
                _logger.LogInformation($"Writing message to queue: {queueName}");
                await InsertIntoQueue(message, queueName, timeout, delay, autoCreateQueue);
                _logger.LogDebug($"Successfully wrote message to queue: {queueName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to write message to queue: {queueName}");
                throw;
            }
        }

        /// <summary>
        /// Write an object as JSON to a specific queue
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <param name="messageObject">The object to serialize and write</param>
        /// <param name="timeout">Optional timeout for the message</param>
        /// <param name="delay">Optional delay before the message becomes visible</param>
        /// <param name="autoCreateQueue">Whether to automatically create the queue if it doesn't exist</param>
        /// <returns>Task</returns>
        public async Task WriteToQueue(string queueName, object messageObject, TimeSpan? timeout = null, TimeSpan? delay = null, bool autoCreateQueue = true)
        {
            try
            {
                _logger.LogInformation($"Writing object message to queue: {queueName}");
                await InsertIntoQueue(messageObject, queueName, timeout, delay);
                _logger.LogDebug($"Successfully wrote object message to queue: {queueName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to write object message to queue: {queueName}");
                throw;
            }
        }

        /// <summary>
        /// Write a workflow job message to a queue
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <param name="wfJobId">The workflow job ID</param>
        /// <param name="workflowStepId">The workflow step ID</param>
        /// <param name="additionalData">Optional additional data</param>
        /// <param name="delay">Optional delay before the message becomes visible</param>
        /// <returns>Task</returns>
        public async Task WriteWorkflowJobMessage(string queueName, long wfJobId, int workflowStepId, object? additionalData = null, TimeSpan? delay = null)
        {
            var workflowMessage = new
            {
                WfJobId = wfJobId,
                WorkflowStepId = workflowStepId,
                AdditionalData = additionalData,
                CreatedAt = DateTimeOffset.UtcNow,
                CreatedBy = _utilityFunctions.UserFullName
            };

            try
            {
                _logger.LogInformation($"Writing workflow job message to queue: {queueName} for job {wfJobId}, step {workflowStepId}");
                await WriteToQueue(queueName, workflowMessage, delay: delay);
                _logger.LogDebug($"Successfully wrote workflow job message to queue: {queueName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to write workflow job message to queue: {queueName}");
                throw;
            }
        }

        /// <summary>
        /// Get queue statistics
        /// </summary>
        /// <param name="queueName">The name of the queue</param>
        /// <returns>Queue statistics including message count</returns>
        public async Task<QueueStatistics> GetQueueStatistics(string queueName)
        {
            try
            {
                var messageCount = await GetQueueMessageCount(queueName);
                var exists = await QueueExists(queueName);

                return new QueueStatistics
                {
                    QueueName = queueName,
                    MessageCount = messageCount,
                    Exists = exists,
                    CheckedAt = DateTimeOffset.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to get statistics for queue: {queueName}");
                throw;
            }
        }
    }

    /// <summary>
    /// Queue statistics information
    /// </summary>
    public class QueueStatistics
    {
        public string QueueName { get; set; } = string.Empty;
        public int MessageCount { get; set; }
        public bool Exists { get; set; }
        public DateTimeOffset CheckedAt { get; set; }
    }
}
