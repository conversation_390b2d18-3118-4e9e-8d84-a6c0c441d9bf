﻿using Sql;

namespace MicroserviceBackendBase.Dtos
{
    [Mappable(nameof(EventId))]
    public class EventDto
    {
        public int? EventId { get; set; }
        public string Description { get; set; }
        public Guid ParentEntityId { get; set; }
        public string ParentEntityType { get; set; }
        public DateTimeOffset CreatedOn { get; set; }
        public string? CreatedByName { get; set; }
    }

    public class EventListCDto : EventDto
    {

    }


}
