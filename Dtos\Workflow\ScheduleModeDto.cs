using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand
    /// </summary>
    [Mappable(nameof(ScheduleModeId))]
    public class ScheduleModeDto
    {
        /// <summary>
        /// Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand
        /// </summary>
        public short ScheduleModeId { get; set; }
        
        /// <summary>
        /// The display label for the schedule mode
        /// </summary>
        public string Label { get; set; } = string.Empty;
        
        /// <summary>
        /// Indicates whether this schedule mode is enabled and available for use
        /// </summary>
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// List DTO for ScheduleMode
    /// </summary>
    public class ScheduleModeListDto : ScheduleModeDto
    {
    }
}
