using Sql;

namespace Redi.Prime3.WorkflowCoordinator.Lib.Dtos
{
    /// <summary>
    /// DTO for workflow queue messages containing workflow execution context
    /// </summary>
    public class WorkflowQueueDto
    {
        /// <summary>
        /// Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
        /// Optional
        /// </summary>
        public int? ScheduleId { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// Optional
        /// </summary>
        public int? WorkflowId { get; set; }
        
        /// <summary>
        /// The unique identifier for the workflow job
        /// Optional
        /// </summary>
        public long? WfJobId { get; set; }
        
        /// <summary>
        /// The workflow step ID
        /// Optional
        /// </summary>
        public int? WfStepId { get; set; }
        
        /// <summary>
        /// The condition ID that must be met for the workflow to proceed
        /// Optional
        /// </summary>
        public int? ConditionId { get; set; }
        
        /// <summary>
        /// The tenant ID
        /// Optional
        /// </summary>
        public int? TenantId { get; set; }
        
        /// <summary>
        /// Comma separated list of queue names to process through sequentially.
        /// For simple sequential processes.
        /// Optional
        /// </summary>
        public string? NextQueueNames { get; set; }
        
        /// <summary>
        /// Identifies the notification event to be optionally triggered
        /// Optional
        /// </summary>
        public string? NotificationEventTypeCode { get; set; }
        
        /// <summary>
        /// Additional metadata as key-value pairs
        /// Optional
        /// </summary>
        public Dictionary<string, object>? MetaData { get; set; }
    }
}
