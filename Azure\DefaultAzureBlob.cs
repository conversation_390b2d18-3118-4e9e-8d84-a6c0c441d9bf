﻿using Azure.Storage.Blobs;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Logging;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Read/Write Azure Blobs to default storage account
    /// </summary>
    public class DefaultAzureBlob : AzureBlobBase
    {
        public DefaultAzureBlob(IAzureClientFactory<BlobServiceClient> blobServiceClient, ILogger logger)
        {
            if (string.IsNullOrEmpty(ConfigBase.AzureStorageAccountName))
            {
                throw new Exception("DefaultAzureBlob has not been properly configured for use by this Function. The config setting AzureStorageAccountName must be set");
            }
            _blobServiceClient = blobServiceClient.CreateClient("Default");
            _logger = logger;
        }
    }
}
