﻿using Azure.Storage.Blobs;
using System.Text;

namespace Redi.Prime3.Function.BaseLib
{
    /// <summary>
    /// Base class for dealing with to Azure Storage Blobs
    /// Create a class extending AzureBlobBase to use the Blob reading and write functions
    /// </summary>
    public abstract class AzureBlobBase : BusinessLogicBase
    {
        protected BlobServiceClient? _blobServiceClient;

        /// <summary>
        /// Write Blob (string) to Azure Storage
        /// </summary>
        /// <param name="blobContents">String to be written as contents of blob</param>
        /// <param name="blobFolderName">Optional - blob folder Name</param>
        /// <param name="blobFileName">Blob file name</param>
        /// <param name="isPublic">Optional defaults to false. When true blog is publicly available</param>
        /// <param name="containerName">Optional Azure Container Name. Defaults to settings AzureContainer</param>
        /// <returns>The stored Blob AbsoluteUri</returns>
        /// <exception cref="Exception"></exception>
#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task<string> WriteBlobString(string blobContents, string blobFileName, bool isPublic = false, string? blobFolderName = null, string? containerName = null)
        {
            byte[] binaryData = Encoding.UTF8.GetBytes(blobContents);
            return await WriteBlobBytes(binaryData, blobFileName, isPublic, blobFolderName, containerName);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage

        /// <summary>
        /// Write Blob (bytes array) to Azure Storage
        /// </summary>
        /// <param name="bytes">The bytes to be written to the blob</param>
        /// <param name="blobFolderName">Optional - blob folder Name</param>
        /// <param name="blobFileName">Blob file name</param>
        /// <param name="isPublic">Optional defaults to false. When true blog is publicly available</param>
        /// <param name="containerName">Optional Azure Container Name. Defaults to settings AzureContainer</param>
        /// <returns>The stored Blob AbsoluteUri</returns>
        /// <exception cref="Exception"></exception>
        public async Task<string> WriteBlobBytes(byte[] bytes, string blobFileName, bool isPublic = false, string? blobFolderName = null, string? containerName = null)
        {
            if (string.IsNullOrEmpty(containerName))
            {
                containerName = ConfigBase.AzureContainer;
            }

            if (blobFileName == string.Empty || blobFileName == null)
            {
                throw new Exception("Blob blobFileName was not given");
            }
            if (_blobServiceClient == null)
            {
                throw new Exception("_blobServiceClient is null");
            }
            if (string.IsNullOrEmpty(containerName))
            {
                throw new Exception("Blob containerName is empty");
            }

            var blobContainerClient = _blobServiceClient.GetBlobContainerClient(containerName);
            await blobContainerClient.CreateIfNotExistsAsync();

            blobContainerClient.SetAccessPolicy(accessType: (isPublic ? Azure.Storage.Blobs.Models.PublicAccessType.Blob : Azure.Storage.Blobs.Models.PublicAccessType.None));
            BlobClient cloudBlockBlob = blobContainerClient.GetBlobClient(string.IsNullOrEmpty(blobFolderName) ? blobFileName : blobFolderName + "/" + blobFileName);

            BinaryData binaryData = new BinaryData(bytes);

            await cloudBlockBlob.UploadAsync(binaryData, true);
            return cloudBlockBlob.Uri.AbsoluteUri;
        }

        /// <summary>
        /// Delete an Azure Blob
        /// </summary>
        /// <param name="fileUri">Full path to the blob</param>
        /// <param name="containerName">Azure Container Name</param>
        /// <returns>True if blob was found and marked for deletion. False if not found</returns>
        /// <exception cref="Exception">_blobServiceClient is null</exception>
        public async Task<bool> DeleteBlob(string fileUri, string? containerName = null)
        {
            if (_blobServiceClient == null)
            {
                throw new Exception("_blobServiceClient is null");
            }
            if (string.IsNullOrEmpty(containerName))
            {
                throw new Exception("Blob containerName is empty");
            }

            var fileUriAsUri = new Uri(fileUri);
            var blobContainerClient = _blobServiceClient.GetBlobContainerClient(containerName);
            var fileAbsoluteSourcePath = fileUriAsUri.AbsolutePath.Split($"/{containerName}/")[1];
            BlobClient blobSourceClient = blobContainerClient.GetBlobClient(fileAbsoluteSourcePath);
            return await blobSourceClient.DeleteIfExistsAsync(Azure.Storage.Blobs.Models.DeleteSnapshotsOption.IncludeSnapshots);
        }

        /// <summary>
        /// Copy blob from one container to another container
        /// </summary>
        /// <param name="sourceFileUri">Full path to the blob</param>
        /// <param name="sourceContainerName">Source Container Name</param>
        /// <param name="destContainerName">Destination Container Name</param>
        /// <param name="deleteAfterCopy">Default: false. When true deletes blob after copy - effectively a move.</param>
        /// <returns></returns>
        /// <exception cref="Exception">_blobServiceClient is null</exception>
        public async Task CopyBlobToAnotherContainer(string sourceFileUri, string? sourceContainerName, string? destContainerName, bool deleteAfterCopy = false)
        {
            if (_blobServiceClient == null)
            {
                throw new Exception("_blobServiceClient is null");
            }
            if (string.IsNullOrEmpty(sourceContainerName))
            {
                throw new Exception("Blob sourceContainerName is empty");
            }
            if (string.IsNullOrEmpty(destContainerName))
            {
                throw new Exception("Blob destContainerName is empty");
            }
            if (sourceContainerName == destContainerName)
            {
                throw new Exception($"The Source and Destination Container names must not be the same. source: {sourceContainerName} dest: {destContainerName} ");
            }

            var sourceFileUriAsUri = new Uri(sourceFileUri);
            var blobContainerClient = _blobServiceClient.GetBlobContainerClient(sourceContainerName);
            var fileAbsoluteSourcePath = sourceFileUriAsUri.AbsolutePath.Split($"/{sourceContainerName}/")[1];
            BlobClient blobSourceClient = blobContainerClient.GetBlobClient(fileAbsoluteSourcePath);

            BlobContainerClient destContainerClient = _blobServiceClient.GetBlobContainerClient(destContainerName);
            await destContainerClient.CreateIfNotExistsAsync();

            var fileAbsoluteDestPath = fileAbsoluteSourcePath;
            BlobClient blobDestClient = destContainerClient.GetBlobClient(fileAbsoluteDestPath);

            await blobDestClient.StartCopyFromUriAsync(blobSourceClient.Uri);
            if (deleteAfterCopy == true) { await blobSourceClient.DeleteIfExistsAsync(); }
        }

        /// <summary>
        /// Copy blob within the same container (with a new blob name) or to a different container.
        /// </summary>
        /// <param name="sourceFileUri">Full path to the blob to be copied</param>
        /// <param name="sourceContainerName">Source Container Name</param>
        /// <param name="destFileUri">Full path to the blob destination name</param>
        /// <param name="destContainerName">Optional. Destination Container Name</param>
        /// <param name="deleteAfterCopy">Default: false. When true deletes blob after copy - effectively a move.</param>
        /// <returns></returns>
        /// <exception cref="Exception">_blobServiceClient is null</exception>
        public async Task CopyBlob(string sourceFileUri, string? sourceContainerName, string destFileUri, string? destContainerName = null, bool deleteAfterCopy = false)
        {
            if (_blobServiceClient == null)
            {
                throw new Exception("_blobServiceClient is null");
            }
            if (string.IsNullOrEmpty(sourceContainerName))
            {
                throw new Exception("CopyBlob sourceContainerName is empty");
            }
            if (string.IsNullOrEmpty(sourceFileUri))
            {
                throw new Exception("CopyBlob sourceFileUri is empty");
            }
            if (string.IsNullOrEmpty(destFileUri))
            {
                throw new Exception("CopyBlob destFileUri is empty");
            }
            if (string.IsNullOrEmpty(destContainerName))
            {
                destContainerName = sourceContainerName;
            }
            if (destContainerName == sourceContainerName && sourceFileUri == destFileUri)
            {
                throw new Exception("CopyBlob source and destination cannot be the same.");
            }

            var sourceFileUriAsUri = new Uri(sourceFileUri);
            var blobContainerClient = _blobServiceClient.GetBlobContainerClient(sourceContainerName);
            var fileAbsoluteSourcePath = sourceFileUriAsUri.AbsolutePath.Split($"/{sourceContainerName}/")[1];
            BlobClient blobSourceClient = blobContainerClient.GetBlobClient(fileAbsoluteSourcePath);

            BlobContainerClient destContainerClient = _blobServiceClient.GetBlobContainerClient(destContainerName);
            await destContainerClient.CreateIfNotExistsAsync();

            var destFileUriAsUri = new Uri(destFileUri);
            var fileAbsoluteDestPath = destFileUriAsUri.AbsolutePath.Split($"/{destContainerName}/")[1];
            BlobClient blobDestClient = destContainerClient.GetBlobClient(fileAbsoluteDestPath);

            await blobDestClient.StartCopyFromUriAsync(blobSourceClient.Uri);
            if (deleteAfterCopy == true) { await blobSourceClient.DeleteIfExistsAsync(); }
        }

        /// <summary>
        /// Move blob from one location to another (Container)
        /// </summary>
        /// <param name="sourceFileUri"></param>
        /// <param name="sourceContainerName"></param>
        /// <param name="destContainerName"></param>
        /// <returns></returns>
#pragma warning disable AsyncFixer01 // Unnecessary async/await usage
        public async Task MoveBlob(string sourceFileUri, string? sourceContainerName, string? destContainerName)
        {
            await CopyBlobToAnotherContainer(sourceFileUri, sourceContainerName, destContainerName, deleteAfterCopy: true);
        }
#pragma warning restore AsyncFixer01 // Unnecessary async/await usage

        /// <summary>
        /// Test the storage account connection by writing test blob.
        /// </summary>
        /// <returns></returns>
        public async Task TestConnection()
        {
            if (_blobServiceClient == null)
            {
                throw new Exception("_blobServiceClient is null");
            }

            try
            {
                var testBlobName = "ztest-def-" + StartupBase.AppId;
                if (testBlobName.Length >63 ) { testBlobName = testBlobName.Substring(0, 63); }
                await WriteBlobString("Test Message", testBlobName, isPublic: true);
            }
            catch (Exception ex)
            {
                throw new StartupException($"Storage Blob Connection failed. {ex.Message}. AccountName:{_blobServiceClient.AccountName}");
            }
        }
    }
}

