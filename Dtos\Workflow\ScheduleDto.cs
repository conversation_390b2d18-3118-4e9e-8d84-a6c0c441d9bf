using Sql;

namespace RediAzurefunctionBase.Dtos
{
    /// <summary>
    /// This is a generic Schedule table that will support schedule reports, as well as other things like processing, tasks, etc. It is responsible for determining WHEN something should happen.
    /// </summary>
    [Mappable(nameof(ScheduleId))]
    public class ScheduleDto : TenantDtoBase
    {
        /// <summary>
        /// Identifies a pre-defined schedule for processing, tasks, reporting, jobs, etc.
        /// </summary>
        public int ScheduleId { get; set; }
        
        /// <summary>
        /// Optional name for a Schedule
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// The workFlow Id. Uniquely identifies a workflow (a series of steps).
        /// Optional - a schedule may be defined with no workflow.
        /// </summary>
        public int? WorkflowId { get; set; }
        
        /// <summary>
        /// Determines if a Schedule is of type 
        /// 1 - Simple (Chained) or 
        /// 2 - Workflow (must have a WorkflowId)
        /// </summary>
        public short ScheduleTypeId { get; set; }
        
        /// <summary>
        /// Optional. If set, this is the condition that must be met for the schedule to be run at its scheduled time. If the condition evaluates to false then the schedule will not run (job will be set to Cancelled).
        /// </summary>
        public int? WorkflowConditionId { get; set; }
        
        /// <summary>
        /// Determines the mode in which a schedule operates: 1 - once, 2 - recurring, 3 - on demand
        /// </summary>
        public short ScheduleModeId { get; set; }
        
        /// <summary>
        /// Schedule purpose allows us to clearly identify schedules for different purposes.
        /// For example a schedule may be a reporting group that is used to pull together many reports into a single report delivery
        /// </summary>
        public short? SchedulePurposeId { get; set; }
        
        /// <summary>
        /// A comma separated list of queue names that will be sequentially processed. Only used if Simple schedule type (1).
        /// After each queue step is executed a message will be written to the next queue (effectively chaining together queue functions).
        /// </summary>
        public string? SimpleChainedQueueNames { get; set; }
        
        /// <summary>
        /// The parent entity identifier (GUID) that this schedule is associated with
        /// </summary>
        public Guid? ParentEntityId { get; set; }
        
        /// <summary>
        /// The parent entity identifier (integer) that this schedule is associated with
        /// </summary>
        public long? ParentEntityIntId { get; set; }
        
        /// <summary>
        /// The type of the parent entity that this schedule is associated with
        /// </summary>
        public string? ParentEntityType { get; set; }
        
        /// <summary>
        /// The next time this schedule must run.
        /// </summary>
        public DateTimeOffset? ScheduledNextRunAt { get; set; }
        
        /// <summary>
        /// Must be true for the schedule to run a new job.
        /// Once a job has started this setting is ignored.
        /// </summary>
        public bool IsEnabled { get; set; }
        
        /// <summary>
        /// Additional notes or comments about the schedule
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// Date and time from when a schedule will first start running.
        /// If empty is valid immediately
        /// </summary>
        public DateTimeOffset? ScheduleStartsOn { get; set; }

        /// <summary>
        /// Date and time when a schedule will end.
        /// If not set, then the schedule is infinite.
        /// </summary>
        public DateTimeOffset? ScheduleEndsOn { get; set; }

        /// <summary>
        /// The Time Zone Iana Identifier.
        /// See https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
        ///
        /// The timezone FromTime and ToTime relate to.
        /// Or the timezone used for scheduling (based on day of week etc)
        /// </summary>
        public string? TimezoneIanaId { get; set; }

        /// <summary>
        /// If set specifies the time of day that a schedule can run at.
        /// If ToTime is also set then the pair specify a window of time during which the schedule can occur.
        /// </summary>
        public TimeSpan? FromTime { get; set; }

        /// <summary>
        /// If FromTime is also set then the pair specify a window of time during which the schedule can occur.
        /// </summary>
        public TimeSpan? ToTime { get; set; }

        /// <summary>
        /// A system generated description of when the schedule is run.
        /// This can be displayed to users.
        /// </summary>
        public string? ScheduledWhenDescription { get; set; }

        /// <summary>
        /// The frequency something repeats or recurs at.
        /// 1 - Daily,
        /// 2 - Weekly,
        /// 3 - Monthly
        /// 4 - Yearly
        /// 5 - Every X Hours
        /// 6 - Every X Minutes
        /// </summary>
        public short? RecurrenceFrequencyId { get; set; }

        /// <summary>
        /// When a Schedule will Recur On.
        /// Works with RecurOnPositionId to support 3rd Tuesday of Month, 1st Weekday of Month, Last Friday of Month, Last Day of Month.
        /// </summary>
        public byte? RecurOnId { get; set; }

        /// <summary>
        /// When a schedule needs to occur on the 1st, 2nd, etc Tuesday (weekday) of a month.
        /// 1 - First, 2 - Second, 3 - Third, 4 - Fourth, 9 - Last
        ///
        /// Works with RecurOnId
        /// </summary>
        public byte? RecurOnPositionId { get; set; }

        /// <summary>
        /// For recurring schedules, specifies the interval (e.g., every X hours, every X minutes)
        /// </summary>
        public short? RecurEveryX { get; set; }

        /// <summary>
        /// For monthly recurrence, specifies the day of the month (1-31)
        /// </summary>
        public byte? RecurOnDayOfMonth { get; set; }

        /// <summary>
        /// For yearly recurrence, specifies the month (1-12)
        /// </summary>
        public byte? RecurOnMonth { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Mondays
        /// </summary>
        public bool IncludeMonday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Tuesdays
        /// </summary>
        public bool IncludeTuesday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Wednesdays
        /// </summary>
        public bool IncludeWednesday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Thursdays
        /// </summary>
        public bool IncludeThursday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Fridays
        /// </summary>
        public bool IncludeFriday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Saturdays
        /// </summary>
        public bool IncludeSaturday { get; set; }

        /// <summary>
        /// Indicates whether the schedule should run on Sundays
        /// </summary>
        public bool IncludeSunday { get; set; }

        /// <summary>
        /// Identifies the notification event to be optionally triggered by the schedule.
        /// </summary>
        public string? NotificationEventTypeCode { get; set; }
    }

    /// <summary>
    /// List DTO for Schedule
    /// </summary>
    public class ScheduleListDto : ScheduleDto
    {
    }
}
