using MicroserviceBackendWorkflow.Dtos;
using Redi.Prime3.MicroService.BaseLib;
using Sql;
using System.Collections.Specialized;

namespace MicroserviceBackendWorkflow.BusinessLogic
{
    public class DependencyComplete : BusinessLogicBase
    {
        public DependencyComplete(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get a single DependencyComplete by ID
        /// </summary>
        /// <param name="dependencyCompleteId">The dependency complete ID</param>
        /// <returns>DependencyCompleteDto or null if not found</returns>
        internal async Task<DependencyCompleteDto?> GetAsync(long dependencyCompleteId)
        {
            string sql = @"
                SELECT [DependencyCompleteId]
                      ,[DependencyKey]
                      ,[DependencyDate]
                      ,[CreatedOn]
                FROM [workflow].[DependencyComplete]
                WHERE [DependencyCompleteId] = @dependencyCompleteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("dependencyCompleteId", dependencyCompleteId);
                return await command.SelectSingle<DependencyCompleteDto>();
            }
        }

        /// <summary>
        /// Create a new DependencyComplete
        /// </summary>
        /// <param name="dto">The DependencyComplete data</param>
        internal async Task CreateAsync(DependencyCompleteDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.DependencyKey))
            {
                throw new HttpRequestException("DependencyKey cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            string sql = @"
                INSERT INTO [workflow].[DependencyComplete]
                ([DependencyKey], [DependencyDate], [CreatedOn])
                VALUES
                (@DependencyKey, @DependencyDate, @CreatedOn)";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Create, sql))
            {
                dto.CreatedOn = DateTimeOffset.UtcNow;
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Update an existing DependencyComplete
        /// </summary>
        /// <param name="dto">The DependencyComplete data</param>
        internal async Task UpdateAsync(DependencyCompleteDto dto)
        {
            if (dto.DependencyCompleteId <= 0)
            {
                throw new HttpRequestException("DependencyCompleteId must be greater than 0", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            if (string.IsNullOrWhiteSpace(dto.DependencyKey))
            {
                throw new HttpRequestException("DependencyKey cannot be null or empty", null, System.Net.HttpStatusCode.UnprocessableEntity);
            }

            var exists = await GetAsync(dto.DependencyCompleteId);
            if (exists == null)
            {
                throw new HttpRequestException($"DependencyComplete with ID '{dto.DependencyCompleteId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                UPDATE [workflow].[DependencyComplete]
                SET [DependencyKey] = @DependencyKey,
                    [DependencyDate] = @DependencyDate
                WHERE [DependencyCompleteId] = @DependencyCompleteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Update, sql))
            {
                command.AddArguments(dto);
                await command.Execute();
            }
        }

        /// <summary>
        /// Delete a DependencyComplete
        /// </summary>
        /// <param name="dependencyCompleteId">The dependency complete ID</param>
        internal async Task DeleteAsync(long dependencyCompleteId)
        {
            var exists = await GetAsync(dependencyCompleteId);
            if (exists == null)
            {
                throw new HttpRequestException($"DependencyComplete with ID '{dependencyCompleteId}' not found", null, System.Net.HttpStatusCode.NotFound);
            }

            string sql = @"
                DELETE FROM [workflow].[DependencyComplete]
                WHERE [DependencyCompleteId] = @dependencyCompleteId";

            using (var command = await _unitOfWork.CmdFactory(CmdType.Delete, sql))
            {
                command.AddArgument("dependencyCompleteId", dependencyCompleteId);
                await command.Execute();
            }
        }

        /// <summary>
        /// Get a list of DependencyCompletes with filtering and paging
        /// </summary>
        /// <param name="standardListParameters">Standard list parameters for paging and sorting</param>
        /// <param name="dependencyKey">Filter by dependency key</param>
        /// <param name="fromDate">Filter by dependency date from</param>
        /// <param name="toDate">Filter by dependency date to</param>
        /// <returns>List of DependencyCompletes</returns>
        internal async Task<ListResponseDto<DependencyCompleteListDto>> GetListAsync(StandardListParameters standardListParameters, string? dependencyKey = null, DateTimeOffset? fromDate = null, DateTimeOffset? toDate = null)
        {
            StringDictionary canBeSortedBy = new StringDictionary()
            {
                { "DependencyCompleteId", "[DependencyComplete].[DependencyCompleteId]" },
                { "DependencyKey", "[DependencyComplete].[DependencyKey]" },
                { "DependencyDate", "[DependencyComplete].[DependencyDate]" },
                { "CreatedOn", "[DependencyComplete].[CreatedOn]" }
            };

            string sql = @"
                SELECT [DependencyCompleteId]
                      ,[DependencyKey]
                      ,[DependencyDate]
                      ,[CreatedOn]
                FROM [workflow].[DependencyComplete]
                WHERE 1=1";

            if (!string.IsNullOrWhiteSpace(dependencyKey))
            {
                sql += " AND [DependencyKey] = @dependencyKey";
            }

            if (fromDate.HasValue)
            {
                sql += " AND [DependencyDate] >= @fromDate";
            }

            if (toDate.HasValue)
            {
                sql += " AND [DependencyDate] <= @toDate";
            }

            sql += standardListParameters.EvaluateSortToSqlOrderBy(canBeSortedBy, "CreatedOn");
            sql += " OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY";

            var result = new ListResponseDto<DependencyCompleteListDto>();
            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, sql))
            {
                command.AddArgument("offset", standardListParameters.Offset);
                command.AddArgument("limit", standardListParameters.Limit);
                if (!string.IsNullOrWhiteSpace(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }
                if (fromDate.HasValue)
                {
                    command.AddArgument("fromDate", fromDate.Value);
                }
                if (toDate.HasValue)
                {
                    command.AddArgument("toDate", toDate.Value);
                }
                result.List = await command.SelectMany<DependencyCompleteListDto>();
            }

            // Get total count
            string countSql = @"
                SELECT COUNT(*) AS totalNumOfRows
                FROM [workflow].[DependencyComplete]
                WHERE 1=1";

            if (!string.IsNullOrWhiteSpace(dependencyKey))
            {
                countSql += " AND [DependencyKey] = @dependencyKey";
            }

            if (fromDate.HasValue)
            {
                countSql += " AND [DependencyDate] >= @fromDate";
            }

            if (toDate.HasValue)
            {
                countSql += " AND [DependencyDate] <= @toDate";
            }

            using (var command = await _unitOfWork.CmdFactory(CmdType.Select, countSql))
            {
                if (!string.IsNullOrWhiteSpace(dependencyKey))
                {
                    command.AddArgument("dependencyKey", dependencyKey);
                }
                if (fromDate.HasValue)
                {
                    command.AddArgument("fromDate", fromDate.Value);
                }
                if (toDate.HasValue)
                {
                    command.AddArgument("toDate", toDate.Value);
                }
                result.TotalNumOfRows = await command.SelectSingle<int>("totalNumOfRows");
            }

            return result;
        }
    }
}
